// Modal Form Diagnostic Script
// Paste this into browser console on https://absolute-escapes-redis.ddev.site/blog/?modaltest=1
// to diagnose any remaining issues

console.log('🔍 MODAL FORM DIAGNOSTIC STARTING...');

// Check if required libraries are loaded
console.log('📚 Library Check:');
console.log('- jQuery:', typeof $ !== 'undefined' ? '✅ Loaded' : '❌ Missing');
console.log('- Fancybox:', typeof $.fancybox !== 'undefined' ? '✅ Loaded' : '❌ Missing');
console.log('- Gravity Forms:', typeof gform !== 'undefined' ? '✅ Loaded' : '❌ Missing');
console.log('- GeoIP Detect:', typeof geoip_detect !== 'undefined' ? '✅ Loaded' : '❌ Missing');

// Check for modal elements
console.log('\n🎭 Modal Elements Check:');
const modalVariants = document.querySelector('.modal-variants');
console.log('- Modal variants container:', modalVariants ? '✅ Found' : '❌ Missing');

if (modalVariants) {
    const modals = modalVariants.querySelectorAll('.form-modal');
    console.log('- Number of modal variants:', modals.length);
    
    modals.forEach((modal, index) => {
        console.log(`  Modal ${index + 1}:`, modal.id);
        const forms = modal.querySelectorAll('form[id^="gform_"]');
        console.log(`    Forms: ${forms.length}`);
        forms.forEach(form => {
            console.log(`      Form ID: ${form.id}`);
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            console.log(`      Checkboxes: ${checkboxes.length}`);
        });
    });
}

// Check for ACF configuration
console.log('\n⚙️ Configuration Check:');
const testModeScript = document.querySelector('script:not([src])');
if (testModeScript && testModeScript.textContent.includes('isTestMode')) {
    console.log('- Test mode script: ✅ Found');
    const isTestMode = testModeScript.textContent.includes('isTestMode = true');
    console.log('- Test mode enabled:', isTestMode ? '✅ Yes' : '❌ No');
} else {
    console.log('- Test mode script: ❌ Missing');
}

// Check session storage
console.log('\n💾 Session Storage Check:');
const modalWasShown = sessionStorage.getItem('modalWasShown');
console.log('- modalWasShown:', modalWasShown || 'Not set');

// Check for any JavaScript errors
console.log('\n🐛 Error Check:');
let errorCount = 0;
const originalError = console.error;
console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
};

// Test modal opening manually
console.log('\n🧪 Manual Modal Test:');
setTimeout(() => {
    if (typeof openModal === 'function') {
        console.log('- openModal function: ✅ Available');
        // Try to detect and open modal
        if (typeof detectModal === 'function') {
            const modal = detectModal();
            if (modal) {
                console.log('- Modal detection: ✅ Success');
                console.log('🚀 Attempting to open modal manually...');
                openModal(modal);
            } else {
                console.log('- Modal detection: ❌ Failed');
            }
        } else {
            console.log('- detectModal function: ❌ Missing');
        }
    } else {
        console.log('- openModal function: ❌ Missing');
    }
}, 2000);

// Monitor for form events
console.log('\n👂 Setting up event monitoring...');
$(document).on('gform_post_render', function(event, formId) {
    console.log('📝 DIAGNOSTIC: gform_post_render fired for form:', formId);
});

$(document).on('gform_pre_submission', function(event, form, ajax) {
    console.log('📤 DIAGNOSTIC: gform_pre_submission for form:', form.id, 'ajax:', ajax);
});

$(document).on('gform_confirmation_loaded', function(event, formId) {
    console.log('✅ DIAGNOSTIC: gform_confirmation_loaded for form:', formId);
});

$(document).on('change', 'input[type="checkbox"]', function() {
    console.log('☑️ DIAGNOSTIC: Checkbox changed:', this.name, 'checked:', this.checked);
});

console.log('🔍 DIAGNOSTIC SETUP COMPLETE - Monitor console for events');
console.log('💡 If modal doesn\'t appear automatically, it will be tested manually in 2 seconds');
