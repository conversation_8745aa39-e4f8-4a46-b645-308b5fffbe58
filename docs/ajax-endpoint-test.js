// AJAX Endpoint Test for Gravity Forms
// Paste this into browser console on https://absolute-escapes-redis.ddev.site/blog/?modaltest=1
// This will test if the AJAX endpoint is reachable and working

console.log('🔍 AJAX ENDPOINT TEST STARTING...');

// First, let's check what AJAX URL WordPress is using
console.log('🌐 WordPress AJAX URL:', typeof ajaxurl !== 'undefined' ? ajaxurl : 'Not defined');

// Check if we can reach the admin-ajax.php endpoint
function testAjaxEndpoint() {
    console.log('🧪 Testing basic AJAX endpoint connectivity...');
    
    const ajaxUrl = '/wp-admin/admin-ajax.php';
    
    // Test 1: Basic connectivity test
    fetch(ajaxUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=heartbeat&_wpnonce=test'
    })
    .then(response => {
        console.log('✅ AJAX endpoint is reachable');
        console.log('📊 Response status:', response.status);
        console.log('📊 Response headers:', [...response.headers.entries()]);
        return response.text();
    })
    .then(data => {
        console.log('📊 Response data:', data.substring(0, 200) + '...');
    })
    .catch(error => {
        console.error('❌ AJAX endpoint test failed:', error);
    });
}

// Test 2: Check if we can find a Gravity Form and test its specific AJAX submission
function testGravityFormAjax() {
    console.log('🧪 Testing Gravity Forms AJAX submission...');
    
    // Wait for modal to open, then test
    setTimeout(() => {
        const modalForm = document.querySelector('.fancybox-content form[id^="gform_"]');
        
        if (!modalForm) {
            console.warn('⚠️ No Gravity Form found in modal - cannot test AJAX submission');
            return;
        }
        
        const formId = modalForm.id.replace('gform_', '');
        console.log('🎯 Testing AJAX submission for form ID:', formId);
        
        // Get form data
        const formData = new FormData(modalForm);
        
        // Add required Gravity Forms AJAX parameters
        formData.append('action', 'gf_submit_' + formId);
        formData.append('gform_ajax', 'form_id=' + formId + '&title=1&description=1&tabindex=1');
        formData.append('is_submit_' + formId, '1');
        formData.append('gform_submit', formId);
        
        console.log('📤 Sending test AJAX request...');
        console.log('📤 Form data entries:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }
        
        fetch('/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('📊 Gravity Forms AJAX Response status:', response.status);
            console.log('📊 Response headers:', [...response.headers.entries()]);
            return response.text();
        })
        .then(data => {
            console.log('📊 Gravity Forms AJAX Response:', data.substring(0, 500) + '...');
            
            // Check if response looks like a Gravity Forms response
            if (data.includes('GFFormDisplay::get_form') || data.includes('gform_confirmation') || data.includes('validation_error')) {
                console.log('✅ Response appears to be from Gravity Forms');
            } else if (data.includes('<!DOCTYPE html>')) {
                console.log('⚠️ Response is HTML page - possible redirect or error page');
            } else {
                console.log('❓ Unknown response format');
            }
        })
        .catch(error => {
            console.error('❌ Gravity Forms AJAX test failed:', error);
        });
        
    }, 3000); // Wait 3 seconds for modal to open
}

// Test 3: Check network connectivity and server response
function testNetworkConnectivity() {
    console.log('🧪 Testing network connectivity...');
    
    // Test if we can reach the site itself
    fetch(window.location.origin + '/wp-json/wp/v2/', {
        method: 'GET'
    })
    .then(response => {
        console.log('✅ WordPress REST API is reachable');
        console.log('📊 REST API status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📊 WordPress info:', {
            name: data.name,
            description: data.description,
            url: data.url
        });
    })
    .catch(error => {
        console.error('❌ WordPress REST API test failed:', error);
    });
}

// Test 4: Check for any JavaScript errors that might be blocking AJAX
function checkForJavaScriptErrors() {
    console.log('🧪 Checking for JavaScript errors...');
    
    let errorCount = 0;
    const originalError = window.onerror;
    
    window.onerror = function(message, source, lineno, colno, error) {
        errorCount++;
        console.error('🐛 JavaScript Error #' + errorCount + ':', {
            message: message,
            source: source,
            line: lineno,
            column: colno,
            error: error
        });
        
        if (originalError) {
            originalError.apply(this, arguments);
        }
    };
    
    setTimeout(() => {
        if (errorCount === 0) {
            console.log('✅ No JavaScript errors detected in the last 5 seconds');
        } else {
            console.warn('⚠️ ' + errorCount + ' JavaScript errors detected - these might be blocking AJAX');
        }
    }, 5000);
}

// Test 5: Monitor actual Gravity Forms AJAX requests
function monitorGravityFormsAjax() {
    console.log('🧪 Setting up Gravity Forms AJAX monitoring...');
    
    // Override XMLHttpRequest to monitor AJAX calls
    const originalXHR = window.XMLHttpRequest;
    const originalFetch = window.fetch;
    
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        
        xhr.open = function(method, url, ...args) {
            if (url.includes('admin-ajax.php') || url.includes('gf_submit')) {
                console.log('📡 XHR Request detected:', method, url);
            }
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        xhr.send = function(data) {
            if (this._url && (this._url.includes('admin-ajax.php') || this._url.includes('gf_submit'))) {
                console.log('📡 XHR Data:', data);
                
                this.addEventListener('load', function() {
                    console.log('📡 XHR Response:', this.status, this.responseText.substring(0, 200) + '...');
                });
                
                this.addEventListener('error', function() {
                    console.error('📡 XHR Error:', this.status, this.statusText);
                });
            }
            return originalSend.apply(this, [data]);
        };
        
        return xhr;
    };
    
    // Also monitor fetch requests
    window.fetch = function(url, options = {}) {
        if (url.includes('admin-ajax.php') || url.includes('gf_submit')) {
            console.log('📡 Fetch Request detected:', url, options);
        }
        return originalFetch.apply(this, [url, options]);
    };
}

// Run all tests
console.log('🚀 Starting comprehensive AJAX endpoint tests...');

testAjaxEndpoint();
testNetworkConnectivity();
checkForJavaScriptErrors();
monitorGravityFormsAjax();

// Test Gravity Forms AJAX after modal opens
console.log('⏳ Gravity Forms AJAX test will run in 3 seconds (waiting for modal)...');
testGravityFormAjax();

console.log('🔍 AJAX ENDPOINT TEST SETUP COMPLETE');
console.log('💡 Monitor console for results over the next 10 seconds');
