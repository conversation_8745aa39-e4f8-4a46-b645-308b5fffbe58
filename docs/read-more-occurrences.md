# "Read More" Occurrences in Absolute Escapes Codebase

This document provides a comprehensive list of where "Read more" text appears in the codebase and database.

## Summary

**Total Occurrences:** 8 instances across 6 files
- Search results: 1 occurrence
- Blog listings: 1 occurrence
- Holiday overview: 1 occurrence (missing text domain)
- Holiday additional days: 1 occurrence
- Holiday related posts: 2 occurrences (desktop + mobile)
- Inspiration block: 2 occurrences (desktop + mobile)

## Theme Files (wp-content/themes/absoluteescapes)

### 1. Search Results Template
**File:** `search.php`
**Line:** 36
**Code:** `<?php _e('Read more', 'absoluteescapes'); ?>`
**Context:** Search results page - link text for individual search result items
**Example URL:** [https://absoluteescapes.com/?s=england](https://absoluteescapes.com/?s=england) (or any search query)
**Visibility:** Appears on each search result item as a link

### 2. Blog Template
**File:** `lib/templates/blog.php`
**Line:** 34
**Code:** `<?php _e('Read more', 'absoluteescapes'); ?>`
**Context:** Blog post listings - link text in excerpt area
**Example URL:** [https://absoluteescapes.com/blog/](https://absoluteescapes.com/blog/) (main blog page)
**Visibility:** Appears inline within blog post excerpts
**Template:** Included in pages that use blog template or blog listing functionality

### 3. Holiday Overview Block
**File:** `lib/blocks/holiday/overview.php`
**Line:** 156
**Code:** `<?php _e('Read more'); ?>`
**Context:** Holiday overview section - trigger for bottom copy expansion (missing text domain)
**Example URL:** Any holiday page (e.g., [https://www.absoluteescapes.com/holidays/west-highland-way/](https://www.absoluteescapes.com/holidays/west-highland-way/))
**Visibility:** Appears as expandable trigger if ACF field `bottom_copy` has content
**Template:** Used in `holiday-builder.php` for holiday post types

### 4. Holiday Additional Days Block
**File:** `lib/blocks/holiday/additional-days.php`
**Line:** 53
**Code:** `<?php _e('Read more', 'absoluteescapes'); ?>`
**Context:** Additional days section - accordion trigger for highlight copy
**Example URL:** Holiday pages with "Additional Days" block (e.g., [https://www.absoluteescapes.com/holidays/west-highland-way/](https://www.absoluteescapes.com/holidays/west-highland-way/))
**Visibility:** Appears in accordion items within additional days section
**Template:** Used in `holiday-builder.php` for holiday post types

### 5. Holiday Related Posts Block
**File:** `lib/blocks/holiday/related-posts.php`
**Lines:** 97, 100
**Code:**
- `<?php _e('Read more', 'absoluteescapes'); ?>` (in excerpt)
- `<?php _e('Read more', 'absoluteescapes'); ?>` (mobile-only link)
**Context:** Related posts section - both inline excerpt links and mobile-specific links
**Example URL:** Holiday pages with related blog posts (e.g., [https://www.absoluteescapes.com/holidays/west-highland-way/](https://www.absoluteescapes.com/holidays/west-highland-way/))
**Visibility:** Appears in related posts section (desktop: inline, mobile: separate link)
**Template:** Used in `holiday-builder.php` for holiday post types

### 6. Inspiration Block
**File:** `lib/blocks/inspiration.php`
**Lines:** 60, 64
**Code:**
- `<?php _e('Read more', 'absoluteescapes'); ?>` (desktop-only, inline)
- `<?php _e('Read more', 'absoluteescapes'); ?>` (mobile-only link)
**Context:** Inspiration posts - both desktop inline and mobile separate links
**Example URL:** Pages with inspiration block via page builder (likely homepage, destination pages)
**Visibility:** Desktop: inline in excerpt, Mobile: separate link with chevron icon
**Template:** Used in `page-builder.php` for pages and posts

## WordPress Excerpt Functionality

### Custom Excerpt Functions
**File:** `lib/functions/general.php`
**Lines:** 114-131
**Functions:**
- `new_excerpt_more()` - Returns '...' instead of default WordPress "[...]"
- `excerpt($limit)` - Custom excerpt function with word limit
- `limit_content($limit)` - Custom content limiting function

**Note:** These functions handle excerpt truncation but don't use "Read more" text directly.

## Translation/Localization

All "Read more" instances in theme files use WordPress translation functions:
- `_e('Read more', 'absoluteescapes')` - Most common pattern
- `_e('Read more')` - One instance missing text domain (overview.php line 156)

**Text Domain:** `absoluteescapes`

## Database Queries to Find All Occurrences

### WordPress Database Tables
To find all "Read more" occurrences in the database, use these SQL queries:

```sql
-- Search in post content
SELECT ID, post_title, post_content 
FROM wp_posts 
WHERE post_content LIKE '%Read more%' 
   OR post_content LIKE '%read more%'
   OR post_content LIKE '%READ MORE%';

-- Search in post meta
SELECT post_id, meta_key, meta_value 
FROM wp_postmeta 
WHERE meta_value LIKE '%Read more%' 
   OR meta_value LIKE '%read more%'
   OR meta_value LIKE '%READ MORE%';

-- Search in options table
SELECT option_name, option_value 
FROM wp_options 
WHERE option_value LIKE '%Read more%' 
   OR option_value LIKE '%read more%'
   OR option_value LIKE '%READ MORE%';

-- Search in ACF fields (stored in postmeta)
SELECT p.post_title, pm.meta_key, pm.meta_value
FROM wp_postmeta pm
JOIN wp_posts p ON pm.post_id = p.ID
WHERE pm.meta_value LIKE '%Read more%'
   OR pm.meta_value LIKE '%read more%'
   OR pm.meta_value LIKE '%READ MORE%';
```

## Plugin Files (Third-party)

### Search Regex Plugin
**Files:** Multiple language files contain "Show %s more" translations
**Context:** Plugin interface for showing additional search results

### WP All Export Plugin
**Files:** Multiple language files contain "read more" references
**Context:** Plugin documentation and CDATA tag information

## CSS Classes and Styling

### Related CSS Classes
**File:** `assets/styles/templates/_search.scss`
**Classes:** `.search-results` styling for search page "Read more" links

**File:** `assets/styles/blocks/_inspiration.scss`
**Classes:** `.inspiration__excerpt` styling for inspiration block excerpts

## Notes

1. **Missing Text Domain:** One instance in `overview.php` line 156 is missing the text domain parameter
2. **Consistency:** Most instances use the proper `absoluteescapes` text domain
3. **Mobile Variations:** Some blocks have separate mobile-only "Read more" links
4. **Icon Usage:** Most "Read more" links include chevron icons (`fa-chevron-down` or `fa-chevron-right`)

## Recommendations

1. Fix the missing text domain in `overview.php` line 156
2. Consider standardizing icon usage across all "Read more" links
3. Ensure all instances are properly translatable for internationalization
