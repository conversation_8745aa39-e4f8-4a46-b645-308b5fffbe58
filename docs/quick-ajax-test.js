// Quick AJAX Test - Run this immediately in console
// Tests basic WordPress AJAX connectivity

console.log('🚀 QUICK AJAX TEST');

// Test 1: Basic admin-ajax.php connectivity
fetch('/wp-admin/admin-ajax.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'action=heartbeat'
})
.then(response => {
    console.log('✅ admin-ajax.php is reachable');
    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    return response.text();
})
.then(data => {
    console.log('Response preview:', data.substring(0, 100));
    
    // Test 2: Try a Gravity Forms specific action
    return fetch('/wp-admin/admin-ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=gf_get_form&form_id=7'
    });
})
.then(response => {
    console.log('📝 Gravity Forms action test - Status:', response.status);
    return response.text();
})
.then(data => {
    console.log('📝 GF Response preview:', data.substring(0, 200));
    
    if (data.includes('gform') || data.includes('GF') || data.includes('gravity')) {
        console.log('✅ Gravity Forms appears to be responding');
    } else if (data.includes('<!DOCTYPE html>')) {
        console.log('⚠️ Got HTML page instead of AJAX response - possible issue');
    } else {
        console.log('❓ Unexpected response format');
    }
})
.catch(error => {
    console.error('❌ AJAX test failed:', error);
});

// Test 3: Check current page for any blocking issues
console.log('🔍 Checking current page environment...');
console.log('- Current URL:', window.location.href);
console.log('- jQuery available:', typeof $ !== 'undefined');
console.log('- ajaxurl defined:', typeof ajaxurl !== 'undefined' ? ajaxurl : 'No');
console.log('- Gravity Forms object:', typeof gform !== 'undefined' ? 'Available' : 'Missing');

// Test 4: Look for any obvious blocking factors
const scripts = Array.from(document.querySelectorAll('script[src]')).map(s => s.src);
const blockers = scripts.filter(src => 
    src.includes('rocket') || 
    src.includes('cache') || 
    src.includes('minify') || 
    src.includes('optimize')
);

if (blockers.length > 0) {
    console.log('⚠️ Potential blocking scripts detected:', blockers);
} else {
    console.log('✅ No obvious blocking scripts detected');
}
