<?php

$wfPHPDeprecatingVersion = '7.2.0'; //When greater than PHP_MINIMUM, will issue a discontinuing warning the first time we check it and find a version less than this (also applies to the other similar constant pairs)
$wfPHPMinimumVersion = '7.0.0'; //The currently supported minimum

$wfOpenSSLDeprecatingVersion = '1.0.1';
$wfOpenSSLMinimumVersion = '1.0.1';

$wfWordPressDeprecatingVersion = '4.9.0';
$wfWordPressMinimumVersion = '4.7.0';

$wfCURLMinimumVersion = '1.0';

//Feature Cutoffs
$wfFeatureWPVersionAuditLog = '6.0';
