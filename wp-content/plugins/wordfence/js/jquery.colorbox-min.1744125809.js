/*!
 Colorbox 1.6.4 (prefixed wf)
 license: MIT
 http://www.jacklmoore.com/colorbox
 */
!function(t,e,i){function n(i,n,o){var h=e.createElement(i);return n&&(h.id=Z+n),o&&(h.style.cssText=o),t(h)}function o(){return i.innerHeight?i.innerHeight:t(i).height()}function h(e,i){i!==Object(i)&&(i={}),this.cache={},this.el=e,this.value=function(e){var n;return void 0===this.cache[e]&&(void 0!==(n=t(this.el).attr("data-cbox-"+e))?this.cache[e]=n:void 0!==i[e]?this.cache[e]=i[e]:void 0!==X[e]&&(this.cache[e]=X[e])),this.cache[e]},this.get=function(e){var i=this.value(e);return t.isFunction(i)?i.call(this.el,this):i}}function r(t){var e=W.length,i=(A+t)%e;return i<0?e+i:i}function a(t,e){return Math.round((/%/.test(t)?("x"===e?E.width():o())/100:1)*parseInt(t,10))}function s(t,e){return t.get("photo")||t.get("photoRegex").test(e)}function l(t,e){return t.get("retinaUrl")&&i.devicePixelRatio>1?e.replace(t.get("photoRegex"),t.get("retinaSuffix")):e}function d(t){"contains"in x[0]&&!x[0].contains(t.target)&&t.target!==v[0]&&(t.stopPropagation(),x.focus())}function c(t){c.str!==t&&(x.add(v).removeClass(c.str).addClass(t),c.str=t)}function g(e){A=0,e&&!1!==e&&"nofollow"!==e?(W=t("."+tt).filter(function(){return new h(this,t.data(this,Y)).get("rel")===e}),-1===(A=W.index(_.el))&&(W=W.add(_.el),A=W.length-1)):W=t(_.el)}function u(i){t(e).trigger(i),at.triggerHandler(i)}function f(i){var o;if(!G){if(o=t(i).data(Y),_=new h(i,o),g(_.get("rel")),!U){U=$=!0,c(_.get("className")),x.css({visibility:"hidden",display:"block",opacity:""}),I=n(st,"LoadedContent","width:0; height:0; overflow:hidden; visibility:hidden"),b.css({width:"",height:""}).append(I),j=T.height()+k.height()+b.outerHeight(!0)-b.height(),D=C.width()+H.width()+b.outerWidth(!0)-b.width(),N=I.outerHeight(!0),z=I.outerWidth(!0);var r=a(_.get("initialWidth"),"x"),s=a(_.get("initialHeight"),"y"),l=_.get("maxWidth"),f=_.get("maxHeight");_.w=Math.max((!1!==l?Math.min(r,a(l,"x")):r)-z-D,0),_.h=Math.max((!1!==f?Math.min(s,a(f,"y")):s)-N-j,0),I.css({width:"",height:_.h}),J.position(),u(et),_.get("onOpen"),O.add(F).hide(),x.focus(),_.get("trapFocus")&&e.addEventListener&&(e.addEventListener("focus",d,!0),at.one(ht,function(){e.removeEventListener("focus",d,!0)})),_.get("returnFocus")&&at.one(ht,function(){t(_.el).focus()})}var p=parseFloat(_.get("opacity"));v.css({opacity:p===p?p:"",cursor:_.get("overlayClose")?"pointer":"",visibility:"visible"}).show(),_.get("closeButton")?B.html(_.get("close")).appendTo(b):B.appendTo("<div/>"),w()}}function p(){x||(V=!1,E=t(i),x=n(st).attr({id:Y,class:!1===t.support.opacity?Z+"IE":"",role:"dialog",tabindex:"-1"}).hide(),v=n(st,"Overlay").hide(),L=t([n(st,"LoadingOverlay")[0],n(st,"LoadingGraphic")[0]]),y=n(st,"Wrapper"),b=n(st,"Content").append(F=n(st,"Title"),R=n(st,"Current"),P=t('<button type="button"/>').attr({id:Z+"Previous"}),K=t('<button type="button"/>').attr({id:Z+"Next"}),S=t('<button type="button"/>').attr({id:Z+"Slideshow"}),L),B=t('<button type="button"/>').attr({id:Z+"Close"}),y.append(n(st).append(n(st,"TopLeft"),T=n(st,"TopCenter"),n(st,"TopRight")),n(st,!1,"clear:left").append(C=n(st,"MiddleLeft"),b,H=n(st,"MiddleRight")),n(st,!1,"clear:left").append(n(st,"BottomLeft"),k=n(st,"BottomCenter"),n(st,"BottomRight"))).find("div div").css({float:"left"}),M=n(st,!1,"position:absolute; width:9999px; visibility:hidden; display:none; max-width:none;"),O=K.add(P).add(R).add(S)),e.body&&!x.parent().length&&t(e.body).append(v,x.append(y,M))}function m(){function i(t){t.which>1||t.shiftKey||t.altKey||t.metaKey||t.ctrlKey||(t.preventDefault(),f(this))}return!!x&&(V||(V=!0,K.click(function(){J.next()}),P.click(function(){J.prev()}),B.click(function(){J.close()}),v.click(function(){_.get("overlayClose")&&J.close()}),t(e).bind("keydown."+Z,function(t){var e=t.keyCode;U&&_.get("escKey")&&27===e&&(t.preventDefault(),J.close()),U&&_.get("arrowKey")&&W[1]&&!t.altKey&&(37===e?(t.preventDefault(),P.click()):39===e&&(t.preventDefault(),K.click()))}),t.isFunction(t.fn.on)?t(e).on("click."+Z,"."+tt,i):t("."+tt).live("click."+Z,i)),!0)}function w(){var e,o,h,r=J.prep,d=++lt;if($=!0,q=!1,u(rt),u(it),_.get("onLoad"),_.h=_.get("height")?a(_.get("height"),"y")-N-j:_.get("innerHeight")&&a(_.get("innerHeight"),"y"),_.w=_.get("width")?a(_.get("width"),"x")-z-D:_.get("innerWidth")&&a(_.get("innerWidth"),"x"),_.mw=_.w,_.mh=_.h,_.get("maxWidth")&&(_.mw=a(_.get("maxWidth"),"x")-z-D,_.mw=_.w&&_.w<_.mw?_.w:_.mw),_.get("maxHeight")&&(_.mh=a(_.get("maxHeight"),"y")-N-j,_.mh=_.h&&_.h<_.mh?_.h:_.mh),e=_.get("href"),Q=setTimeout(function(){L.show()},100),_.get("inline")){var c=t(e).eq(0);h=t("<div>").hide().insertBefore(c),at.one(rt,function(){h.replaceWith(c)}),r(c)}else _.get("iframe")?r(" "):_.get("html")?r(_.get("html")):s(_,e)?(e=l(_,e),q=_.get("createImg"),t(q).addClass(Z+"Photo").bind("error."+Z,function(){r(n(st,"Error").html(_.get("imgError")))}).one("load",function(){d===lt&&setTimeout(function(){var e;_.get("retinaImage")&&i.devicePixelRatio>1&&(q.height=q.height/i.devicePixelRatio,q.width=q.width/i.devicePixelRatio),_.get("scalePhotos")&&(o=function(){q.height-=q.height*e,q.width-=q.width*e},_.mw&&q.width>_.mw&&(e=(q.width-_.mw)/q.width,o()),_.mh&&q.height>_.mh&&(e=(q.height-_.mh)/q.height,o())),_.h&&(q.style.marginTop=Math.max(_.mh-q.height,0)/2+"px"),W[1]&&(_.get("loop")||W[A+1])&&(q.style.cursor="pointer",t(q).bind("click."+Z,function(){J.next()})),q.style.width=q.width+"px",q.style.height=q.height+"px",r(q)},1)}),q.src=e):e&&M.load(e,_.get("data"),function(e,i){d===lt&&r("error"===i?n(st,"Error").html(_.get("xhrError")):t(this).contents())})}var v,x,y,b,T,C,H,k,W,E,I,M,L,F,R,S,K,P,B,O,_,j,D,N,z,A,q,U,$,G,Q,J,V,X={html:!1,photo:!1,iframe:!1,inline:!1,transition:"elastic",speed:300,fadeOut:300,width:!1,initialWidth:"600",innerWidth:!1,maxWidth:!1,height:!1,initialHeight:"450",innerHeight:!1,maxHeight:!1,scalePhotos:!0,scrolling:!0,opacity:.9,preloading:!0,className:!1,overlayClose:!0,escKey:!0,arrowKey:!0,top:!1,bottom:!1,left:!1,right:!1,fixed:!1,data:void 0,closeButton:!0,fastIframe:!0,open:!1,reposition:!0,loop:!0,slideshow:!1,slideshowAuto:!0,slideshowSpeed:2500,slideshowStart:"start slideshow",slideshowStop:"stop slideshow",photoRegex:/\.(gif|png|jp(e|g|eg)|bmp|ico|webp|jxr|svg)((#|\?).*)?$/i,retinaImage:!1,retinaUrl:!1,retinaSuffix:"@2x.$1",current:"image {current} of {total}",previous:"previous",next:"next",close:"close",xhrError:"This content failed to load.",imgError:"This image failed to load.",returnFocus:!0,trapFocus:!0,onOpen:!1,onLoad:!1,onComplete:!1,onCleanup:!1,onClosed:!1,rel:function(){return this.rel},href:function(){return t(this).attr("href")},title:function(){return this.title},createImg:function(){var e=new Image,i=t(this).data("cbox-img-attrs");return"object"==typeof i&&t.each(i,function(t,i){e[t]=i}),e},createIframe:function(){var i=e.createElement("iframe"),n=t(this).data("cbox-iframe-attrs");return"object"==typeof n&&t.each(n,function(t,e){i[t]=e}),"frameBorder"in i&&(i.frameBorder=0),"allowTransparency"in i&&(i.allowTransparency="true"),i.name=(new Date).getTime(),i.allowFullscreen=!0,i}},Y="wfcolorbox",Z="wfcbox",tt=Z+"Element",et=Z+"_open",it=Z+"_load",nt=Z+"_complete",ot=Z+"_cleanup",ht=Z+"_closed",rt=Z+"_purge",at=t("<a/>"),st="div",lt=0,dt={},ct=function(){function t(){clearTimeout(r)}function e(){(_.get("loop")||W[A+1])&&(t(),r=setTimeout(J.next,_.get("slideshowSpeed")))}function i(){S.html(_.get("slideshowStop")).unbind(s).one(s,n),at.bind(nt,e).bind(it,t),x.removeClass(a+"off").addClass(a+"on")}function n(){t(),at.unbind(nt,e).unbind(it,t),S.html(_.get("slideshowStart")).unbind(s).one(s,function(){J.next(),i()}),x.removeClass(a+"on").addClass(a+"off")}function o(){h=!1,S.hide(),t(),at.unbind(nt,e).unbind(it,t),x.removeClass(a+"off "+a+"on")}var h,r,a=Z+"Slideshow_",s="click."+Z;return function(){h?_.get("slideshow")||(at.unbind(ot,o),o()):_.get("slideshow")&&W[1]&&(h=!0,at.one(ot,o),_.get("slideshowAuto")?i():n(),S.show())}}();t[Y]||(t(p),(J=t.fn[Y]=t[Y]=function(e,i){var n=this;return e=e||{},t.isFunction(n)&&(n=t("<a/>"),e.open=!0),n[0]?(p(),m()&&(i&&(e.onComplete=i),n.each(function(){var i=t.data(this,Y)||{};t.data(this,Y,t.extend(i,e))}).addClass(tt),new h(n[0],e).get("open")&&f(n[0])),n):n}).position=function(e,i){function n(){T[0].style.width=k[0].style.width=b[0].style.width=parseInt(x[0].style.width,10)-D+"px",b[0].style.height=C[0].style.height=H[0].style.height=parseInt(x[0].style.height,10)-j+"px"}var h,r,s,l=0,d=0,c=x.offset();if(E.unbind("resize."+Z),x.css({top:-9e4,left:-9e4}),r=E.scrollTop(),s=E.scrollLeft(),_.get("fixed")?(c.top-=r,c.left-=s,x.css({position:"fixed"})):(l=r,d=s,x.css({position:"absolute"})),!1!==_.get("right")?d+=Math.max(E.width()-_.w-z-D-a(_.get("right"),"x"),0):!1!==_.get("left")?d+=a(_.get("left"),"x"):d+=Math.round(Math.max(E.width()-_.w-z-D,0)/2),!1!==_.get("bottom")?l+=Math.max(o()-_.h-N-j-a(_.get("bottom"),"y"),0):!1!==_.get("top")?l+=a(_.get("top"),"y"):l+=Math.round(Math.max(o()-_.h-N-j,0)/2),x.css({top:c.top,left:c.left,visibility:"visible"}),y[0].style.width=y[0].style.height="9999px",h={width:_.w+z+D,height:_.h+N+j,top:l,left:d},e){var g=0;t.each(h,function(t){h[t]===dt[t]||(g=e)}),e=g}dt=h,e||x.css(h),x.dequeue().animate(h,{duration:e||0,complete:function(){n(),$=!1,y[0].style.width=_.w+z+D+"px",y[0].style.height=_.h+N+j+"px",_.get("reposition")&&setTimeout(function(){E.bind("resize."+Z,J.position)},1),t.isFunction(i)&&i()},step:n})},J.resize=function(t){var e;U&&((t=t||{}).width&&(_.w=a(t.width,"x")-z-D),t.innerWidth&&(_.w=a(t.innerWidth,"x")),I.css({width:_.w}),t.height&&(_.h=a(t.height,"y")-N-j),t.innerHeight&&(_.h=a(t.innerHeight,"y")),t.innerHeight||t.height||(e=I.scrollTop(),I.css({height:"auto"}),_.h=I.height()),I.css({height:_.h}),e&&I.scrollTop(e),J.position("none"===_.get("transition")?0:_.get("speed")))},J.prep=function(i){if(U){var o,a="none"===_.get("transition")?0:_.get("speed");I.remove(),(I=n(st,"LoadedContent").append(i)).hide().appendTo(M.show()).css({width:(_.w=_.w||I.width(),_.w=_.mw&&_.mw<_.w?_.mw:_.w,_.w),overflow:_.get("scrolling")?"auto":"hidden"}).css({height:(_.h=_.h||I.height(),_.h=_.mh&&_.mh<_.h?_.mh:_.h,_.h)}).prependTo(b),M.hide(),t(q).css({float:"none"}),c(_.get("className")),o=function(){function i(){!1===t.support.opacity&&x[0].style.removeAttribute("filter")}var n,o,d=W.length;U&&(o=function(){clearTimeout(Q),L.hide(),u(nt),_.get("onComplete")},F.html(_.get("title")).show(),I.show(),d>1?("string"==typeof _.get("current")&&R.html(_.get("current").replace("{current}",A+1).replace("{total}",d)).show(),K[_.get("loop")||A<d-1?"show":"hide"]().html(_.get("next")),P[_.get("loop")||A?"show":"hide"]().html(_.get("previous")),ct(),_.get("preloading")&&t.each([r(-1),r(1)],function(){var i=W[this],n=new h(i,t.data(i,Y)),o=n.get("href");o&&s(n,o)&&(o=l(n,o),e.createElement("img").src=o)})):O.hide(),_.get("iframe")?(n=_.get("createIframe"),_.get("scrolling")||(n.scrolling="no"),t(n).attr({src:_.get("href"),class:Z+"Iframe"}).one("load",o).appendTo(I),at.one(rt,function(){n.src="//about:blank"}),_.get("fastIframe")&&t(n).trigger("load")):o(),"fade"===_.get("transition")?x.fadeTo(a,1,i):i())},"fade"===_.get("transition")?x.fadeTo(a,0,function(){J.position(0,o)}):J.position(a,o)}},J.next=function(){!$&&W[1]&&(_.get("loop")||W[A+1])&&(A=r(1),f(W[A]))},J.prev=function(){!$&&W[1]&&(_.get("loop")||A)&&(A=r(-1),f(W[A]))},J.close=function(){U&&!G&&(G=!0,U=!1,u(ot),_.get("onCleanup"),E.unbind("."+Z),v.fadeTo(_.get("fadeOut")||0,0),x.stop().fadeTo(_.get("fadeOut")||0,0,function(){x.hide(),v.hide(),u(rt),I.remove(),setTimeout(function(){G=!1,u(ht),_.get("onClosed")},1)}))},J.remove=function(){x&&(x.stop(),t[Y].close(),x.stop(!1,!0).remove(),v.remove(),G=!1,x=null,t("."+tt).removeData(Y).removeClass(tt),t(e).unbind("click."+Z).unbind("keydown."+Z))},J.element=function(){return t(_.el)},J.settings=X)}(jQuery,document,window);