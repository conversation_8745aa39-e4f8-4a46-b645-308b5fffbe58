# Copyright (C) 2025 Wordfence
# This file is distributed under the GPLv3.
msgid ""
msgstr ""
"Project-Id-Version: Wordfence Security 8.0.5\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wordfence-zip-TMYSeWBPU\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-04-08T15:23:30+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: wordfence\n"

#. Plugin Name of the plugin
msgid "Wordfence Security"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.wordfence.com/"
msgstr ""

#. Description of the plugin
msgid "Wordfence Security - Anti-virus, Firewall and Malware Scan"
msgstr ""

#. Author of the plugin
msgid "Wordfence"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:175
msgid "Wordfence WAF Mode Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:176
msgid "Wordfence WAF Rule Status Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:177
msgid "Wordfence WAF Protection Level Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:178
msgid "Wordfence WAF Allow Entry Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:179
msgid "Wordfence WAF Allow Entry Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:180
msgid "Wordfence WAF Allow Entry Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:181
msgid "Wordfence WAF Blocklist Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:183
msgid "Allowlisted IPs Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:184
msgid "Allowlisted Services Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:185
msgid "Allowed 404s Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:186
msgid "Ignored Alert IPs Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:188
msgid "Banned URLs Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:189
msgid "Banned Usernames Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:191
msgid "Brute Force Protection Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:192
msgid "General Blocking and Rate Limiting Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:193
msgid "Never Block Crawlers Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:194
msgid "Lockout Invalid Users Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:195
msgid "Prevent Use of Breached Passwords Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:196
msgid "Enforce Strong Passwords Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:197
msgid "Mask Login Errors Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:198
msgid "Prevent Using \"admin\" Username Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:199
msgid "Block Author Scanning Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:200
msgid "Prevent Use of Application Passwords Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:201
msgid "Block Bad POST Requests Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:202
msgid "Check Password Strength on Reset Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:204
msgid "Failed Login Failure Threshold Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:205
msgid "Forgot Password Threshold Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:206
msgid "Login Security Counting Period Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:207
msgid "Login Security Lockout Threshold Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:208
msgid "Automatic Block Duration Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:210
msgid "Custom Block Text Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:212
msgid "Global Rate Limit Settings Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:213
msgid "Crawler Rate Limit Settings Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:214
msgid "Crawler 404 Rate Limit Settings Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:215
msgid "Human Rate Limit Settings Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:216
msgid "Human 404 Rate Limit Settings Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:218
msgid "Scan Options Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:219
msgid "Scan Schedule Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:221
msgid "Country Blocking Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:222
msgid "Manual Block Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:223
msgid "Block Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:225
msgid "Participate in the Wordfence Security Network Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:226
msgid "Audit Log Mode Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:227
msgid "License Key Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:229
#: lib/audit-log/wfAuditLogObserversWordfence.php:237
msgid "IP Source Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:230
#: lib/audit-log/wfAuditLogObserversWordfence.php:238
msgid "Trusted Proxies Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:231
msgid "Trusted Proxy Preset Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:233
msgid "2FA Deactivated on User"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:234
msgid "2FA Activated on User"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:235
msgid "XML-RPC Requires 2FA Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:236
msgid "IPs Bypassing 2FA Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:239
msgid "2FA Role Requirements Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:240
msgid "2FA Grace Period Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:241
msgid "XML-RPC Interface Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:242
msgid "Login Captcha Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:243
msgid "reCAPTCHA Threshold Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:244
msgid "WooCommerce 2FA Integration Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordfence.php:245
msgid "Captcha Test Mode Toggled"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:66
msgid "Attachment Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:67
msgid "Attachment Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:68
msgid "Attachment Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:71
msgid "Page Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:72
msgid "Page Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:73
msgid "Page Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:75
msgid "Page Moved to Trash"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:76
msgid "Page Removed from Trash"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:79
msgid "Post Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:80
msgid "Post Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:81
msgid "Post Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:83
msgid "Post Moved to Trash"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreContent.php:84
msgid "Post Removed from Trash"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:81
msgid "Multisite Blog Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:82
msgid "Multisite Blog Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:83
msgid "Multisite Blog Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:85
msgid "Multisite Blog Activated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:86
msgid "Multisite Blog Deactivated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:87
msgid "Multisite Blog Signup Submitted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:89
msgid "Multisite Blog Archived"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:90
msgid "Multisite Blog Moved to Trash"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:91
msgid "Multisite Blog Made Public"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:92
msgid "Multisite Blog Marked as Spam"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:93
msgid "Multisite Blog Unarchived"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:94
msgid "Multisite Blog Removed from Trash"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:95
msgid "Multisite Blog Made Private"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:96
msgid "Multisite Blog Unmarked as Spam"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:98
msgid "Multisite User Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:99
msgid "Multisite User Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:100
msgid "Multisite User Activated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:101
msgid "User Added to Multisite Blog"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:102
msgid "User Removed from Multisite Blog"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:103
msgid "User Invited to Multisite Blog"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:104
msgid "User Signed Up on Multisite Blog"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:106
msgid "Multisite Network Plugins Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:108
msgid "Super Admin Granted to User"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreMultisite.php:109
msgid "Super Admin Revoked from User"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:102
msgid "Site Data Exported"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:103
msgid "Recovery Key Generated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:105
msgid "Mail Send Failed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:106
msgid "Mail Sent"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:108
msgid "Active Plugins Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:109
msgid "Admin Email Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:110
msgid "Anonymous Comments Allowed Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:111
msgid "Comment Moderation Default Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:112
msgid "Default Comment Status Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:113
msgid "Default User Role Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:114
msgid "Home URL Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:115
msgid "Site URL Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:116
msgid "Child Theme Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:117
msgid "Parent Theme Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:118
msgid "User Registration Permission Option Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:120
msgid "Role Capabilities Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:121
msgid "Admin Page View Denied"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:123
msgid "Plugin Installed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:124
msgid "Plugin Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:125
msgid "Plugin Activated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:126
msgid "Plugin Deactivated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:128
msgid "Theme Installed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:129
msgid "Theme Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:130
msgid "Theme Switched"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:131
msgid "Theme Customized"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:132
msgid "Theme Sidebar Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:134
msgid "Automatic Updates Completed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:135
msgid "Core Update Completed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:136
msgid "Plugin Update Completed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreSite.php:137
msgid "Theme Update Completed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:61
msgid "User Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:62
msgid "User Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:63
msgid "User Updated"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:65
msgid "App Password Created"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:66
msgid "App Password Deleted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:67
msgid "App Password Accepted"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:69
msgid "User Logged In"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:70
msgid "User Logged Out"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:71
msgid "Auth Cookie Set"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:72
msgid "Password Reset"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:74
msgid "Role Added to User"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:75
msgid "Role Removed from User"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:76
msgid "User Capabilities Meta Value Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:77
msgid "User Level Meta Value Changed"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:79
msgid "User Unmarked as Spam"
msgstr ""

#: lib/audit-log/wfAuditLogObserversWordPressCoreUser.php:80
msgid "User Marked as Spam"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Block"
#: lib/dashboard/widget_content_countries.php:6
#: lib/dashboard/widget_content_ips.php:7
#: lib/dashboard/widget_ips.php:116
#: views/blocking/blocking-create.php:14
#: views/reports/activity-report-email-inline.php:147
#: views/reports/activity-report-email-inline.php:201
#: views/reports/activity-report.php:17
#: views/reports/activity-report.php:61
msgid "Country"
msgstr ""

#: lib/dashboard/widget_content_countries.php:7
#: lib/dashboard/widget_content_ips.php:8
#: lib/dashboard/widget_ips.php:117
#: views/blocking/block-list.php:73
#: views/reports/activity-report-email-inline.php:148
#: views/reports/activity-report-email-inline.php:203
#: views/reports/activity-report.php:18
#: views/reports/activity-report.php:63
msgid "Block Count"
msgstr ""

#: lib/dashboard/widget_content_ips.php:6
#: lib/dashboard/widget_content_logins.php:7
#: lib/dashboard/widget_ips.php:115
#: lib/dashboard/widget_logins.php:96
#: lib/wordfenceClass.php:6355
#: views/reports/activity-report-email-inline.php:146
#: views/reports/activity-report.php:16
#: views/waf/option-whitelist.php:110
#: views/waf/options-group-whitelisted.php:87
#: views/waf/options-group-whitelisted.php:100
msgid "IP"
msgstr ""

#: lib/dashboard/widget_content_logins.php:6
#: lib/dashboard/widget_logins.php:95
#: lib/wordfenceClass.php:6431
#: views/reports/activity-report-email-inline.php:256
#: views/reports/activity-report.php:106
msgid "Username"
msgstr ""

#: lib/dashboard/widget_content_logins.php:8
#: lib/dashboard/widget_logins.php:97
msgid "Date"
msgstr ""

#: lib/dashboard/widget_countries.php:9
msgid "Top Countries by Number of Attacks - Last 7 Days"
msgstr ""

#: lib/dashboard/widget_countries.php:16
#: lib/dashboard/widget_ips.php:21
#: lib/dashboard/widget_localattacks.php:17
msgid "No Data Available During Learning Mode"
msgstr ""

#: lib/dashboard/widget_countries.php:25
#: lib/dashboard/widget_networkattacks.php:8
#: lib/wfDiagnostic.php:997
msgid "Wordfence Network"
msgstr ""

#: lib/dashboard/widget_countries.php:31
#: lib/dashboard/widget_countries.php:38
#: lib/dashboard/widget_ips.php:35
#: lib/dashboard/widget_ips.php:45
#: lib/dashboard/widget_ips.php:55
#: lib/dashboard/widget_localattacks.php:32
msgid "No blocks have been recorded."
msgstr ""

#: lib/dashboard/widget_ips.php:14
msgid "Top IPs Blocked"
msgstr ""

#: lib/dashboard/widget_ips.php:28
#: lib/dashboard/widget_networkattacks.php:22
msgid "24 Hours"
msgstr ""

#: lib/dashboard/widget_ips.php:29
msgid "7 Days"
msgstr ""

#: lib/dashboard/widget_ips.php:30
#: lib/dashboard/widget_networkattacks.php:23
msgid "30 Days"
msgstr ""

#: lib/dashboard/widget_ips.php:39
#: lib/dashboard/widget_ips.php:49
#: lib/dashboard/widget_ips.php:59
#: lib/dashboard/widget_logins.php:30
#: lib/dashboard/widget_logins.php:40
msgid "Show more"
msgstr ""

#. translators: WordPress username.
#: lib/dashboard/widget_ips.php:96
#: lib/dashboard/widget_logins.php:77
#: lib/wordfenceClass.php:6300
msgid "An error occurred"
msgstr ""

#: lib/dashboard/widget_ips.php:96
#: lib/dashboard/widget_logins.php:77
msgid "We encountered an error trying load more data."
msgstr ""

#: lib/dashboard/widget_localattacks.php:8
msgid "Firewall Summary:"
msgstr ""

#. translators: The site's domain name.
#: lib/dashboard/widget_localattacks.php:10
msgid "Attacks Blocked for %s"
msgstr ""

#: lib/dashboard/widget_localattacks.php:37
#: views/blocking/blocking-create.php:10
msgid "<span class=\"wf-hidden-xs\">Block </span>Type"
msgstr ""

#: lib/dashboard/widget_localattacks.php:44
#: modules/login-security/views/settings/user-stats.php:59
msgid "Total"
msgstr ""

#: lib/dashboard/widget_localattacks.php:49
msgid "Today"
msgstr ""

#: lib/dashboard/widget_localattacks.php:49
msgid "Week"
msgstr ""

#: lib/dashboard/widget_localattacks.php:49
msgid "Month"
msgstr ""

#: lib/dashboard/widget_localattacks.php:64
#: lib/menu_firewall_waf.php:52
#: lib/menu_firewall_waf_options.php:159
#: lib/menu_scanner.php:73
#: lib/menu_scanner_options.php:128
#: lib/wfLicense.php:243
msgid "Premium"
msgstr ""

#: lib/dashboard/widget_localattacks.php:70
msgid "How are these categorized?"
msgstr ""

#. translators: URL to WordPress admin panel.
#. translators: Support URL.
#. translators: 1. Time duration. 2. Support URL.
#. translators: 1. URL. 2. URL.
#. translators: 1. URL to publicly accessible file. 2. Support URL.
#. translators: URL to publicly accessible file.
#. translators: 1. WordPress Post type. 2. URL. 3. URL.
#. translators: 1. WordPress post type. 2. URL. 3. URL.
#. translators: 1. WordPress version. 2. WordPress version.
#. translators: 1. WordPress admin panel URL. 2. Support URL.
#. translators: 1. Reset password URL. 2. Support URL.
#. translators: Localized date.
#. translators: Time limit (number).
#. translators: 1. Plugin name. 2. Plugin version. 3. Support URL.
#. translators: Number of files.
#. translators: word order may be altered as long as HTML remains around "full"
#: lib/dashboard/widget_localattacks.php:70
#: lib/dashboard/widget_notifications.php:19
#: lib/dashboard/widget_notifications.php:124
#: lib/email_unsubscribeRequest.php:14
#: lib/IPTrafList.php:24
#: lib/IPTrafList.php:41
#: lib/IPTrafList.php:51
#: lib/menu_dashboard.php:114
#: lib/menu_dashboard_options.php:158
#: lib/menu_firewall_blocking.php:34
#: lib/menu_support.php:41
#: lib/menu_support.php:53
#: lib/menu_support.php:59
#: lib/menu_support.php:85
#: lib/menu_support.php:123
#: lib/menu_support.php:141
#: lib/menu_support.php:146
#: lib/menu_support.php:167
#: lib/menu_tools_auditlog.php:93
#: lib/menu_tools_auditlog.php:130
#: lib/menu_tools_diagnostic.php:717
#: lib/menu_tools_diagnostic.php:876
#: lib/menu_tools_diagnostic.php:877
#: lib/menu_tools_diagnostic.php:882
#: lib/menu_tools_diagnostic.php:883
#: lib/menu_tools_diagnostic.php:888
#: lib/menu_tools_diagnostic.php:895
#: lib/menu_tools_diagnostic.php:902
#: lib/menu_tools_livetraffic.php:209
#: lib/menu_tools_livetraffic.php:346
#: lib/menu_tools_livetraffic.php:353
#: lib/menu_tools_livetraffic.php:428
#: lib/menu_tools_livetraffic.php:481
#: lib/menu_tools_twoFactor.php:51
#: lib/menu_wordfence_central.php:56
#: lib/wf503.php:390
#: lib/wfCentralAPI.php:912
#: lib/wfScanEngine.php:310
#: lib/wfScanEngine.php:648
#: lib/wfScanEngine.php:661
#: lib/wfScanEngine.php:733
#: lib/wfScanEngine.php:756
#: lib/wfScanEngine.php:832
#: lib/wfScanEngine.php:1062
#: lib/wfScanEngine.php:1063
#: lib/wfScanEngine.php:1204
#: lib/wfScanEngine.php:1341
#: lib/wfScanEngine.php:1484
#: lib/wfScanEngine.php:1855
#: lib/wfScanEngine.php:1972
#: lib/wfScanEngine.php:2031
#: lib/wfScanEngine.php:2200
#: lib/wfScanEngine.php:2239
#: lib/wfScanEngine.php:2440
#: lib/wfScanEngine.php:2482
#: lib/wfVersionCheckController.php:69
#: lib/wfVersionCheckController.php:92
#: lib/wfVersionCheckController.php:168
#: lib/wfVersionCheckController.php:187
#: lib/wordfenceClass.php:1446
#: lib/wordfenceClass.php:2824
#: lib/wordfenceClass.php:2960
#: lib/wordfenceClass.php:3115
#: lib/wordfenceClass.php:3151
#: lib/wordfenceClass.php:3649
#: lib/wordfenceClass.php:5039
#: lib/wordfenceClass.php:5047
#: lib/wordfenceClass.php:5053
#: lib/wordfenceClass.php:5078
#: lib/wordfenceClass.php:5090
#: lib/wordfenceClass.php:5098
#: lib/wordfenceClass.php:6475
#: lib/wordfenceClass.php:6492
#: lib/wordfenceClass.php:6514
#: lib/wordfenceClass.php:6533
#: lib/wordfenceClass.php:6539
#: lib/wordfenceClass.php:6586
#: lib/wordfenceHash.php:540
#: lib/wordfenceHash.php:587
#: lib/wordfenceHash.php:661
#: lib/wordfenceHash.php:895
#: views/blocking/block-list.php:35
#: views/blocking/blocking-create.php:179
#: views/blocking/blocking-status.php:27
#: views/blocking/option-bypass-cookie.php:8
#: views/blocking/option-bypass-redirect.php:8
#: views/blocking/options-group-advanced-country.php:85
#: views/common/page-help.php:2
#: views/common/page-title.php:29
#: views/common/section-subtitle.php:27
#: views/common/section-title.php:27
#: views/common/status-critical.php:17
#: views/common/status-detail.php:37
#: views/common/status-tooltip.php:19
#: views/common/status-tooltip.php:30
#: views/common/status-warning.php:17
#: views/dashboard/option-howgetips.php:21
#: views/dashboard/options-group-dashboard.php:107
#: views/dashboard/options-group-general.php:36
#: views/dashboard/options-group-import.php:31
#: views/dashboard/options-group-import.php:44
#: views/dashboard/options-group-license.php:35
#: views/dashboard/options-group-license.php:65
#: views/dashboard/options-group-license.php:67
#: views/dashboard/options-group-license.php:72
#: views/dashboard/status-payment-expiring.php:31
#: views/dashboard/status-renewing.php:17
#: views/gdpr/banner.php:55
#: views/onboarding/modal-final-attempt.php:29
#: views/onboarding/modal-final-attempt.php:30
#: views/options/option-label.php:28
#: views/options/option-select.php:22
#: views/options/option-switch.php:35
#: views/options/option-text.php:30
#: views/options/option-textarea.php:37
#: views/options/option-toggled-boolean-switch.php:32
#: views/options/option-toggled-multiple.php:29
#: views/options/option-toggled-segmented.php:21
#: views/options/option-toggled-select.php:27
#: views/options/option-toggled-sub.php:44
#: views/options/option-toggled-sub.php:52
#: views/options/option-toggled-textarea.php:24
#: views/options/option-toggled.php:36
#: views/options/option-token.php:25
#: views/scanner/issue-configReadable.php:12
#: views/scanner/issue-configReadable.php:17
#: views/scanner/issue-coreUnknown.php:13
#: views/scanner/issue-database.php:18
#: views/scanner/issue-file.php:19
#: views/scanner/issue-file.php:20
#: views/scanner/issue-knownfile.php:19
#: views/scanner/issue-knownfile.php:20
#: views/scanner/issue-publiclyAccessible.php:12
#: views/scanner/issue-publiclyAccessible.php:17
#: views/scanner/issue-wfPluginAbandoned.php:18
#: views/scanner/issue-wfPluginAbandoned.php:19
#: views/scanner/issue-wfPluginAbandoned.php:20
#: views/scanner/issue-wfPluginRemoved.php:17
#: views/scanner/issue-wfPluginRemoved.php:18
#: views/scanner/issue-wfPluginUpgrade.php:18
#: views/scanner/issue-wfPluginUpgrade.php:19
#: views/scanner/issue-wfPluginUpgrade.php:20
#: views/scanner/issue-wfPluginVulnerable.php:17
#: views/scanner/issue-wfPluginVulnerable.php:18
#: views/scanner/issue-wfThemeUpgrade.php:18
#: views/scanner/issue-wfThemeUpgrade.php:19
#: views/scanner/issue-wfUpgrade.php:17
#: views/scanner/issue-wpscan_directoryList.php:12
#: views/scanner/issue-wpscan_directoryList.php:17
#: views/scanner/issue-wpscan_fullPathDiscl.php:12
#: views/scanner/issue-wpscan_fullPathDiscl.php:17
#: views/scanner/option-scan-signatures.php:22
#: views/scanner/scan-progress-detailed.php:16
#: views/scanner/scan-progress-element.php:59
#: views/scanner/scan-scheduling.php:38
#: views/scanner/scanner-status.php:38
#: views/scanner/scanner-status.php:67
#: views/scanner/site-cleaning-beta-sigs.php:16
#: views/scanner/site-cleaning-bottom.php:18
#: views/scanner/site-cleaning-bottom.php:19
#: views/scanner/site-cleaning-bottom.php:21
#: views/scanner/site-cleaning-high-sense.php:16
#: views/scanner/site-cleaning.php:19
#: views/scanner/site-cleaning.php:21
#: views/tools/options-group-2fa.php:40
#: views/tours/login-security.php:30
#: views/tours/login-security.php:62
#: views/waf/firewall-status.php:38
#: views/waf/firewall-status.php:70
#: views/waf/firewall-status.php:80
#: views/waf/option-rate-limit.php:27
#: views/waf/option-rules.php:5
#: views/waf/option-whitelist.php:5
#: views/waf/options-group-basic-firewall.php:471
#: views/waf/waf-install.php:13
msgid "opens in new tab"
msgstr ""

#: lib/dashboard/widget_logins.php:9
#: views/reports/activity-report-email-inline.php:257
#: views/reports/activity-report.php:107
msgid "Login Attempts"
msgstr ""

#: lib/dashboard/widget_logins.php:20
msgid "Successful"
msgstr ""

#: lib/dashboard/widget_logins.php:21
msgid "Failed"
msgstr ""

#: lib/dashboard/widget_logins.php:26
msgid "No successful logins have been recorded."
msgstr ""

#: lib/dashboard/widget_logins.php:36
msgid "No failed logins have been recorded."
msgstr ""

#: lib/dashboard/widget_networkattacks.php:8
msgid "Total Attacks Blocked:"
msgstr ""

#: lib/dashboard/widget_networkattacks.php:17
msgid "Blocked attack counts not available yet."
msgstr ""

#: lib/dashboard/widget_networkattacks.php:32
msgid "Total Attacks"
msgstr ""

#. translators: Time since. Example: 1 minute, 2 seconds
#: lib/dashboard/widget_networkattacks.php:208
msgid "Last Updated: %s ago"
msgstr ""

#: lib/dashboard/widget_notifications.php:8
#: lib/wordfenceClass.php:7009
msgid "Notifications"
msgstr ""

#: lib/dashboard/widget_notifications.php:25
msgid "No notifications received"
msgstr ""

#: lib/dashboard/widget_notifications.php:37
#: lib/menu_wordfence_central.php:61
msgid "Wordfence Central Status"
msgstr ""

#: lib/dashboard/widget_notifications.php:43
#: lib/menu_wordfence_central.php:104
msgid "It looks like you've tried to connect this site to Wordfence Central, but the installation did not finish."
msgstr ""

#: lib/dashboard/widget_notifications.php:46
#: lib/menu_wordfence_central.php:55
#: lib/menu_wordfence_central.php:114
msgid "Wordfence Central allows you to manage Wordfence on multiple sites from one location. It makes security monitoring and configuring Wordfence easier."
msgstr ""

#: lib/dashboard/widget_notifications.php:52
msgid "Connection:"
msgstr ""

#. translators: 1. Email address. 2. Localized date.
#: lib/dashboard/widget_notifications.php:56
msgid "Connected by %1$s on %2$s"
msgstr ""

#. translators: 1. Localized date.
#: lib/dashboard/widget_notifications.php:61
msgid "Disconnected on %1$s"
msgstr ""

#: lib/dashboard/widget_notifications.php:65
msgid "a Wordfence Central user"
msgstr ""

#: lib/dashboard/widget_notifications.php:68
#: lib/menu_tools_diagnostic.php:814
#: lib/menu_wordfence_central.php:41
#: lib/menu_wordfence_central.php:54
#: lib/wordfenceClass.php:6921
#: views/diagnostics/text.php:583
msgid "Wordfence Central"
msgstr ""

#. translators: 1. Email address or placeholder. 2. Localized date.
#: lib/dashboard/widget_notifications.php:75
msgid "Disconnected by %1$s on %2$s"
msgstr ""

#: lib/dashboard/widget_notifications.php:78
msgid "Connection not finished"
msgstr ""

#: lib/dashboard/widget_notifications.php:80
msgid "Not connected"
msgstr ""

#: lib/dashboard/widget_notifications.php:83
msgid "Audit Log:"
msgstr ""

#: lib/dashboard/widget_notifications.php:87
msgid "Disabled (premium feature)"
msgstr ""

#: lib/dashboard/widget_notifications.php:91
msgid "Not recording (Central disconnected)"
msgstr ""

#: lib/dashboard/widget_notifications.php:94
msgid "Not recording"
msgstr ""

#: lib/dashboard/widget_notifications.php:97
msgid "Recording (significant events only)"
msgstr ""

#: lib/dashboard/widget_notifications.php:100
msgid "Recording (all events)"
msgstr ""

#: lib/dashboard/widget_notifications.php:106
msgid "Manage"
msgstr ""

#: lib/dashboard/widget_notifications.php:113
#: lib/menu_wordfence_central.php:108
#: views/onboarding/banner.php:13
#: views/onboarding/disabled-overlay.php:8
msgid "Resume Installation"
msgstr ""

#: lib/dashboard/widget_notifications.php:114
#: lib/dashboard/widget_notifications.php:119
#: lib/menu_wordfence_central.php:65
#: lib/wfCentralAPI.php:904
msgid "Disconnect This Site"
msgstr ""

#: lib/dashboard/widget_notifications.php:121
msgid "Reconnect This Site"
msgstr ""

#: lib/dashboard/widget_notifications.php:121
msgid "Connect This Site"
msgstr ""

#: lib/dashboard/widget_notifications.php:124
#: lib/menu_wordfence_central.php:56
msgid "Visit Wordfence Central"
msgstr ""

#: lib/dashboard/widget_notifications.php:201
msgid "Confirm Disconnect"
msgstr ""

#: lib/dashboard/widget_notifications.php:202
msgid "Are you sure you want to disconnect your site from Wordfence Central?"
msgstr ""

#. translators: Support URL.
#: lib/dashboard/widget_notifications.php:203
#: lib/menu_scanner.php:207
#: lib/menu_scanner.php:218
#: lib/menu_tools_diagnostic.php:1010
#: lib/menu_tools_twoFactor.php:235
#: lib/menu_tools_twoFactor.php:273
#: lib/wordfenceClass.php:8172
#: lib/wordfenceClass.php:8226
#: lib/wordfenceClass.php:8290
#: lib/wordfenceClass.php:8361
#: lib/wordfenceClass.php:8408
#: modules/login-security/views/manage/deactivate.php:31
#: modules/login-security/views/manage/regenerate.php:26
#: views/blocking/block-list.php:501
#: views/blocking/blocking-create.php:212
#: views/blocking/blocking-create.php:529
#: views/dashboard/options-group-license.php:178
#: views/offboarding/deactivation-prompt.php:30
#: views/offboarding/deactivation-prompt.php:46
#: views/offboarding/deactivation-prompt.php:59
#: views/options/block-all-options-controls.php:164
#: views/options/block-controls.php:79
msgid "Cancel"
msgstr ""

#: lib/dashboard/widget_notifications.php:204
msgid "Disconnect"
msgstr ""

#: lib/diffResult.php:9
msgid "Wordfence: Viewing File Differences"
msgstr ""

#: lib/diffResult.php:11
msgid "The two panels below show a before and after view of a file on your system that has been modified. The left panel shows the original file before modification. The right panel shows your version of the file that has been modified. Use this view to determine if a file has been modified by an attacker or if this is a change that you or another trusted person made. If you are happy with the modifications you see here, then you should choose to ignore this file the next time Wordfence scans your system."
msgstr ""

#: lib/diffResult.php:14
#: lib/wfViewResult.php:10
msgid "Filename:"
msgstr ""

#: lib/diffResult.php:15
msgid "File type:"
msgstr ""

#: lib/diffResult.php:18
msgid "WordPress Core File"
msgstr ""

#: lib/diffResult.php:20
msgid "Theme File"
msgstr ""

#: lib/diffResult.php:21
msgid "Theme Name:"
msgstr ""

#: lib/diffResult.php:23
msgid "Theme Version:"
msgstr ""

#: lib/diffResult.php:25
msgid "Plugin File"
msgstr ""

#: lib/diffResult.php:26
msgid "Plugin Name:"
msgstr ""

#: lib/diffResult.php:27
msgid "Plugin Version:"
msgstr ""

#: lib/diffResult.php:29
msgid "Unknown Type"
msgstr ""

#: lib/diffResult.php:38
msgid "There are no differences between the original file and the file in the repository."
msgstr ""

#: lib/diffResult.php:44
msgid "&copy;&nbsp;%1$d to %2$d Wordfence &mdash; Visit <a href=\"https://www.wordfence.com/\">Wordfence.com</a> for help, security updates and more."
msgstr ""

#. translators: 1. Blog name/title. 2. Date.
#: lib/email_genericAlert.php:4
msgid "This email was sent from your website \"%1$s\" by the Wordfence plugin at %2$s"
msgstr ""

#. translators: URL to the WordPress admin panel.
#: lib/email_genericAlert.php:8
msgid "The Wordfence administrative URL for this site is: %s"
msgstr ""

#: lib/email_genericAlert.php:14
msgid ""
"NOTE: You are using the free version of Wordfence. Upgrade today:\n"
" - Receive real-time Firewall and Scan engine rule updates for protection as threats emerge\n"
" - Real-time IP Blocklist blocks the most malicious IPs from accessing your site\n"
" - Country blocking\n"
" - IP reputation monitoring\n"
" - Schedule scans to run more frequently and at optimal times\n"
" - Access to Premium Support\n"
" - Discounts for multi-year and multi-license purchases\n"
"\n"
"Click here to upgrade to Wordfence Premium:\n"
"https://www.wordfence.com/zz1/wordfence-signup/"
msgstr ""

#. translators: URL to the WordPress admin panel.
#: lib/email_genericAlert.php:30
msgid ""
"To change your alert options for Wordfence, visit:\n"
"%s"
msgstr ""

#. translators: URL to the WordPress admin panel.
#: lib/email_genericAlert.php:34
msgid ""
"To see current Wordfence alerts, visit:\n"
"%s"
msgstr ""

#. translators: URL to the site's homepage.
#: lib/email_newIssues.php:5
msgid "This email was sent from your website \"%s\" by the Wordfence plugin."
msgstr ""

#. translators: 1. URL to the site's homepage. 2. Number of scan results.
#: lib/email_newIssues.php:12
msgid "Wordfence found the following new issues on \"%1$s\" (%2$d existing issue was also found again)."
msgid_plural "Wordfence found the following new issues on \"%1$s\" (%2$d existing issues were also found again)."
msgstr[0] ""
msgstr[1] ""

#. translators: 1. URL to the site's homepage.
#: lib/email_newIssues.php:22
msgid "Wordfence found the following new issues on \"%1$s\"."
msgstr ""

#. translators: Localized date.
#: lib/email_newIssues.php:32
msgid "Alert generated at %s"
msgstr ""

#. translators: URL to WordPress admin panel.
#: lib/email_newIssues.php:38
msgid "See the details of these scan results on your site at: %s"
msgstr ""

#: lib/email_newIssues.php:42
msgid "HIGH SENSITIVITY scanning is enabled, it may produce false positives"
msgstr ""

#. translators: 1. URL to WordPress admin panel. 2. URL to WordPress admin panel. 3. URL to Wordfence support page. 4. URL to Wordfence support page.
#: lib/email_newIssues.php:50
msgid "The scan was terminated early because it reached the time limit for scans. If you would like to allow your scans to run longer, you can customize the limit on the options page: <a href=\"%1$s\">%2$s</a> or read more about scan options to improve scan speed here: <a href=\"%3$s\">%4$s</a>"
msgstr ""

#: lib/email_newIssues.php:56
msgid "Critical Problems:"
msgstr ""

#: lib/email_newIssues.php:57
msgid "High Severity Problems:"
msgstr ""

#: lib/email_newIssues.php:58
msgid "Medium Severity Problems:"
msgstr ""

#: lib/email_newIssues.php:59
msgid "Low Severity Problems:"
msgstr ""

#: lib/email_newIssues.php:78
msgid "Plugin contains an unpatched security vulnerability."
msgstr ""

#: lib/email_newIssues.php:80
#: lib/email_newIssues.php:106
#: views/scanner/issue-wfPluginAbandoned.php:21
#: views/scanner/issue-wfPluginAbandoned.php:39
#: views/scanner/issue-wfPluginRemoved.php:19
#: views/scanner/issue-wfPluginRemoved.php:34
#: views/scanner/issue-wfPluginUpgrade.php:21
#: views/scanner/issue-wfPluginUpgrade.php:39
#: views/scanner/issue-wfPluginVulnerable.php:19
#: views/scanner/issue-wfPluginVulnerable.php:33
#: views/scanner/issue-wfThemeUpgrade.php:20
#: views/scanner/issue-wfThemeUpgrade.php:37
#: views/scanner/issue-wfUpgrade.php:18
#: views/scanner/issue-wfUpgrade.php:33
msgid "Vulnerability Severity"
msgstr ""

#: lib/email_newIssues.php:83
#: lib/email_newIssues.php:109
#: views/scanner/issue-wfPluginAbandoned.php:20
#: views/scanner/issue-wfPluginAbandoned.php:38
#: views/scanner/issue-wfPluginRemoved.php:18
#: views/scanner/issue-wfPluginRemoved.php:33
#: views/scanner/issue-wfPluginUpgrade.php:20
#: views/scanner/issue-wfPluginUpgrade.php:38
#: views/scanner/issue-wfPluginVulnerable.php:18
#: views/scanner/issue-wfPluginVulnerable.php:32
#: views/scanner/issue-wfThemeUpgrade.php:19
#: views/scanner/issue-wfThemeUpgrade.php:36
#: views/scanner/issue-wfUpgrade.php:17
#: views/scanner/issue-wfUpgrade.php:32
msgid "Vulnerability Information"
msgstr ""

#: lib/email_newIssues.php:89
msgid "The core files scan has not run because this version is not currently indexed by Wordfence. New WordPress versions may take up to a day to be indexed."
msgstr ""

#: lib/email_newIssues.php:92
msgid "Firewall issues may be caused by file permission changes or other technical problems."
msgstr ""

#: lib/email_newIssues.php:92
msgid "More Details and Instructions"
msgstr ""

#: lib/email_newIssues.php:95
msgid "Scanning additional paths is optional and is not always necessary."
msgstr ""

#. translators: 1. WordPress version. 2. WordPress version.
#: lib/email_newIssues.php:95
#: lib/email_unlockRequest.php:14
#: lib/menu_dashboard.php:114
#: lib/menu_dashboard.php:483
#: lib/menu_dashboard_options.php:158
#: lib/wfVersionCheckController.php:69
#: lib/wfVersionCheckController.php:92
#: lib/wfVersionCheckController.php:168
#: lib/wfVersionCheckController.php:187
#: lib/wordfenceClass.php:6619
#: modules/login-security/classes/controller/wordfencels.php:494
#: modules/login-security/classes/controller/wordfencels.php:506
#: modules/login-security/views/options/option-roles.php:62
#: views/blocking/blocking-create.php:528
#: views/blocking/blocking-status.php:27
#: views/dashboard/options-group-dashboard.php:107
#: views/gdpr/banner.php:55
#: views/offboarding/deactivation-prompt.php:42
#: views/onboarding/modal-final-attempt.php:30
#: views/scanner/scanner-status.php:67
#: views/tours/login-security.php:30
#: views/tours/login-security.php:62
#: views/waf/firewall-status.php:70
#: views/waf/firewall-status.php:80
#: views/waf/options-group-basic-firewall.php:471
msgid "Learn More"
msgstr ""

#: lib/email_newIssues.php:104
#: views/scanner/issue-wfPluginUpgrade.php:16
#: views/scanner/issue-wfPluginUpgrade.php:34
#: views/scanner/issue-wfThemeUpgrade.php:16
#: views/scanner/issue-wfThemeUpgrade.php:33
#: views/scanner/issue-wfUpgrade.php:15
#: views/scanner/issue-wfUpgrade.php:30
msgid "Update includes security-related fixes."
msgstr ""

#: lib/email_newIssues.php:127
msgid "The malicious URL matched"
msgstr ""

#. translators: Number of scan results
#: lib/email_newIssues.php:136
msgid "%d existing issue was found again and is not shown."
msgid_plural "%d existing issues were found again and are not shown."
msgstr[0] ""
msgstr[1] ""

#. translators: Number of scan results
#: lib/email_newIssues.php:139
msgid "%d issue was omitted from this email due to length limits."
msgid_plural "%d issues were omitted from this email due to length limits."
msgstr[0] ""
msgstr[1] ""

#. translators: Number of scan results
#: lib/email_newIssues.php:140
msgid "View every issue:"
msgstr ""

#: lib/email_newIssues.php:149
msgid "NOTE: You are using the free version of Wordfence. Upgrade today:"
msgstr ""

#: lib/email_newIssues.php:152
msgid "Receive real-time Firewall and Scan engine rule updates for protection as threats emerge"
msgstr ""

#: lib/email_newIssues.php:153
msgid "Real-time IP Blocklist blocks the most malicious IPs from accessing your site"
msgstr ""

#: lib/email_newIssues.php:154
msgid "Country blocking"
msgstr ""

#: lib/email_newIssues.php:155
msgid "IP reputation monitoring"
msgstr ""

#: lib/email_newIssues.php:156
msgid "Schedule scans to run more frequently and at optimal times"
msgstr ""

#: lib/email_newIssues.php:157
msgid "Access to Premium Support"
msgstr ""

#: lib/email_newIssues.php:158
msgid "Discounts for multi-year and multi-license purchases"
msgstr ""

#: lib/email_newIssues.php:161
msgid "Click here to upgrade to Wordfence Premium:"
msgstr ""

#. translators: 1. IP address. 2. Site URL. 3. Site name.
#: lib/email_unlockRequest.php:4
msgid "Either you or someone else at IP address <b>%1$s</b> requested instructions to regain access to the website <a href=\"%2$s\"><b>%3$s</b></a>."
msgstr ""

#. translators: Localized date.
#: lib/email_unlockRequest.php:8
#: lib/email_unsubscribeRequest.php:8
msgid "Request was generated at: %s"
msgstr ""

#: lib/email_unlockRequest.php:10
msgid "If you did not request these instructions then you can safely ignore them."
msgstr ""

#: lib/email_unlockRequest.php:11
msgid "These instructions <b>will be valid for 30 minutes</b> from the time they were sent."
msgstr ""

#: lib/email_unlockRequest.php:14
msgid "Click here to unlock your ability to sign-in and to access to the site."
msgstr ""

#: lib/email_unlockRequest.php:14
msgid "Do this if you simply need to regain access because you were accidentally locked out. If you received an \"Insecure Password\" message before getting locked out, you may also need to reset your password."
msgstr ""

#: lib/email_unlockRequest.php:17
msgid "Click here to unblock all IP addresses."
msgstr ""

#: lib/email_unlockRequest.php:17
msgid "Do this if you still can't regain access using the link above. It causes everyone who is blocked or locked out to be able to access your site again."
msgstr ""

#: lib/email_unlockRequest.php:20
msgid "Click here to unlock all IP addresses and disable the Wordfence Firewall and Wordfence login security for all users"
msgstr ""

#: lib/email_unlockRequest.php:20
msgid "Do this if you keep getting locked out or blocked and can't access your site. You can re-enable login security and the firewall once you sign-in to the site by visiting the Wordfence Firewall menu, clicking and then turning on the firewall and login security options. If you use country blocking, you will also need to choose which countries to block."
msgstr ""

#. translators: 1. IP address. 2. Site URL. 3. Site name.
#: lib/email_unsubscribeRequest.php:4
msgid "Either you or someone at IP address <b>%1$s</b> requested an alert unsubscribe link for the website <a href=\"%2$s\"><b>%3$s</b></a>."
msgstr ""

#: lib/email_unsubscribeRequest.php:10
msgid "If you did not request this, you can safely ignore it."
msgstr ""

#. translators: URL to WordPress admin panel.
#: lib/email_unsubscribeRequest.php:14
msgid "<a href=\"%s\" target=\"_blank\">Click here<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: IP address.
#: lib/IPTraf.php:11
msgid "Wordfence: All recent hits for IP address %s"
msgstr ""

#. translators: 1. year (2011). 2. year (2020)
#: lib/IPTraf.php:14
#: lib/sysinfo.php:20
#: lib/wfViewResult.php:25
msgid "&copy;&nbsp;%d to %d Wordfence &mdash; Visit <a href=\"https://www.wordfence.com/\">Wordfence.com</a> for help, security updates and more."
msgstr ""

#: lib/IPTrafList.php:13
#: lib/wfLockedOut.php:377
msgid "Time:"
msgstr ""

#. translators: 1. Time ago, example: 2 hours, 40 seconds. 2. Localized date. 3. Unix timestamp.
#: lib/IPTrafList.php:16
msgid "%1$s ago -- %2$s -- %3$s in Unixtime"
msgstr ""

#: lib/IPTrafList.php:19
msgid "Seconds since last hit:"
msgstr ""

#: lib/IPTrafList.php:22
#: lib/menu_tools_livetraffic.php:251
msgid "URL:"
msgstr ""

#: lib/IPTrafList.php:31
msgid "Page not found"
msgstr ""

#: lib/IPTrafList.php:34
msgid "Normal request"
msgstr ""

#: lib/IPTrafList.php:39
msgid "Referrer:"
msgstr ""

#: lib/IPTrafList.php:44
msgid "Full Browser ID:"
msgstr ""

#: lib/IPTrafList.php:49
msgid "User:"
msgstr ""

#: lib/IPTrafList.php:57
msgid "Location:"
msgstr ""

#: lib/live_activity.php:5
msgid "Wordfence Live Activity:"
msgstr ""

#: lib/live_activity.php:9
msgid "Live Updates Paused &mdash; Click inside window to resume"
msgstr ""

#: lib/menu_dashboard.php:20
#: lib/wordfenceClass.php:6885
msgid "Wordfence Dashboard"
msgstr ""

#: lib/menu_dashboard.php:22
msgid "Learn more<span class=\"wf-hidden-xs\"> about the Dashboard</span>"
msgstr ""

#: lib/menu_dashboard.php:50
#: lib/menu_dashboard_options.php:94
#: lib/menu_firewall.php:16
#: lib/menu_firewall.php:26
#: lib/menu_tools_auditlog.php:205
#: lib/wordfenceClass.php:6889
#: models/page/wfPage.php:105
msgid "Firewall"
msgstr ""

#: lib/menu_dashboard.php:51
#: lib/menu_dashboard_options.php:95
msgid "WAF Currently in Learning Mode"
msgstr ""

#: lib/menu_dashboard.php:51
#: lib/menu_dashboard_options.php:95
msgid "Protection from known and emerging threats"
msgstr ""

#: lib/menu_dashboard.php:53
#: lib/menu_dashboard_options.php:97
#: views/waf/firewall-status.php:70
msgid "Manage Firewall"
msgstr ""

#: lib/menu_dashboard.php:54
#: lib/menu_dashboard_options.php:98
msgid "Firewall Status"
msgstr ""

#: lib/menu_dashboard.php:57
#: lib/menu_dashboard.php:73
#: lib/menu_dashboard_options.php:101
#: lib/menu_dashboard_options.php:117
msgid "https://www.wordfence.com/help/dashboard/#dashboard-status"
msgstr ""

#: lib/menu_dashboard.php:67
#: lib/menu_dashboard_options.php:111
#: lib/menu_scanner.php:27
#: lib/menu_scanner.php:296
#: lib/wordfenceClass.php:6896
#: models/page/wfPage.php:113
msgid "Scan"
msgstr ""

#: lib/menu_dashboard.php:68
#: lib/menu_dashboard_options.php:112
msgid "Detection of security issues"
msgstr ""

#: lib/menu_dashboard.php:70
#: lib/menu_dashboard_options.php:114
#: lib/menu_scanner.php:60
msgid "Manage Scan"
msgstr ""

#: lib/menu_dashboard.php:71
#: lib/menu_dashboard_options.php:115
#: lib/menu_options.php:89
#: lib/menu_scanner.php:61
#: lib/menu_scanner_options.php:116
#: views/dashboard/options-group-dashboard.php:97
msgid "Scan Status"
msgstr ""

#: lib/menu_dashboard.php:82
#: lib/menu_dashboard_options.php:126
msgid "Premium License Conflict"
msgstr ""

#: lib/menu_dashboard.php:83
#: lib/menu_dashboard_options.php:127
#: views/dashboard/options-group-license.php:47
msgid "License already in use"
msgstr ""

#: lib/menu_dashboard.php:85
#: lib/menu_dashboard_options.php:129
#: views/dashboard/options-group-license.php:65
msgid "Reset License"
msgstr ""

#: lib/menu_dashboard.php:93
#: lib/menu_dashboard.php:104
#: lib/menu_dashboard.php:112
#: lib/menu_dashboard_options.php:137
#: lib/menu_dashboard_options.php:148
#: lib/menu_dashboard_options.php:156
#: views/scanner/scanner-status.php:65
#: views/waf/firewall-status.php:78
msgid "Premium Protection Disabled"
msgstr ""

#: lib/menu_dashboard.php:94
#: lib/menu_dashboard_options.php:138
msgid "License is expired"
msgstr ""

#: lib/menu_dashboard.php:96
#: lib/menu_dashboard.php:123
#: lib/menu_dashboard_options.php:140
#: lib/menu_dashboard_options.php:167
msgid "Renew License"
msgstr ""

#: lib/menu_dashboard.php:105
#: lib/menu_dashboard_options.php:149
msgid "The license you were using has been removed from your account. Please reach out to <a href=\"mailto:<EMAIL>\"><EMAIL></a> or create a Premium support case at <a href=\"https://support.wordfence.com/support/tickets\" target=\"_blank\">https://support.wordfence.com/support/tickets<span class=\"screen-reader-text\"> (opens in new tab)</span></a> for more information. Our staff is happy to help."
msgstr ""

#: lib/menu_dashboard.php:113
#: lib/menu_dashboard_options.php:157
#: views/waf/firewall-status.php:79
msgid "As a free Wordfence user, you are currently using the Community version of the Threat Defense Feed. Premium users are protected by additional firewall rules and malware signatures. Upgrade to Premium today to improve your protection."
msgstr ""

#: lib/menu_dashboard.php:114
#: lib/menu_dashboard_options.php:158
#: lib/menu_firewall_waf.php:55
#: lib/menu_firewall_waf.php:72
#: lib/menu_scanner.php:76
#: lib/menu_support.php:53
#: lib/menu_tools_auditlog.php:130
#: lib/menu_tools_twoFactor.php:51
#: lib/wordfenceClass.php:6934
#: views/blocking/blocking-create.php:179
#: views/blocking/blocking-status.php:23
#: views/blocking/blocking-status.php:27
#: views/blocking/options-group-advanced-country.php:85
#: views/dashboard/options-group-dashboard.php:107
#: views/dashboard/options-group-license.php:72
#: views/onboarding/modal-final-attempt.php:29
#: views/scanner/scanner-status.php:67
#: views/tools/options-group-2fa.php:40
#: views/waf/firewall-status.php:80
#: views/waf/options-group-basic-firewall.php:471
msgid "Upgrade to Premium"
msgstr ""

#: lib/menu_dashboard.php:120
#: lib/menu_dashboard.php:179
#: lib/menu_dashboard_options.php:164
#: lib/menu_dashboard_options.php:221
msgid "Premium License Expiring"
msgstr ""

#: lib/menu_dashboard.php:121
#: lib/menu_dashboard_options.php:165
msgid "Auto-renew is disabled"
msgstr ""

#: lib/menu_dashboard.php:130
#: lib/menu_dashboard_options.php:174
msgid "Payment Method Expiring"
msgstr ""

#: lib/menu_dashboard.php:133
#: lib/menu_dashboard_options.php:177
msgid "Payment Method Expired"
msgstr ""

#: lib/menu_dashboard.php:136
#: lib/menu_dashboard_options.php:180
msgid "Payment Method Missing"
msgstr ""

#: lib/menu_dashboard.php:139
#: lib/menu_dashboard_options.php:183
msgid "Payment Method Invalid"
msgstr ""

#: lib/menu_dashboard.php:145
#: lib/menu_dashboard.php:168
msgid "License renews today"
msgstr ""

#: lib/menu_dashboard.php:148
msgid "License renews tomorrow"
msgstr ""

#. translators: Number of days
#: lib/menu_dashboard.php:153
#: lib/menu_dashboard.php:174
msgid "License renews in %d days"
msgstr ""

#: lib/menu_dashboard.php:161
#: lib/menu_dashboard_options.php:203
msgid "Update Payment Method"
msgstr ""

#: lib/menu_dashboard.php:171
msgid "License renews in 1 day"
msgstr ""

#: lib/menu_dashboard.php:182
#: lib/menu_dashboard_options.php:224
msgid "Review Payment Method"
msgstr ""

#: lib/menu_dashboard.php:190
#: lib/menu_dashboard_options.php:232
msgid "%s Enabled"
msgstr ""

#: lib/menu_dashboard.php:194
#: lib/menu_dashboard_options.php:236
msgid "Learn about Wordfence Care and Wordfence Response"
msgstr ""

#: lib/menu_dashboard.php:196
#: lib/menu_dashboard_options.php:238
msgid "Learn about Wordfence Response"
msgstr ""

#: lib/menu_dashboard.php:225
#: lib/wordfenceClass.php:6900
msgid "Tools"
msgstr ""

#: lib/menu_dashboard.php:226
msgid "Live Traffic, Whois Lookup, Import/Export, and Diagnostics"
msgstr ""

#: lib/menu_dashboard.php:236
#: lib/menu_firewall_waf.php:157
#: lib/menu_scanner.php:129
#: lib/menu_support.php:17
#: lib/wordfenceClass.php:6916
msgid "Help"
msgstr ""

#: lib/menu_dashboard.php:237
#: lib/menu_firewall_waf.php:158
#: lib/menu_scanner.php:130
msgid "Find the documentation and help you need"
msgstr ""

#: lib/menu_dashboard.php:247
#: models/page/wfPage.php:103
msgid "Global Options"
msgstr ""

#: lib/menu_dashboard.php:248
msgid "Manage global options for Wordfence such as alerts, premium status, and more"
msgstr ""

#: lib/menu_dashboard.php:299
msgid "This is your Dashboard"
msgstr ""

#: lib/menu_dashboard.php:300
msgid "The Wordfence Dashboard provides valuable insights into the current state of your site's security. You'll find useful data summarized here as well as important status updates and notifications."
msgstr ""

#: lib/menu_dashboard.php:307
#: lib/menu_dashboard.php:323
#: lib/menu_dashboard.php:386
#: lib/menu_dashboard.php:403
#: lib/menu_dashboard.php:421
#: lib/menu_firewall_blocking.php:127
#: lib/menu_firewall_blocking.php:143
#: lib/menu_firewall_blocking.php:211
#: lib/menu_firewall_waf.php:255
#: lib/menu_firewall_waf.php:272
#: lib/menu_firewall_waf.php:289
#: lib/menu_scanner.php:304
#: lib/menu_scanner.php:321
#: lib/menu_scanner.php:373
#: views/tours/login-security.php:37
#: views/tours/login-security.php:53
msgid "Next"
msgstr ""

#: lib/menu_dashboard.php:314
msgid "Easily Monitor Your Wordfence Protection"
msgstr ""

#: lib/menu_dashboard.php:315
msgid "Each feature contains a status that reminds you what's enabled, disabled or needs attention. The Notifications section will highlight actions you need to take."
msgstr ""

#: lib/menu_dashboard.php:322
#: lib/menu_dashboard.php:339
#: lib/menu_dashboard.php:402
#: lib/menu_dashboard.php:420
#: lib/menu_dashboard.php:437
#: lib/menu_firewall_blocking.php:142
#: lib/menu_firewall_blocking.php:158
#: lib/menu_firewall_blocking.php:225
#: lib/menu_firewall_waf.php:271
#: lib/menu_firewall_waf.php:288
#: lib/menu_firewall_waf.php:306
#: lib/menu_scanner.php:320
#: lib/menu_scanner.php:336
#: lib/menu_scanner.php:387
#: views/tours/login-security.php:52
#: views/tours/login-security.php:69
msgid "Previous"
msgstr ""

#: lib/menu_dashboard.php:330
#: lib/menu_dashboard.php:410
msgid "Global Wordfence Options"
msgstr ""

#: lib/menu_dashboard.php:332
msgid "You'll find this icon throughout the plugin. Clicking it will show you the options and features for each section of Wordfence. From the dashboard, you can find the <strong>Global Options</strong> for Wordfence such as alerts, automatic updates, and managing your site's Premium License."
msgstr ""

#: lib/menu_dashboard.php:340
#: lib/menu_dashboard.php:438
#: lib/menu_firewall_blocking.php:159
#: lib/menu_firewall_blocking.php:226
#: lib/menu_firewall_waf.php:307
#: lib/menu_firewall_waf.php:354
#: lib/menu_scanner.php:337
#: lib/menu_scanner.php:388
#: lib/menu_tools_auditlog.php:245
#: lib/menu_tools_auditlog.php:276
#: lib/menu_tools_livetraffic.php:577
#: lib/menu_tools_livetraffic.php:608
msgid "Got it"
msgstr ""

#. translators: Wordfence version.
#: lib/menu_dashboard.php:376
msgid "You have successfully updated to Wordfence %s"
msgstr ""

#: lib/menu_dashboard.php:377
msgid "This update includes a number of significant interface changes. We'd like to walk you through  some of them, but you can bypass the tour for a section at any time by closing the dialogs."
msgstr ""

#: lib/menu_dashboard.php:378
msgid "We welcome your feedback and comments at <a href=\"mailto:<EMAIL>\"><EMAIL></a>. For a deeper dive on all of the changes, <a href=\"https://www.wordfence.com/blog/2018/01/introducing-wordfence-7/\" target=\"_blank\" rel=\"noopener noreferrer\">click here<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: lib/menu_dashboard.php:393
msgid "Monitor Your Wordfence Protection"
msgstr ""

#: lib/menu_dashboard.php:394
msgid "Each feature contains a status percentage reminding you at a high level of what's enabled, disabled, or needing your attention. The Notifications section highlights actions you need to take."
msgstr ""

#: lib/menu_dashboard.php:412
msgid "Manage your Wordfence license, see alerts and automatic plugin updates, and import/export your settings."
msgstr ""

#: lib/menu_dashboard.php:428
msgid "Updated Navigation"
msgstr ""

#: lib/menu_dashboard.php:429
msgid "The main navigation no longer includes an <strong>Options</strong> link. Options are now accessed via the <strong>Options</strong> link on each feature's main page. Live Traffic is now located in the Tools section, and blocking is found under the Firewall. Shortcuts to add a <strong>Blocking</strong> link back to the main navigation are available under Blocking options."
msgstr ""

#: lib/menu_dashboard.php:478
msgid "Recommended Settings Change"
msgstr ""

#: lib/menu_dashboard.php:479
msgid "Greetings! The default configuration for Wordfence Live Traffic has changed. The new default saves only logins and blocked requests, while this site is currently recording all traffic. Would you like to change to the new default?"
msgstr ""

#: lib/menu_dashboard.php:479
msgid "Rate limiting based on type of request (human vs crawler) may be less accurate because this prevents loading the extra JavaScript used for that identification."
msgstr ""

#: lib/menu_dashboard.php:480
msgid "Yes Please"
msgstr ""

#: lib/menu_dashboard.php:482
#: views/onboarding/modal-final-attempt.php:31
msgid "No Thanks"
msgstr ""

#: lib/menu_dashboard_options.php:11
#: lib/menu_dashboard_options.php:75
#: lib/menu_options.php:274
msgid "Wordfence Global Options"
msgstr ""

#: lib/menu_dashboard_options.php:48
msgid "Back<span class=\"wf-hidden-xs\"> to Dashboard</span>"
msgstr ""

#: lib/menu_dashboard_options.php:50
msgid "Are you sure you want to restore the default global settings? This will undo any custom changes you have made to the options on this page. Your configured license key and alert emails will not be changed."
msgstr ""

#: lib/menu_dashboard_options.php:77
msgid "Learn more<span class=\"wf-hidden-xs\"> about Global Options</span>"
msgstr ""

#: lib/menu_dashboard_options.php:189
#: lib/menu_dashboard_options.php:210
msgid "today"
msgstr ""

#: lib/menu_dashboard_options.php:192
msgid "tomorrow"
msgstr ""

#. translators: Number of days
#: lib/menu_dashboard_options.php:195
#: lib/menu_dashboard_options.php:216
msgid "in %d days"
msgstr ""

#: lib/menu_dashboard_options.php:201
#: lib/menu_dashboard_options.php:222
msgid "License renews %s"
msgstr ""

#: lib/menu_dashboard_options.php:213
msgid "in 1 day"
msgstr ""

#: lib/menu_dashboard_options.php:283
#: lib/menu_dashboard_options.php:293
#: lib/menu_options.php:416
#: lib/menu_options.php:426
#: lib/menu_tools.php:24
#: lib/menu_tools_importExport.php:7
#: lib/menu_tools_importExport.php:13
#: models/page/wfPage.php:121
#: views/dashboard/options-group-import.php:22
msgid "Import/Export Options"
msgstr ""

#: lib/menu_dashboard_options.php:291
msgid "Importing and exporting of options has moved to the Tools page"
msgstr ""

#: lib/menu_firewall.php:16
#: lib/menu_firewall_waf.php:35
#: lib/menu_firewall_waf_options.php:142
msgid "Web Application Firewall"
msgstr ""

#: lib/menu_firewall.php:17
#: lib/menu_firewall.php:37
#: lib/menu_firewall_blocking.php:119
#: lib/menu_firewall_waf.php:142
#: lib/wordfenceClass.php:6891
#: models/page/wfPage.php:109
msgid "Blocking"
msgstr ""

#: lib/menu_firewall.php:29
#: lib/menu_firewall_waf_options.php:125
msgid "Learn more<span class=\"wf-hidden-xs\"> about the Firewall</span>"
msgstr ""

#: lib/menu_firewall.php:40
#: lib/menu_firewall_blocking_options.php:90
msgid "Learn more<span class=\"wf-hidden-xs\"> about Blocking</span>"
msgstr ""

#: lib/menu_firewall_blocking.php:25
msgid "<strong>Note:</strong> Blocking is disabled when the option \"Enable Rate Limiting and Advanced Blocking\" is off."
msgstr ""

#: lib/menu_firewall_blocking.php:26
#: lib/menu_tools_twoFactor.php:79
msgid "Turn On"
msgstr ""

#. translators: PHP version.
#: lib/menu_firewall_blocking.php:33
msgid "<strong>Note:</strong> The GeoIP database that is required for country blocking has been updated to a new format. This new format requires sites to run PHP 5.4 or newer, and this site is on PHP %s. To ensure country blocking continues functioning, please update PHP."
msgstr ""

#: lib/menu_firewall_blocking.php:34
msgid "More Information"
msgstr ""

#: lib/menu_firewall_blocking.php:40
msgid "Create a Blocking Rule"
msgstr ""

#: lib/menu_firewall_blocking.php:40
msgid "Edit Blocking Rule"
msgstr ""

#: lib/menu_firewall_blocking.php:120
msgid "Wordfence lets you take control of protecting your site with powerful blocking features. Block traffic based on IP, IP range, hostname, browser, or referrer. Country blocking is available for Premium customers."
msgstr ""

#: lib/menu_firewall_blocking.php:134
#: lib/menu_firewall_blocking.php:204
msgid "Blocking Builder"
msgstr ""

#: lib/menu_firewall_blocking.php:135
msgid "All of your blocking rules are in one central location. Choose the Block Type, then enter the details for the rule. Once it has been added, you'll see it saved as a rule for your site."
msgstr ""

#: lib/menu_firewall_blocking.php:150
#: lib/menu_firewall_blocking.php:218
msgid "Manage Blocking Rules"
msgstr ""

#: lib/menu_firewall_blocking.php:151
msgid "Here's where you'll see all the blocking rules you've created. You can also manage them as well as remove or modify them from this table."
msgstr ""

#: lib/menu_firewall_blocking.php:205
msgid "All of the blocking rules you create are now in one central location. Simply choose the block type and enter the details for the rule you want to create. Premium users have access to advanced country blocking options, found via the <strong>Options</strong> link."
msgstr ""

#: lib/menu_firewall_blocking.php:219
msgid "All blocking rules you create will show here. You can manage them as well as remove or modify them from the same location."
msgstr ""

#: lib/menu_firewall_blocking_options.php:12
#: lib/menu_firewall_blocking_options.php:88
#: lib/menu_options.php:342
#: models/page/wfPage.php:111
#: views/blocking/blocking-status.php:14
msgid "Blocking Options"
msgstr ""

#. translators: Page title/label.
#: lib/menu_firewall_blocking_options.php:51
#: lib/menu_firewall_waf_options.php:83
#: lib/menu_scanner_options.php:66
msgid "<span class=\"wf-hidden-xs\">Back to </span>%s"
msgstr ""

#: lib/menu_firewall_blocking_options.php:53
msgid "Are you sure you want to restore the default Blocking settings? This will undo any custom changes you have made to the options on this page. Any existing blocks will be preserved."
msgstr ""

#: lib/menu_firewall_blocking_options.php:100
#: modules/login-security/views/settings/options.php:203
msgid "General"
msgstr ""

#: lib/menu_firewall_blocking_options.php:114
msgid "Display Blocking menu option"
msgstr ""

#: lib/menu_firewall_waf.php:36
#: lib/menu_firewall_waf.php:53
#: lib/menu_firewall_waf_options.php:143
#: lib/menu_firewall_waf_options.php:160
msgid "Currently in Learning Mode"
msgstr ""

#: lib/menu_firewall_waf.php:36
#: lib/menu_firewall_waf_options.php:143
msgid "Stops Complex Attacks"
msgstr ""

#: lib/menu_firewall_waf.php:38
msgid "Manage WAF"
msgstr ""

#: lib/menu_firewall_waf.php:39
#: lib/menu_firewall_waf_options.php:146
#: lib/menu_options.php:106
#: views/waf/options-group-basic-firewall.php:34
msgid "Web Application Firewall Status"
msgstr ""

#: lib/menu_firewall_waf.php:42
#: lib/menu_firewall_waf.php:60
#: lib/menu_firewall_waf.php:76
#: lib/menu_firewall_waf.php:108
msgid "https://www.wordfence.com/help/firewall/#firewall-status"
msgstr ""

#: lib/menu_firewall_waf.php:52
#: lib/menu_firewall_waf_options.php:159
msgid "Firewall Rules: "
msgstr ""

#: lib/menu_firewall_waf.php:52
#: lib/menu_firewall_waf_options.php:159
#: lib/menu_scanner.php:73
#: lib/menu_scanner_options.php:128
msgid "Community"
msgstr ""

#: lib/menu_firewall_waf.php:53
#: lib/menu_firewall_waf_options.php:160
msgid "Rules updated in real-time"
msgstr ""

#: lib/menu_firewall_waf.php:53
#: lib/menu_firewall_waf_options.php:160
msgid "Rule updates delayed by 30 days"
msgstr ""

#: lib/menu_firewall_waf.php:55
msgid "Manage Firewall Rules"
msgstr ""

#: lib/menu_firewall_waf.php:57
#: lib/menu_firewall_waf_options.php:164
msgid "Firewall Rules Status"
msgstr ""

#: lib/menu_firewall_waf.php:69
#: lib/menu_firewall_waf_options.php:176
msgid "Real-Time IP Blocklist: "
msgstr ""

#: lib/menu_firewall_waf.php:69
#: lib/menu_firewall_waf_options.php:176
#: lib/wfDiagnostic.php:970
#: lib/wfDiagnostic.php:1064
#: lib/wfDiagnostic.php:1069
#: lib/wfDiagnostic.php:1073
#: lib/wfDiagnostic.php:1133
#: lib/wfDiagnostic.php:1134
#: lib/wfDiagnostic.php:1135
#: lib/wfDiagnostic.php:1136
#: lib/wfDiagnostic.php:1137
#: lib/wfDiagnostic.php:1138
#: lib/wfDiagnostic.php:1139
#: lib/wfDiagnostic.php:1140
#: lib/wfDiagnostic.php:1141
#: lib/wfDiagnostic.php:1142
#: lib/wfDiagnostic.php:1143
#: lib/wfDiagnostic.php:1144
#: lib/wfDiagnostic.php:1145
#: lib/wfDiagnostic.php:1146
#: lib/wfDiagnostic.php:1147
#: lib/wfDiagnostic.php:1148
#: lib/wfDiagnostic.php:1149
#: lib/wfDiagnostic.php:1150
#: lib/wfDiagnostic.php:1151
#: lib/wfDiagnostic.php:1152
#: lib/wfDiagnostic.php:1153
#: lib/wfDiagnostic.php:1154
#: lib/wfDiagnostic.php:1155
#: lib/wfDiagnostic.php:1156
#: lib/wfDiagnostic.php:1163
#: lib/wfDiagnostic.php:1167
#: lib/wfDiagnostic.php:1168
#: lib/wfDiagnostic.php:1169
#: lib/wfDiagnostic.php:1170
#: lib/wfDiagnostic.php:1171
#: lib/wfDiagnostic.php:1174
#: lib/wfDiagnostic.php:1179
#: lib/wfDiagnostic.php:1181
#: lib/wfDiagnostic.php:1182
#: lib/wfDiagnostic.php:1183
#: lib/wfDiagnostic.php:1184
#: lib/wfDiagnostic.php:1185
#: lib/wfDiagnostic.php:1186
#: lib/wfDiagnostic.php:1187
#: lib/wfDiagnostic.php:1189
#: lib/wfDiagnostic.php:1190
#: lib/wfDiagnostic.php:1193
#: lib/wfDiagnostic.php:1218
#: models/firewall/wfFirewall.php:35
#: views/scanner/scan-scheduling.php:15
#: views/waf/options-group-basic-firewall.php:481
#: views/waf/options-group-whitelisted.php:81
#: views/waf/options-group-whitelisted.php:94
msgid "Enabled"
msgstr ""

#: lib/menu_firewall_waf.php:69
#: lib/menu_firewall_waf_options.php:176
#: lib/wfDiagnostic.php:1064
#: lib/wfDiagnostic.php:1069
#: lib/wfDiagnostic.php:1073
#: lib/wfDiagnostic.php:1133
#: lib/wfDiagnostic.php:1134
#: lib/wfDiagnostic.php:1135
#: lib/wfDiagnostic.php:1136
#: lib/wfDiagnostic.php:1137
#: lib/wfDiagnostic.php:1138
#: lib/wfDiagnostic.php:1139
#: lib/wfDiagnostic.php:1140
#: lib/wfDiagnostic.php:1141
#: lib/wfDiagnostic.php:1142
#: lib/wfDiagnostic.php:1143
#: lib/wfDiagnostic.php:1144
#: lib/wfDiagnostic.php:1145
#: lib/wfDiagnostic.php:1146
#: lib/wfDiagnostic.php:1147
#: lib/wfDiagnostic.php:1148
#: lib/wfDiagnostic.php:1149
#: lib/wfDiagnostic.php:1150
#: lib/wfDiagnostic.php:1151
#: lib/wfDiagnostic.php:1152
#: lib/wfDiagnostic.php:1153
#: lib/wfDiagnostic.php:1154
#: lib/wfDiagnostic.php:1155
#: lib/wfDiagnostic.php:1156
#: lib/wfDiagnostic.php:1163
#: lib/wfDiagnostic.php:1167
#: lib/wfDiagnostic.php:1168
#: lib/wfDiagnostic.php:1169
#: lib/wfDiagnostic.php:1170
#: lib/wfDiagnostic.php:1171
#: lib/wfDiagnostic.php:1174
#: lib/wfDiagnostic.php:1179
#: lib/wfDiagnostic.php:1181
#: lib/wfDiagnostic.php:1182
#: lib/wfDiagnostic.php:1183
#: lib/wfDiagnostic.php:1184
#: lib/wfDiagnostic.php:1185
#: lib/wfDiagnostic.php:1186
#: lib/wfDiagnostic.php:1187
#: lib/wfDiagnostic.php:1189
#: lib/wfDiagnostic.php:1190
#: lib/wfDiagnostic.php:1193
#: lib/wfDiagnostic.php:1218
#: lib/wordfenceClass.php:6326
#: models/firewall/wfFirewall.php:41
#: modules/login-security/classes/controller/wordfencels.php:506
#: modules/login-security/views/options/option-roles.php:8
#: views/scanner/scan-scheduling.php:14
#: views/tools/options-group-audit-log.php:68
#: views/user/disabled-application-passwords.php:12
#: views/waf/options-group-basic-firewall.php:48
#: views/waf/options-group-basic-firewall.php:480
msgid "Disabled"
msgstr ""

#: lib/menu_firewall_waf.php:70
#: lib/menu_firewall_waf_options.php:177
msgid "Blocks requests from known malicious IPs"
msgstr ""

#: lib/menu_firewall_waf.php:72
msgid "Manage Real-Time IP Blocklist"
msgstr ""

#: lib/menu_firewall_waf.php:72
#: views/waf/option-whitelist.php:102
msgid "Enable"
msgstr ""

#: lib/menu_firewall_waf.php:74
#: lib/menu_firewall_waf_options.php:181
msgid "Blocklist Status"
msgstr ""

#: lib/menu_firewall_waf.php:102
#: lib/menu_firewall_waf.php:279
#: lib/menu_firewall_waf_options.php:192
#: lib/wfDiagnostic.php:1173
#: views/waf/options-group-brute-force.php:26
msgid "Brute Force Protection"
msgstr ""

#: lib/menu_firewall_waf.php:102
#: lib/menu_firewall_waf_options.php:192
msgid ": Disabled"
msgstr ""

#: lib/menu_firewall_waf.php:103
#: lib/menu_firewall_waf_options.php:193
msgid "Stops Password Guessing Attacks"
msgstr ""

#: lib/menu_firewall_waf.php:105
msgid "Manage Brute Force Protection"
msgstr ""

#: lib/menu_firewall_waf.php:106
#: lib/menu_firewall_waf_options.php:196
msgid "Brute Force Protection Status"
msgstr ""

#: lib/menu_firewall_waf.php:131
#: lib/wfDiagnostic.php:1192
#: views/waf/options-group-rate-limiting.php:26
msgid "Rate Limiting"
msgstr ""

#: lib/menu_firewall_waf.php:132
msgid "Block crawlers that are using too many resources or stealing content"
msgstr ""

#: lib/menu_firewall_waf.php:143
msgid "Block traffic by country, IP, IP range, user agent, referrer, or hostname"
msgstr ""

#: lib/menu_firewall_waf.php:168
msgid "All Firewall Options"
msgstr ""

#: lib/menu_firewall_waf.php:169
msgid "Manage global and advanced firewall options"
msgstr ""

#: lib/menu_firewall_waf.php:246
msgid "The Wordfence firewall protects your sites from attackers"
msgstr ""

#: lib/menu_firewall_waf.php:247
msgid "This is where you can monitor the work Wordfence is doing to protect your site and also where you can manage the options to optimize the firewall's configuration."
msgstr ""

#: lib/menu_firewall_waf.php:262
msgid "Web Application Firewall (WAF)"
msgstr ""

#: lib/menu_firewall_waf.php:263
msgid "The Wordfence Web Application Firewall blocks known and emerging attacks using firewall rules. When you first install the WAF, it will be in learning mode. This allows Wordfence to learn about your site so that we can understand how to protect it and how to allow normal visitors through the firewall. We recommend you let Wordfence learn for a week before you enable the firewall."
msgstr ""

#: lib/menu_firewall_waf.php:280
msgid "Wordfence protects your site from password-guessing attacks by locking out attackers and helping you avoid weak passwords."
msgstr ""

#: lib/menu_firewall_waf.php:296
#: lib/menu_firewall_waf.php:347
#: lib/menu_firewall_waf_options.php:23
#: lib/menu_firewall_waf_options.php:123
#: lib/menu_options.php:305
#: models/page/wfPage.php:107
msgid "Firewall Options"
msgstr ""

#: lib/menu_firewall_waf.php:298
msgid "Set up the way you want the firewall to protect your site including the web application firewall, brute force protection, rate limiting, and blocking."
msgstr ""

#: lib/menu_firewall_waf.php:349
msgid "All of the Firewall settings are now located here. This includes configuration options for the web application firewall, brute force protection, rate limiting, allowlisted URLs, and blocking."
msgstr ""

#: lib/menu_firewall_waf_options.php:85
msgid "Are you sure you want to restore the default Firewall settings? This will undo any custom changes you have made to the options on this page. If you have manually disabled any rules or added any custom allowlisted URLs, those changes will not be overwritten."
msgstr ""

#: lib/menu_install.php:5
msgid "Wordfence is already installed on this site. If you need to replace the current license, you may do so by visiting the \"All Options\" page of the Wordfence menu."
msgstr ""

#: lib/menu_install.php:9
msgid "Too many installation requests have been made from your IP address. Please try again later."
msgstr ""

#: lib/menu_install.php:12
msgid "The link you used to access this page has expired, has already been used, or is otherwise invalid."
msgstr ""

#: lib/menu_install.php:15
msgid "An error occurred while retrieving your license information from the Wordfence servers. Please ensure that your server can reach www.wordfence.com on port 443."
msgstr ""

#: lib/menu_install.php:26
#: lib/wordfenceClass.php:6927
msgid "Install Wordfence"
msgstr ""

#: lib/menu_options.php:24
#: lib/menu_options.php:264
#: lib/wordfenceClass.php:6911
msgid "All Options"
msgstr ""

#: lib/menu_options.php:68
#: views/dashboard/options-group-license.php:35
#: views/onboarding/registration-prompt.php:44
msgid "License Key"
msgstr ""

#: lib/menu_options.php:69
msgid "Display All Options menu item"
msgstr ""

#: lib/menu_options.php:70
msgid "Display Blocking menu item"
msgstr ""

#: lib/menu_options.php:71
msgid "Display Live Traffic menu item"
msgstr ""

#: lib/menu_options.php:72
msgid "Display Audit Log menu item"
msgstr ""

#: lib/menu_options.php:73
#: views/dashboard/options-group-general.php:44
msgid "Update Wordfence automatically when a new version is released?"
msgstr ""

#: lib/menu_options.php:74
#: views/dashboard/options-group-general.php:55
msgid "Where to email alerts"
msgstr ""

#: lib/menu_options.php:75
#: views/dashboard/option-howgetips.php:21
msgid "How does Wordfence get IPs"
msgstr ""

#: lib/menu_options.php:76
#: lib/menu_tools_diagnostic.php:236
#: modules/login-security/views/options/option-ip-source.php:47
#: views/dashboard/option-howgetips.php:50
#: views/diagnostics/text.php:124
msgid "Trusted Proxies"
msgstr ""

#: lib/menu_options.php:77
#: views/dashboard/options-group-general.php:73
msgid "Look up visitor IP locations via Wordfence servers"
msgstr ""

#: lib/menu_options.php:78
#: views/dashboard/options-group-general.php:86
msgid "Hide WordPress version"
msgstr ""

#: lib/menu_options.php:79
#: views/dashboard/options-group-general.php:98
msgid "Disable Code Execution for Uploads directory"
msgstr ""

#: lib/menu_options.php:80
#: views/dashboard/options-group-general.php:110
msgid "Pause live updates when window loses focus"
msgstr ""

#: lib/menu_options.php:81
#: views/dashboard/options-group-general.php:120
msgid "Update interval in seconds"
msgstr ""

#: lib/menu_options.php:82
#: views/dashboard/options-group-general.php:133
msgid "Bypass the LiteSpeed \"noabort\" check"
msgstr ""

#: lib/menu_options.php:83
#: views/dashboard/options-group-general.php:145
msgid "Delete Wordfence tables and data on deactivation"
msgstr ""

#: lib/menu_options.php:84
#: views/dashboard/options-group-dashboard.php:36
msgid "Updates Needed (Plugin, Theme, or Core)"
msgstr ""

#: lib/menu_options.php:85
#: views/dashboard/options-group-dashboard.php:48
msgid "Security Alerts"
msgstr ""

#: lib/menu_options.php:86
#: views/dashboard/options-group-dashboard.php:60
msgid "Promotions"
msgstr ""

#: lib/menu_options.php:87
#: views/dashboard/options-group-dashboard.php:72
msgid "Blog Highlights"
msgstr ""

#: lib/menu_options.php:88
#: views/dashboard/options-group-dashboard.php:84
msgid "Product Updates"
msgstr ""

#: lib/menu_options.php:90
#: views/dashboard/options-group-alert.php:36
msgid "Email me when Wordfence is automatically updated"
msgstr ""

#: lib/menu_options.php:91
#: views/dashboard/options-group-alert.php:48
msgid "Email me if Wordfence is deactivated"
msgstr ""

#: lib/menu_options.php:92
#: views/dashboard/options-group-alert.php:59
msgid "Email me if the Wordfence Web Application Firewall is turned off"
msgstr ""

#: lib/menu_options.php:93
msgid "Alert me with scan results of this severity level or greater"
msgstr ""

#: lib/menu_options.php:94
#: views/dashboard/options-group-alert.php:90
msgid "Alert when an IP address is blocked"
msgstr ""

#: lib/menu_options.php:95
#: views/dashboard/options-group-alert.php:101
msgid "Alert when someone is locked out from login"
msgstr ""

#: lib/menu_options.php:96
#: views/dashboard/options-group-alert.php:123
msgid "Alert when the \"lost password\" form is used for a valid user"
msgstr ""

#: lib/menu_options.php:97
#: views/dashboard/options-group-alert.php:134
msgid "Alert me when someone with administrator access signs in"
msgstr ""

#: lib/menu_options.php:98
#: views/dashboard/options-group-alert.php:140
msgid "Only alert me when that administrator signs in from a new device"
msgstr ""

#: lib/menu_options.php:99
#: views/dashboard/options-group-alert.php:151
msgid "Alert me when a non-admin user signs in"
msgstr ""

#: lib/menu_options.php:100
#: views/dashboard/options-group-alert.php:157
msgid "Only alert me when that user signs in from a new device"
msgstr ""

#: lib/menu_options.php:101
#: views/dashboard/options-group-alert.php:168
msgid "Alert me when there's a large increase in attacks detected on my site"
msgstr ""

#: lib/menu_options.php:102
#: views/dashboard/options-group-alert.php:177
msgid "Maximum email alerts to send per hour"
msgstr ""

#: lib/menu_options.php:103
#: views/dashboard/options-group-email-summary.php:43
msgid "Enable email summary"
msgstr ""

#: lib/menu_options.php:104
#: views/dashboard/options-group-email-summary.php:52
msgid "List of directories to exclude from recently modified file list"
msgstr ""

#: lib/menu_options.php:105
#: views/dashboard/options-group-email-summary.php:63
msgid "Enable activity report widget on the WordPress dashboard"
msgstr ""

#: lib/menu_options.php:107
msgid "Web Application Firewall Protection Level"
msgstr ""

#: lib/menu_options.php:108
#: views/waf/options-group-basic-firewall.php:468
msgid "Real-Time IP Blocklist"
msgstr ""

#: lib/menu_options.php:109
#: views/waf/options-group-advanced-firewall.php:40
msgid "Delay IP and Country blocking until after WordPress and plugins have loaded (only process firewall rules early)"
msgstr ""

#: lib/menu_options.php:110
#: views/waf/options-group-advanced-firewall.php:52
msgid "Allowlisted IP addresses that bypass all rules"
msgstr ""

#: lib/menu_options.php:111
#: views/waf/options-group-advanced-firewall.php:88
msgid "Allowlisted services"
msgstr ""

#: lib/menu_options.php:112
#: views/waf/options-group-advanced-firewall.php:99
msgid "Immediately block IPs that access these URLs"
msgstr ""

#: lib/menu_options.php:113
#: views/waf/options-group-advanced-firewall.php:112
msgid "Ignored IP addresses for Wordfence Web Application Firewall alerting"
msgstr ""

#: lib/menu_options.php:114
msgid "Web Application Firewall Rules"
msgstr ""

#: lib/menu_options.php:115
#: lib/wfDiagnostic.php:1174
#: views/waf/options-group-brute-force.php:38
msgid "Enable brute force protection"
msgstr ""

#: lib/menu_options.php:116
#: lib/wfDiagnostic.php:1175
#: views/waf/options-group-brute-force.php:61
msgid "Lock out after how many login failures"
msgstr ""

#: lib/menu_options.php:117
#: lib/wfDiagnostic.php:1176
#: views/waf/options-group-brute-force.php:77
msgid "Lock out after how many forgot password attempts"
msgstr ""

#: lib/menu_options.php:118
#: lib/wfDiagnostic.php:1177
#: views/waf/options-group-brute-force.php:93
msgid "Count failures over what time period"
msgstr ""

#: lib/menu_options.php:119
#: lib/wfDiagnostic.php:1178
#: views/waf/options-group-brute-force.php:109
msgid "Amount of time a user is locked out"
msgstr ""

#: lib/menu_options.php:120
#: lib/wfDiagnostic.php:1179
#: views/waf/options-group-brute-force.php:121
msgid "Immediately lock out invalid usernames"
msgstr ""

#: lib/menu_options.php:121
#: lib/wfDiagnostic.php:1180
#: views/waf/options-group-brute-force.php:139
msgid "Immediately block the IP of users who try to sign in as these usernames"
msgstr ""

#: lib/menu_options.php:122
#: lib/wfDiagnostic.php:1182
#: views/waf/options-group-brute-force.php:178
msgid "Enforce strong passwords"
msgstr ""

#: lib/menu_options.php:123
#: lib/wfDiagnostic.php:1181
#: views/waf/options-group-brute-force.php:155
msgid "Prevent the use of passwords leaked in data breaches"
msgstr ""

#: lib/menu_options.php:124
#: lib/wfDiagnostic.php:1183
#: views/waf/options-group-brute-force.php:190
msgid "Don't let WordPress reveal valid users in login errors"
msgstr ""

#: lib/menu_options.php:125
msgid "Prevent users registering \"admin\" username if it doesn't exist"
msgstr ""

#: lib/menu_options.php:126
msgid "Prevent discovery of usernames through \"/?author=N\" scans, the oEmbed API, the WordPress REST API, and WordPress XML Sitemaps"
msgstr ""

#: lib/menu_options.php:127
#: lib/wfDiagnostic.php:1186
#: views/waf/options-group-brute-force.php:226
msgid "Disable WordPress application passwords"
msgstr ""

#: lib/menu_options.php:128
#: lib/wfDiagnostic.php:1187
#: views/waf/options-group-brute-force.php:238
msgid "Block IPs who send POST requests with blank User-Agent and Referer"
msgstr ""

#: lib/menu_options.php:129
#: lib/wfDiagnostic.php:1188
#: views/waf/options-group-brute-force.php:249
msgid "Custom text shown on block pages"
msgstr ""

#: lib/menu_options.php:130
#: lib/wfDiagnostic.php:1189
#: views/waf/options-group-brute-force.php:264
msgid "Check password strength on profile update"
msgstr ""

#: lib/menu_options.php:131
#: lib/wfDiagnostic.php:1190
#: views/waf/options-group-brute-force.php:276
msgid "Participate in the Real-Time Wordfence Security Network"
msgstr ""

#: lib/menu_options.php:132
#: lib/wfDiagnostic.php:1193
#: views/waf/options-group-rate-limiting.php:38
msgid "Enable Rate Limiting and Advanced Blocking"
msgstr ""

#: lib/menu_options.php:133
#: lib/wfDiagnostic.php:1194
#: views/waf/options-group-rate-limiting.php:60
msgid "How should we treat Google's crawlers"
msgstr ""

#: lib/menu_options.php:134
#: lib/wfDiagnostic.php:1196
#: views/waf/options-group-rate-limiting.php:100
msgid "If anyone's requests exceed"
msgstr ""

#: lib/menu_options.php:135
#: lib/wfDiagnostic.php:1199
#: views/waf/options-group-rate-limiting.php:117
msgid "If a crawler's page views exceed"
msgstr ""

#: lib/menu_options.php:136
#: lib/wfDiagnostic.php:1202
#: views/waf/options-group-rate-limiting.php:134
msgid "If a crawler's pages not found (404s) exceed"
msgstr ""

#: lib/menu_options.php:137
#: lib/wfDiagnostic.php:1205
#: views/waf/options-group-rate-limiting.php:151
msgid "If a human's page views exceed"
msgstr ""

#: lib/menu_options.php:138
#: lib/wfDiagnostic.php:1208
#: views/waf/options-group-rate-limiting.php:168
msgid "If a human's pages not found (404s) exceed"
msgstr ""

#: lib/menu_options.php:139
#: lib/wfDiagnostic.php:1211
#: views/waf/options-group-rate-limiting.php:184
msgid "How long is an IP address blocked when it breaks a rule"
msgstr ""

#: lib/menu_options.php:140
#: lib/wfDiagnostic.php:1212
#: views/waf/options-group-rate-limiting.php:194
msgid "Allowlisted 404 URLs"
msgstr ""

#: lib/menu_options.php:141
msgid "Web Application Firewall Allowlisted URLs"
msgstr ""

#: lib/menu_options.php:142
msgid "Monitor background requests from an administrator's web browser for false positives (Front-end Website)"
msgstr ""

#: lib/menu_options.php:143
msgid "Monitor background requests from an administrator's web browser for false positives (Admin Panel)"
msgstr ""

#: lib/menu_options.php:144
msgid "What to do when we block someone visiting from a blocked country"
msgstr ""

#: lib/menu_options.php:145
msgid "URL to redirect blocked countries to"
msgstr ""

#: lib/menu_options.php:146
#: lib/wfDiagnostic.php:1218
#: views/blocking/options-group-advanced-country.php:62
msgid "Block countries even if they are logged in"
msgstr ""

#: lib/menu_options.php:147
msgid "If user from a blocked country hits the relative URL ____ then redirect that user to ____ and set a cookie that will bypass all country blocking"
msgstr ""

#: lib/menu_options.php:148
msgid "If user who is allowed to access the site views the relative URL ____ then set a cookie that will bypass country blocking in future in case that user hits the site from a blocked country"
msgstr ""

#: lib/menu_options.php:149
#: views/scanner/scan-scheduling.php:12
msgid "Schedule Wordfence Scans"
msgstr ""

#: lib/menu_options.php:150
#: lib/wfDiagnostic.php:1132
msgid "Scan Type"
msgstr ""

#: lib/menu_options.php:151
#: lib/wfDiagnostic.php:1133
#: views/scanner/options-group-general.php:32
msgid "Check if this website is on a domain blocklist"
msgstr ""

#: lib/menu_options.php:152
msgid "Check if this website is being &quot;Spamvertised&quot;"
msgstr ""

#: lib/menu_options.php:153
#: lib/wfDiagnostic.php:1135
#: views/scanner/options-group-general.php:34
msgid "Check if this website IP is generating spam"
msgstr ""

#: lib/menu_options.php:154
#: lib/wfDiagnostic.php:1136
#: views/scanner/options-group-general.php:35
msgid "Scan for misconfigured How does Wordfence get IPs"
msgstr ""

#: lib/menu_options.php:155
#: lib/wfDiagnostic.php:1137
#: views/scanner/options-group-general.php:36
msgid "Scan for publicly accessible configuration, backup, or log files"
msgstr ""

#: lib/menu_options.php:156
#: lib/wfDiagnostic.php:1138
#: views/scanner/options-group-general.php:37
msgid "Scan for publicly accessible quarantined files"
msgstr ""

#: lib/menu_options.php:157
#: lib/wfDiagnostic.php:1139
#: views/scanner/options-group-general.php:38
msgid "Scan core files against repository versions for changes"
msgstr ""

#: lib/menu_options.php:158
#: lib/wfDiagnostic.php:1140
#: views/scanner/options-group-general.php:39
msgid "Scan theme files against repository versions for changes"
msgstr ""

#: lib/menu_options.php:159
#: lib/wfDiagnostic.php:1141
#: views/scanner/options-group-general.php:40
msgid "Scan plugin files against repository versions for changes"
msgstr ""

#: lib/menu_options.php:160
#: lib/wfDiagnostic.php:1142
#: views/scanner/options-group-general.php:41
msgid "Scan wp-admin and wp-includes for files not bundled with WordPress"
msgstr ""

#: lib/menu_options.php:161
#: lib/wfDiagnostic.php:1143
#: views/scanner/options-group-general.php:42
msgid "Scan for signatures of known malicious files"
msgstr ""

#: lib/menu_options.php:162
#: lib/wfDiagnostic.php:1144
#: views/scanner/options-group-general.php:43
msgid "Scan file contents for backdoors, trojans and suspicious code"
msgstr ""

#: lib/menu_options.php:163
#: lib/wfDiagnostic.php:1145
#: views/scanner/options-group-general.php:44
msgid "Scan file contents for malicious URLs"
msgstr ""

#: lib/menu_options.php:164
#: lib/wfDiagnostic.php:1146
#: views/scanner/options-group-general.php:45
msgid "Scan posts for known dangerous URLs and suspicious content"
msgstr ""

#: lib/menu_options.php:165
#: lib/wfDiagnostic.php:1147
#: views/scanner/options-group-general.php:46
msgid "Scan comments for known dangerous URLs and suspicious content"
msgstr ""

#: lib/menu_options.php:166
#: lib/wfDiagnostic.php:1148
#: views/scanner/options-group-general.php:47
msgid "Scan WordPress core, plugin, and theme options for known dangerous URLs and suspicious content"
msgstr ""

#: lib/menu_options.php:167
#: lib/wfDiagnostic.php:1149
#: views/scanner/options-group-general.php:48
msgid "Scan for out of date, abandoned, and vulnerable plugins, themes, and WordPress versions"
msgstr ""

#: lib/menu_options.php:168
#: lib/wfDiagnostic.php:1150
#: views/scanner/options-group-general.php:49
msgid "Scan for suspicious admin users created outside of WordPress"
msgstr ""

#: lib/menu_options.php:169
#: lib/wfDiagnostic.php:1151
#: views/scanner/options-group-general.php:50
msgid "Check the strength of passwords"
msgstr ""

#: lib/menu_options.php:170
#: lib/wfDiagnostic.php:1152
#: views/scanner/options-group-general.php:51
msgid "Monitor disk space"
msgstr ""

#: lib/menu_options.php:171
#: lib/wfDiagnostic.php:1153
#: views/scanner/options-group-general.php:52
msgid "Monitor Web Application Firewall status"
msgstr ""

#: lib/menu_options.php:172
#: lib/wfDiagnostic.php:1154
#: views/scanner/options-group-general.php:53
msgid "Scan files outside your WordPress installation"
msgstr ""

#: lib/menu_options.php:173
#: lib/wfDiagnostic.php:1155
#: views/scanner/options-group-general.php:54
msgid "Scan images, binary, and other files as if they were executable"
msgstr ""

#: lib/menu_options.php:174
#: lib/wfDiagnostic.php:1156
#: views/scanner/options-group-performance.php:32
msgid "Use low resource scanning (reduces server load by lengthening the scan duration)"
msgstr ""

#: lib/menu_options.php:175
#: lib/wfDiagnostic.php:1157
#: views/scanner/options-group-performance.php:33
msgid "Limit the number of issues sent in the scan results email"
msgstr ""

#: lib/menu_options.php:176
#: lib/wfDiagnostic.php:1158
#: views/scanner/options-group-performance.php:34
msgid "Time limit that a scan can run in seconds"
msgstr ""

#. translators: Time until.
#: lib/menu_options.php:177
#: lib/wfDiagnostic.php:1159
#: views/scanner/options-group-performance.php:35
msgid "How much memory should Wordfence request when scanning"
msgstr ""

#: lib/menu_options.php:178
msgid "Maximum execution time for each scan stage"
msgstr ""

#: lib/menu_options.php:179
msgid "Exclude files from scan that match these wildcard patterns"
msgstr ""

#: lib/menu_options.php:180
msgid "Additional scan signatures"
msgstr ""

#: lib/menu_options.php:181
#: lib/wfDiagnostic.php:1163
#: views/scanner/options-group-advanced.php:59
msgid "Use only IPv4 to start scans"
msgstr ""

#: lib/menu_options.php:182
#: lib/wfDiagnostic.php:1164
#: views/scanner/options-group-advanced.php:77
msgid "Maximum number of attempts to resume each scan stage"
msgstr ""

#: lib/menu_options.php:183
msgid "Traffic logging mode (Live Traffic)"
msgstr ""

#: lib/menu_options.php:184
#: views/tools/options-group-live-traffic.php:78
msgid "Don't log signed-in users with publishing access"
msgstr ""

#: lib/menu_options.php:185
#: views/tools/options-group-live-traffic.php:87
msgid "List of comma separated usernames to ignore"
msgstr ""

#: lib/menu_options.php:186
#: views/tools/options-group-live-traffic.php:96
msgid "List of comma separated IP addresses to ignore"
msgstr ""

#: lib/menu_options.php:187
#: views/tools/options-group-live-traffic.php:105
msgid "Browser user-agent to ignore"
msgstr ""

#: lib/menu_options.php:188
#: views/tools/options-group-live-traffic.php:114
msgid "Amount of Live Traffic data to store (number of rows)"
msgstr ""

#: lib/menu_options.php:189
msgid "Maximum days to keep Live Traffic data"
msgstr ""

#: lib/menu_options.php:190
#: views/tools/options-group-audit-log.php:66
msgid "Audit Log logging mode"
msgstr ""

#: lib/menu_options.php:191
#: views/dashboard/options-group-import.php:31
msgid "Export this site's Wordfence options for import on another site"
msgstr ""

#: lib/menu_options.php:192
#: views/dashboard/options-group-import.php:44
msgid "Import Wordfence options from another site using a token"
msgstr ""

#: lib/menu_options.php:196
msgid "Require Cellphone Sign-in for all Administrators"
msgstr ""

#: lib/menu_options.php:197
msgid "Enable Separate Prompt for Two Factor Code"
msgstr ""

#: lib/menu_options.php:206
msgid "Are you sure you want to restore the default settings? This will undo any custom changes you have made to the options on this page. If you have manually disabled any rules or added any custom allowlisted URLs, those changes will not be overwritten."
msgstr ""

#: lib/menu_options.php:270
msgid "These options are also available throughout the plugin pages, in the relevant sections. This page is provided for easier setup for experienced Wordfence users."
msgstr ""

#: lib/menu_options.php:353
#: models/page/wfPage.php:115
msgid "Scan Options"
msgstr ""

#: lib/menu_options.php:385
msgid "Tool Options"
msgstr ""

#: lib/menu_options.php:424
msgid "Importing and exporting of options is available on the Tools page"
msgstr ""

#: lib/menu_scanner.php:10
msgid "Status Updates Paused<br /><small>Click inside window to resume</small>"
msgstr ""

#: lib/menu_scanner.php:30
msgid "Learn more<span class=\"wf-hidden-xs\"> about the Scanner</span>"
msgstr ""

#: lib/menu_scanner.php:57
#: lib/menu_scanner_options.php:112
msgid "Scan Type: "
msgstr ""

#: lib/menu_scanner.php:63
#: lib/menu_scanner.php:83
#: lib/menu_scanner.php:99
msgid "https://www.wordfence.com/help/scan/#scan-status"
msgstr ""

#: lib/menu_scanner.php:73
#: lib/menu_scanner_options.php:128
msgid "Malware Signatures: "
msgstr ""

#: lib/menu_scanner.php:74
#: lib/menu_scanner_options.php:129
msgid "Signatures updated in real-time"
msgstr ""

#: lib/menu_scanner.php:74
#: lib/menu_scanner_options.php:129
msgid "Signature updates delayed by 30 days"
msgstr ""

#: lib/menu_scanner.php:76
#: lib/wordfenceClass.php:6946
msgid "Protect More Sites"
msgstr ""

#: lib/menu_scanner.php:78
#: lib/menu_scanner_options.php:132
msgid "Malware Signatures Status"
msgstr ""

#: lib/menu_scanner.php:81
#: lib/menu_scanner_options.php:135
#: models/scanner/wfScanner.php:869
msgid "Enable Premium Scan Signatures."
msgstr ""

#: lib/menu_scanner.php:93
#: lib/menu_scanner_options.php:147
msgid "Reputation Checks"
msgstr ""

#: lib/menu_scanner.php:94
#: lib/menu_scanner_options.php:148
msgid "Check spam &amp; spamvertising blocklists"
msgstr ""

#: lib/menu_scanner.php:96
msgid "Manage Options"
msgstr ""

#: lib/menu_scanner.php:97
#: lib/menu_scanner_options.php:151
msgid "Reputation Check Status"
msgstr ""

#: lib/menu_scanner.php:140
#: lib/menu_scanner_options.php:93
msgid "Scan Options and Scheduling"
msgstr ""

#: lib/menu_scanner.php:141
msgid "Manage scan options including scheduling"
msgstr ""

#: lib/menu_scanner.php:202
msgid "Are you sure you want to delete?"
msgstr ""

#: lib/menu_scanner.php:203
msgid "<strong>WARNING:</strong> If you delete the wrong file, it could cause your WordPress website to stop functioning, and you will probably have to restore from a backup."
msgstr ""

#. translators: Support URL.
#: lib/menu_scanner.php:206
msgid "Do not delete files on your system unless you're ABSOLUTELY sure you know what you're doing. If you delete the wrong file it could cause your WordPress website to stop functioning and you will probably have to restore from backups. If you're unsure, Cancel and work with your hosting provider to clean your system of infected files. If you'd like to learn more, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">click here for our help article<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: lib/menu_scanner.php:208
msgid "Delete Files"
msgstr ""

#: lib/menu_scanner.php:216
msgid "Are you sure you want to repair?"
msgstr ""

#: lib/menu_scanner.php:217
msgid "Do not repair files on your system unless you're ABSOLUTELY sure you know what you're doing. If you repair the wrong file it could cause your WordPress website to stop functioning and you will probably have to restore from backups. If you're unsure, Cancel and work with your hosting provider to clean your system of infected files."
msgstr ""

#: lib/menu_scanner.php:219
msgid "Repair Files"
msgstr ""

#. translators: Time limit (number).
#. translators: Support URL.
#: lib/menu_scanner.php:229
#: lib/menu_tools_twoFactor.php:246
#: lib/menu_tools_twoFactor.php:255
#: lib/menu_tools_twoFactor.php:264
#: lib/menu_tools_twoFactor.php:284
#: lib/wordfenceClass.php:5048
#: lib/wordfenceClass.php:5054
#: lib/wordfenceClass.php:5060
#: lib/wordfenceClass.php:5079
#: lib/wordfenceClass.php:5085
#: lib/wordfenceClass.php:5092
#: lib/wordfenceClass.php:5100
#: lib/wordfenceClass.php:6268
#: lib/wordfenceClass.php:6270
#: lib/wordfenceClass.php:6321
#: lib/wordfenceClass.php:8197
#: lib/wordfenceClass.php:8204
#: lib/wordfenceClass.php:8321
#: lib/wordfenceClass.php:8396
#: modules/login-security/classes/controller/wordfencels.php:373
#: modules/login-security/classes/controller/wordfencels.php:376
#: views/dashboard/options-group-import.php:147
#: views/dashboard/options-group-import.php:157
#: views/dashboard/options-group-import.php:177
#: views/offboarding/deactivation-prompt.php:70
#: views/onboarding/modal-final-attempt.php:36
msgid "Close"
msgstr ""

#: lib/menu_scanner.php:297
msgid "A Wordfence scan looks for malware, malicious URLs, and patterns of infections by examining all of the files, posts, and comments on your WordPress website. It also checks your server and monitors your site's online reputation."
msgstr ""

#: lib/menu_scanner.php:311
msgid "Manage Scan Settings"
msgstr ""

#: lib/menu_scanner.php:313
msgid "Set up the way you want the scan to monitor your site security including custom scan configurations and scheduling."
msgstr ""

#: lib/menu_scanner.php:328
msgid "Start Your First Scan"
msgstr ""

#: lib/menu_scanner.php:329
msgid "By default, Wordfence will scan your site daily. Start your first scan now to see if your site has any security issues that need to be addressed. From here you can run manual scans any time you like."
msgstr ""

#: lib/menu_scanner.php:365
msgid "Scan Options &amp; Settings"
msgstr ""

#: lib/menu_scanner.php:367
msgid "All of your scan options, including scheduling, are now located here."
msgstr ""

#: lib/menu_scanner.php:380
msgid "Scan Progress and Activity"
msgstr ""

#: lib/menu_scanner.php:381
msgid "Track each scan stage as Wordfence scans your entire site. Along the way you can see the activity log one line at a time or expand the activity log for a more detailed view. Clicking on scan results will reveal detailed scan findings."
msgstr ""

#: lib/menu_scanner_credentials.php:31
msgid "Back to Scan"
msgstr ""

#: lib/menu_scanner_credentials.php:51
msgid "File System Credentials Required"
msgstr ""

#. translators: URL to the WordPress admin panel.
#: lib/menu_scanner_credentials.php:68
msgid "Security token has expired. Click <a href=\"%s\">here</a> to return to the scan page."
msgstr ""

#: lib/menu_scanner_options.php:14
msgid "Scanner Options"
msgstr ""

#: lib/menu_scanner_options.php:68
msgid "Are you sure you want to restore the default Scan settings? This will undo any custom changes you have made to the options on this page."
msgstr ""

#: lib/menu_scanner_options.php:95
msgid "Learn more<span class=\"wf-hidden-xs\"> about Scanning</span>"
msgstr ""

#: lib/menu_support.php:30
msgid "Premium Support"
msgstr ""

#: lib/menu_support.php:33
msgid "As a Wordfence Response customer you are entitled to hands-on priority support 24 hours a day 365 days a year. Our incident response team is available out of hours to handle urgent issues and security incidents. Our customer support team is available during business hours (Monday to Friday, 6am to 5pm Pacific and 9am to 8pm Eastern time) for product assistance. Both teams can sign-in to your site to assist, on request."
msgstr ""

#: lib/menu_support.php:35
msgid "As a Wordfence Care customer you are entitled to hands-on priority support and have access to our incident response team. Our senior support engineers and incident response team respond to requests quickly within business hours (Monday to Friday, 6am to 5pm Pacific and 9am to 8pm Eastern time) and can sign-in to your site on request to assist with complex issues."
msgstr ""

#: lib/menu_support.php:37
msgid "As a Wordfence Premium customer you're entitled to paid support via our ticketing system. Our senior support engineers respond to Premium tickets during regular business hours (Monday to Friday, 6am to 5pm Pacific and 9am to 8pm Eastern time) and have a direct line to our QA and development teams."
msgstr ""

#: lib/menu_support.php:41
#: views/scanner/site-cleaning-beta-sigs.php:16
#: views/scanner/site-cleaning-bottom.php:21
#: views/scanner/site-cleaning-high-sense.php:16
#: views/scanner/site-cleaning.php:19
msgid "Get Help"
msgstr ""

#: lib/menu_support.php:44
msgid "Upgrade to hands-on support with Wordfence Care"
msgstr ""

#: lib/menu_support.php:46
msgid "Upgrade to a 24/7 1-hour response time with Wordfence Response"
msgstr ""

#: lib/menu_support.php:51
msgid "Upgrade Now to Access Premium Support"
msgstr ""

#: lib/menu_support.php:52
msgid "Our senior support engineers <strong>respond to Premium tickets within a few hours</strong> on average and have a direct line to our QA and development teams."
msgstr ""

#: lib/menu_support.php:57
msgid "Free Support"
msgstr ""

#: lib/menu_support.php:58
msgid "Support for free customers is available via our forums page on wordpress.org. The majority of requests <strong>receive an answer within a few days.</strong>"
msgstr ""

#: lib/menu_support.php:59
msgid "Go to Support Forums"
msgstr ""

#: lib/menu_support.php:74
msgid "GDPR Information"
msgstr ""

#: lib/menu_support.php:85
msgid "General Data Protection Regulation"
msgstr ""

#: lib/menu_support.php:86
msgid "The GDPR is a set of rules that provides more control over EU personal data. Defiant has updated its terms of service, privacy policies, and software, as well as made available standard contractual clauses to meet GDPR compliance."
msgstr ""

#: lib/menu_support.php:95
msgid "Agreement to New Terms and Privacy Policies"
msgstr ""

#: lib/menu_support.php:96
msgid "To continue using Defiant products and services including the Wordfence plugin, all customers must review and agree to the updated terms and privacy policies. These changes reflect our commitment to follow data protection best practices and regulations. The Wordfence interface will remain disabled until these terms are agreed to."
msgstr ""

#: lib/menu_support.php:109
msgid "All Documentation"
msgstr ""

#: lib/menu_support.php:117
msgid "Top Topics and Questions"
msgstr ""

#: lib/menu_support.php:165
msgid "Documentation"
msgstr ""

#: lib/menu_support.php:166
msgid "Documentation about Wordfence may be found on our website by clicking the button below or by clicking the <i class=\"wf-fa wf-fa-question-circle-o\" aria-hidden=\"true\"></i> links on any of the plugin's pages."
msgstr ""

#: lib/menu_support.php:167
msgid "View Documentation"
msgstr ""

#: lib/menu_support.php:272
#: views/onboarding/registration-prompt.php:78
msgid "Premium License Installed"
msgstr ""

#: lib/menu_support.php:272
#: views/onboarding/registration-prompt.php:79
msgid "Congratulations! Wordfence Premium is now active on your website. Please note that some Premium features are not enabled by default."
msgstr ""

#: lib/menu_support.php:272
#: views/blocking/blocking-create.php:530
#: views/waf/waf-install.php:106
#: views/waf/waf-modal-wrapper.php:17
#: views/waf/waf-uninstall.php:108
msgid "Continue"
msgstr ""

#: lib/menu_support.php:290
msgid "An unknown error occurred."
msgstr ""

#: lib/menu_tools.php:19
#: lib/menu_tools_twoFactor.php:14
#: lib/menu_tools_twoFactor.php:23
#: models/page/wfPage.php:117
#: modules/login-security/classes/controller/wordfencels.php:902
#: modules/login-security/classes/controller/wordfencels.php:903
msgid "Two-Factor Authentication"
msgstr ""

#: lib/menu_tools.php:21
#: lib/menu_tools_livetraffic.php:8
#: lib/menu_tools_livetraffic.php:41
#: lib/menu_tools_livetraffic.php:571
#: lib/menu_tools_livetraffic.php:602
#: lib/wordfenceClass.php:6902
#: models/page/wfPage.php:119
msgid "Live Traffic"
msgstr ""

#: lib/menu_tools.php:22
#: lib/menu_tools_auditlog.php:16
#: lib/menu_tools_auditlog.php:49
#: lib/menu_tools_auditlog.php:239
#: lib/menu_tools_auditlog.php:270
#: lib/wordfenceClass.php:6905
msgid "Audit Log"
msgstr ""

#: lib/menu_tools.php:23
#: lib/menu_tools_whois.php:7
#: lib/menu_tools_whois.php:16
#: lib/menu_tools_whois.php:75
#: models/page/wfPage.php:123
msgid "Whois Lookup"
msgstr ""

#: lib/menu_tools.php:25
#: lib/menu_tools_diagnostic.php:24
#: lib/wfDiagnostic.php:1166
#: models/page/wfPage.php:125
msgid "Diagnostics"
msgstr ""

#. translators: URL to support page.
#: lib/menu_tools_auditlog.php:52
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wf-help-link\">Learn more<span class=\"wf-hidden-xs\"> about the Audit Log</span><span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/menu_tools_auditlog.php:59
msgid "The Wordfence Audit Log records a history of events on your site to assist in monitoring for unauthorized actions or signs of compromise, ranging from user creation and editing to plugin/theme installation and updates. You can choose to log all events or significant events only, which includes all authentication, site configuration, and site functionality events. Events are securely saved to Wordfence Central to prevent any tampering with the data that may interfere with post-incident analysis and response."
msgstr ""

#: lib/menu_tools_auditlog.php:63
msgid "View Audit Log"
msgstr ""

#: lib/menu_tools_auditlog.php:67
#: lib/menu_tools_auditlog.php:97
#: lib/menu_wordfence_central.php:117
msgid "Connect Site"
msgstr ""

#: lib/menu_tools_auditlog.php:84
#: lib/menu_tools_auditlog.php:105
msgid "Audit log mode: Disabled"
msgstr ""

#. translators: 1. WordPress version. 2. Minimum WordPress version.
#: lib/menu_tools_auditlog.php:86
msgid "You are running WordPress version %1$s, which is not supported by the Wordfence Audit Log. In order to use it, please upgrade to at least WordPress version %2$s."
msgstr ""

#: lib/menu_tools_auditlog.php:93
#: lib/menu_tools_auditlog.php:97
msgid "Audit log mode: Not recording"
msgstr ""

#: lib/menu_tools_auditlog.php:93
#: modules/login-security/views/options/option-select.php:22
#: modules/login-security/views/options/option-text.php:30
#: modules/login-security/views/options/option-toggled-boolean-switch.php:32
#: modules/login-security/views/options/option-toggled-segmented.php:21
#: modules/login-security/views/options/option-toggled-select.php:27
#: modules/login-security/views/options/option-toggled-sub.php:44
#: modules/login-security/views/options/option-toggled-sub.php:52
#: modules/login-security/views/options/option-toggled-textarea.php:24
#: modules/login-security/views/options/option-token.php:25
#: views/options/option-select.php:22
#: views/options/option-switch.php:35
#: views/options/option-text.php:30
#: views/options/option-textarea.php:37
#: views/options/option-toggled-boolean-switch.php:32
#: views/options/option-toggled-multiple.php:29
#: views/options/option-toggled-segmented.php:21
#: views/options/option-toggled-select.php:27
#: views/options/option-toggled-sub.php:44
#: views/options/option-toggled-sub.php:52
#: views/options/option-toggled-textarea.php:24
#: views/options/option-toggled.php:36
#: views/options/option-token.php:25
#: views/scanner/scan-scheduling.php:38
#: views/waf/option-rate-limit.php:27
msgid "Premium Feature"
msgstr ""

#: lib/menu_tools_auditlog.php:97
msgid "Wordfence Central is not connected, which is required for recording of audit log events."
msgstr ""

#: lib/menu_tools_auditlog.php:101
msgid "Audit log mode: Preview"
msgstr ""

#: lib/menu_tools_auditlog.php:101
msgid "Change the recording mode setting above to begin recording events to Wordfence Central."
msgstr ""

#: lib/menu_tools_auditlog.php:105
msgid "You will not be able to preview events and events will not record to Wordfence Central."
msgstr ""

#: lib/menu_tools_auditlog.php:109
msgid "Audit log mode: Malfunctioning"
msgstr ""

#: lib/menu_tools_auditlog.php:109
msgid "The Audit Log has failed to successfully send events for two days. Please verify the connection with Wordfence Central, connectivity to the Wordfence servers, and that the database has no damaged tables."
msgstr ""

#: lib/menu_tools_auditlog.php:113
msgid "Audit log mode: Significant events only"
msgstr ""

#: lib/menu_tools_auditlog.php:113
msgid "The audit log is currently recording all significant events to Wordfence Central, which includes user actions and updates, site modifications, and Wordfence configuration changes."
msgstr ""

#: lib/menu_tools_auditlog.php:117
msgid "Audit log mode: All events"
msgstr ""

#: lib/menu_tools_auditlog.php:117
msgid "The audit log is currently recording all monitored events to Wordfence Central, including content changes, user actions and changes, site modifications, and Wordfence configuration updates."
msgstr ""

#: lib/menu_tools_auditlog.php:126
msgid "Log Security Events to an Off-Site Audit Log on Wordfence Central"
msgstr ""

#: lib/menu_tools_auditlog.php:127
msgid "The Wordfence Audit Log is designed to monitor all changes and actions in security-sensitive areas of the site. Actions such as user creation, plugin installation and activation, changes to settings, and similar are all logged with relevant contextual information for later review or forensic analysis. Additionally, the log is securely saved outside of the site on Wordfence Central (sample below) to avoid tampering or deletion by malicious actors."
msgstr ""

#: lib/menu_tools_auditlog.php:147
msgid "Recent Event Summary"
msgstr ""

#: lib/menu_tools_auditlog.php:149
msgid "The most recently-detected events on this site are listed below. When the audit log is enabled and your site is connected to Wordfence Central, full details of each event can be found on Central. This includes information such as record IDs, version numbers, and which modifications were made. Log entries in preview mode are only stored locally."
msgstr ""

#: lib/menu_tools_auditlog.php:158
#: lib/menu_tools_livetraffic.php:286
#: lib/wordfenceClass.php:6423
msgid "Type"
msgstr ""

#: lib/menu_tools_auditlog.php:159
#: lib/menu_tools_livetraffic.php:289
#: lib/wf503.php:368
#: views/reports/activity-report-email-inline.php:294
msgid "Time"
msgstr ""

#: lib/menu_tools_auditlog.php:160
msgid "Events"
msgstr ""

#: lib/menu_tools_auditlog.php:169
msgid "No Events Detected"
msgstr ""

#: lib/menu_tools_auditlog.php:202
msgid "Authentication"
msgstr ""

#: lib/menu_tools_auditlog.php:203
msgid "User/Permissions"
msgstr ""

#: lib/menu_tools_auditlog.php:204
msgid "Plugin/Themes/Updates"
msgstr ""

#: lib/menu_tools_auditlog.php:206
msgid "Site Settings"
msgstr ""

#: lib/menu_tools_auditlog.php:207
msgid "Multisite"
msgstr ""

#: lib/menu_tools_auditlog.php:208
msgid "Content"
msgstr ""

#: lib/menu_tools_auditlog.php:240
#: lib/menu_tools_auditlog.php:271
msgid "The Wordfence Audit Log is a premium feature that records a history of events on your site to assist in monitoring for unauthorized actions or signs of compromise. Events can include everything from user creation and editing to plugin/theme installation and updates. All data captured for relevant events is saved remotely to Wordfence Central to prevent any tampering that may interfere with post-incident analysis and response."
msgstr ""

#: lib/menu_tools_diagnostic.php:34
msgid "This page shows information that can be used for troubleshooting conflicts, configuration issues, or compatibility with other plugins, themes, or a host's environment. Failing tests are not always a sign of something that you need to fix, but can help the Wordfence team when troubleshooting a problem. (<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More <span class=\"screen-reader-text\">opens in new tab</span></a>)"
msgstr ""

#: lib/menu_tools_diagnostic.php:38
msgid "Thanks for sending your diagnostic page over email"
msgstr ""

#: lib/menu_tools_diagnostic.php:53
msgid "Send Report by Email"
msgstr ""

#: lib/menu_tools_diagnostic.php:60
msgid "Email address:"
msgstr ""

#: lib/menu_tools_diagnostic.php:67
msgid "Ticket Number/Forum Username:"
msgstr ""

#: lib/menu_tools_diagnostic.php:123
msgid "Additional Detail"
msgstr ""

#: lib/menu_tools_diagnostic.php:166
msgid "View Additional Detail"
msgstr ""

#: lib/menu_tools_diagnostic.php:187
#: views/diagnostics/text.php:75
#: views/scanner/issue-checkHowGetIPs.php:8
msgid "IP Detection"
msgstr ""

#: lib/menu_tools_diagnostic.php:188
#: views/diagnostics/text.php:75
msgid "Methods of detecting a visitor's IP address."
msgstr ""

#: lib/menu_tools_diagnostic.php:200
#: views/diagnostics/text.php:84
msgid "IPs"
msgstr ""

#: lib/menu_tools_diagnostic.php:201
#: views/diagnostics/text.php:85
#: views/diagnostics/text.php:148
#: views/diagnostics/text.php:558
#: views/diagnostics/text.php:589
msgid "Value"
msgstr ""

#: lib/menu_tools_diagnostic.php:202
#: views/diagnostics/text.php:86
msgid "Used"
msgstr ""

#. translators: WordPress theme template directory.
#. translators: WordPress theme stylesheet directory.
#. translators: WordPress custom user table.
#. translators: WordPress custom user meta table.
#: lib/menu_tools_diagnostic.php:218
#: lib/menu_tools_diagnostic.php:237
#: lib/menu_tools_diagnostic.php:242
#: lib/menu_tools_diagnostic.php:270
#: lib/menu_tools_diagnostic.php:791
#: lib/menu_tools_diagnostic.php:837
#: lib/wfDiagnostic.php:379
#: lib/wfDiagnostic.php:415
#: lib/wfDiagnostic.php:457
#: lib/wfDiagnostic.php:473
#: lib/wfDiagnostic.php:1037
#: lib/wfDiagnostic.php:1038
#: lib/wfDiagnostic.php:1051
#: lib/wfDiagnostic.php:1052
#: lib/wfDiagnostic.php:1065
#: lib/wfDiagnostic.php:1066
#: lib/wfDiagnostic.php:1067
#: lib/wfDiagnostic.php:1068
#: lib/wfDiagnostic.php:1082
#: lib/wfDiagnostic.php:1083
#: lib/wfDiagnostic.php:1084
#: lib/wfDiagnostic.php:1086
#: lib/wfDiagnostic.php:1087
#: lib/wfDiagnostic.php:1088
#: lib/wfDiagnostic.php:1093
#: lib/wfDiagnostic.php:1227
#: lib/wfDiagnostic.php:1230
#: views/diagnostics/text.php:101
#: views/diagnostics/text.php:125
#: views/diagnostics/text.php:133
#: views/diagnostics/text.php:155
#: views/diagnostics/text.php:569
#: views/diagnostics/text.php:600
msgid "(not set)"
msgstr ""

#: lib/menu_tools_diagnostic.php:227
#: views/diagnostics/text.php:111
msgid "In use"
msgstr ""

#: lib/menu_tools_diagnostic.php:229
#: views/diagnostics/text.php:113
msgid "Configured but not valid"
msgstr ""

#: lib/menu_tools_diagnostic.php:241
#: views/dashboard/option-howgetips.php:68
#: views/diagnostics/text.php:132
msgid "Trusted Proxy Preset"
msgstr ""

#: lib/menu_tools_diagnostic.php:255
#: views/diagnostics/text.php:141
msgid "WordPress Settings"
msgstr ""

#: lib/menu_tools_diagnostic.php:256
#: views/diagnostics/text.php:141
msgid "WordPress version and internal settings/constants."
msgstr ""

#: lib/menu_tools_diagnostic.php:298
#: views/diagnostics/text.php:179
msgid "WordPress Plugins"
msgstr ""

#: lib/menu_tools_diagnostic.php:299
#: views/diagnostics/text.php:179
msgid "Status of installed plugins."
msgstr ""

#. translators: Plugin version.
#. translators: Theme version.
#: lib/menu_tools_diagnostic.php:323
#: lib/menu_tools_diagnostic.php:369
#: lib/menu_tools_diagnostic.php:468
#: views/diagnostics/text.php:197
#: views/diagnostics/text.php:236
#: views/diagnostics/text.php:310
msgid "Version %s"
msgstr ""

#: lib/menu_tools_diagnostic.php:327
#: views/diagnostics/text.php:201
msgid "Network Activated"
msgstr ""

#: lib/menu_tools_diagnostic.php:329
#: lib/menu_tools_diagnostic.php:372
#: lib/menu_tools_diagnostic.php:428
#: lib/menu_tools_diagnostic.php:471
#: modules/login-security/classes/controller/users.php:551
#: modules/login-security/classes/controller/wordfencels.php:490
#: views/diagnostics/text.php:203
#: views/diagnostics/text.php:241
#: views/diagnostics/text.php:283
#: views/diagnostics/text.php:314
msgid "Active"
msgstr ""

#: lib/menu_tools_diagnostic.php:331
#: lib/menu_tools_diagnostic.php:430
#: lib/menu_tools_diagnostic.php:473
#: modules/login-security/classes/controller/users.php:560
#: modules/login-security/classes/controller/wordfencels.php:490
#: views/diagnostics/text.php:205
#: views/diagnostics/text.php:283
#: views/diagnostics/text.php:316
msgid "Inactive"
msgstr ""

#: lib/menu_tools_diagnostic.php:343
#: views/diagnostics/text.php:217
msgid "Must-Use WordPress Plugins"
msgstr ""

#: lib/menu_tools_diagnostic.php:344
#: views/diagnostics/text.php:217
msgid "WordPress \"mu-plugins\" that are always active, including those provided by hosts."
msgstr ""

#: lib/menu_tools_diagnostic.php:379
#: views/diagnostics/text.php:246
msgid "No MU-Plugins"
msgstr ""

#: lib/menu_tools_diagnostic.php:391
#: views/diagnostics/text.php:255
msgid "Drop-In WordPress Plugins"
msgstr ""

#: lib/menu_tools_diagnostic.php:392
#: views/diagnostics/text.php:255
msgid "WordPress \"drop-in\" plugins that are active."
msgstr ""

#: lib/menu_tools_diagnostic.php:405
#: views/diagnostics/text.php:261
msgid "Advanced caching plugin"
msgstr ""

#: lib/menu_tools_diagnostic.php:406
#: views/diagnostics/text.php:262
msgid "Custom database class"
msgstr ""

#: lib/menu_tools_diagnostic.php:407
#: views/diagnostics/text.php:263
msgid "Custom database error message"
msgstr ""

#: lib/menu_tools_diagnostic.php:408
#: views/diagnostics/text.php:264
msgid "Custom installation script"
msgstr ""

#: lib/menu_tools_diagnostic.php:409
#: views/diagnostics/text.php:265
msgid "Custom maintenance message"
msgstr ""

#: lib/menu_tools_diagnostic.php:410
#: views/diagnostics/text.php:266
msgid "External object cache"
msgstr ""

#: lib/menu_tools_diagnostic.php:411
#: views/diagnostics/text.php:267
msgid "Custom PHP error message"
msgstr ""

#: lib/menu_tools_diagnostic.php:412
#: views/diagnostics/text.php:268
msgid "Custom PHP fatal error handler"
msgstr ""

#: lib/menu_tools_diagnostic.php:414
#: views/diagnostics/text.php:270
msgid "Executed before Multisite is loaded"
msgstr ""

#: lib/menu_tools_diagnostic.php:415
#: views/diagnostics/text.php:271
msgid "Custom site deleted message"
msgstr ""

#: lib/menu_tools_diagnostic.php:416
#: views/diagnostics/text.php:272
msgid "Custom site inactive message"
msgstr ""

#: lib/menu_tools_diagnostic.php:417
#: views/diagnostics/text.php:273
msgid "Custom site suspended message"
msgstr ""

#: lib/menu_tools_diagnostic.php:442
#: views/diagnostics/text.php:291
#: views/reports/activity-report-email-inline.php:392
#: views/reports/activity-report.php:191
msgid "Themes"
msgstr ""

#: lib/menu_tools_diagnostic.php:443
#: views/diagnostics/text.php:291
msgid "Status of installed themes."
msgstr ""

#: lib/menu_tools_diagnostic.php:481
#: views/diagnostics/text.php:325
msgid "No Themes"
msgstr ""

#: lib/menu_tools_diagnostic.php:493
#: views/diagnostics/text.php:334
msgid "Cron Jobs"
msgstr ""

#: lib/menu_tools_diagnostic.php:494
#: views/diagnostics/text.php:334
msgid "List of WordPress cron jobs scheduled by WordPress, plugins, or themes."
msgstr ""

#: lib/menu_tools_diagnostic.php:514
#: views/diagnostics/text.php:349
msgid "Overdue"
msgstr ""

#: lib/menu_tools_diagnostic.php:546
#: views/diagnostics/text.php:361
msgid "Database Tables"
msgstr ""

#: lib/menu_tools_diagnostic.php:547
#: views/diagnostics/text.php:361
msgid "Database table names, sizes, timestamps, and other metadata."
msgstr ""

#: lib/menu_tools_diagnostic.php:557
msgid "Wordfence Table Check"
msgstr ""

#: lib/menu_tools_diagnostic.php:560
#: views/diagnostics/text.php:379
msgid "Unable to verify - table count too high"
msgstr ""

#: lib/menu_tools_diagnostic.php:589
#: views/diagnostics/text.php:412
msgid "All Tables Exist"
msgstr ""

#. translators: 1. WordPress table prefix. 2. Wordfence table case. 3. List of database tables.
#: lib/menu_tools_diagnostic.php:593
#: views/diagnostics/text.php:414
msgid "Tables missing (prefix %1$s, %2$s): %3$s"
msgstr ""

#. translators: 1. WordPress table prefix. 2. Wordfence table case. 3. List of database tables.
#: lib/menu_tools_diagnostic.php:593
#: views/diagnostics/text.php:414
msgid "lowercase"
msgstr ""

#. translators: 1. WordPress table prefix. 2. Wordfence table case. 3. List of database tables.
#: lib/menu_tools_diagnostic.php:593
#: views/diagnostics/text.php:414
msgid "regular case"
msgstr ""

#: lib/menu_tools_diagnostic.php:599
msgid "Number of Database Tables"
msgstr ""

#. translators: Row/record count.
#: lib/menu_tools_diagnostic.php:645
#: views/diagnostics/text.php:451
msgid "and %d more"
msgstr ""

#: lib/menu_tools_diagnostic.php:664
#: views/diagnostics/text.php:462
msgid "Log Files"
msgstr ""

#: lib/menu_tools_diagnostic.php:665
#: views/diagnostics/text.php:462
msgid "PHP error logs generated by your site, if enabled by your host."
msgstr ""

#: lib/menu_tools_diagnostic.php:677
#: views/diagnostics/text.php:468
#: views/reports/activity-report-email-inline.php:341
#: views/scanner/issue-file.php:8
#: views/scanner/issue-knownfile.php:8
msgid "File"
msgstr ""

#: lib/menu_tools_diagnostic.php:678
#: lib/menu_tools_diagnostic.php:717
#: lib/wordfenceClass.php:6329
#: modules/login-security/views/manage/activate.php:32
#: modules/login-security/views/manage/activate.php:133
#: modules/login-security/views/manage/regenerate.php:77
msgid "Download"
msgstr ""

#: lib/menu_tools_diagnostic.php:686
#: views/diagnostics/text.php:475
msgid "No log files found."
msgstr ""

#: lib/menu_tools_diagnostic.php:706
#: views/diagnostics/text.php:494
msgid "UTC"
msgstr ""

#: lib/menu_tools_diagnostic.php:717
msgid "Requires downloading from the server directly"
msgstr ""

#: lib/menu_tools_diagnostic.php:731
#: views/diagnostics/text.php:515
msgid "Scan Issues"
msgstr ""

#. translators: Number of scan issues.
#: lib/menu_tools_diagnostic.php:736
#: views/diagnostics/text.php:523
msgid "New Issues (%d total)"
msgstr ""

#: lib/menu_tools_diagnostic.php:757
#: lib/wordfenceClass.php:4004
#: views/diagnostics/text.php:546
msgid "No New Issues"
msgstr ""

#: lib/menu_tools_diagnostic.php:768
#: views/diagnostics/text.php:552
msgid "Wordfence Settings"
msgstr ""

#: lib/menu_tools_diagnostic.php:769
#: views/diagnostics/text.php:552
msgid "Diagnostic Wordfence settings/constants."
msgstr ""

#: lib/menu_tools_diagnostic.php:815
#: views/diagnostics/text.php:583
msgid "Diagnostic connection information for Wordfence Central."
msgstr ""

#: lib/menu_tools_diagnostic.php:855
#: lib/sysinfo.php:11
#: lib/wordfenceClass.php:4038
#: views/diagnostics/text.php:618
msgid "Unable to output phpinfo content because it is disabled"
msgstr ""

#: lib/menu_tools_diagnostic.php:864
msgid "Other Tests"
msgstr ""

#: lib/menu_tools_diagnostic.php:865
msgid "System configuration, memory test, send test email from this server."
msgstr ""

#: lib/menu_tools_diagnostic.php:876
msgid "Click to view your system's configuration in a new window"
msgstr ""

#: lib/menu_tools_diagnostic.php:882
msgid "Test your WordPress host's available memory"
msgstr ""

#: lib/menu_tools_diagnostic.php:888
msgid "Send a test email from this WordPress server to an email address:"
msgstr ""

#: lib/menu_tools_diagnostic.php:890
msgid "Send Test Email"
msgstr ""

#: lib/menu_tools_diagnostic.php:895
msgid "Send a test activity report email:"
msgstr ""

#: lib/menu_tools_diagnostic.php:897
msgid "Send Test Activity Report"
msgstr ""

#: lib/menu_tools_diagnostic.php:902
msgid "Clear all Wordfence Central connection data"
msgstr ""

#: lib/menu_tools_diagnostic.php:903
msgid "Clear All Connection Data"
msgstr ""

#: lib/menu_tools_diagnostic.php:903
#: lib/menu_tools_diagnostic.php:904
msgid "Successfully removed data"
msgstr ""

#: lib/menu_tools_diagnostic.php:903
msgid "All associated Wordfence Central connection data has been cleared."
msgstr ""

#: lib/menu_tools_diagnostic.php:904
msgid "Clear Local Connection Data"
msgstr ""

#: lib/menu_tools_diagnostic.php:904
msgid "All associated Wordfence Central connection data has been removed from the local database."
msgstr ""

#: lib/menu_tools_diagnostic.php:916
msgid "Debugging Options"
msgstr ""

#: lib/menu_tools_diagnostic.php:933
#: lib/wfDiagnostic.php:1167
msgid "Enable debugging mode (increases database load)"
msgstr ""

#: lib/menu_tools_diagnostic.php:945
#: lib/wfDiagnostic.php:1168
msgid "Start all scans remotely (Try this if your scans aren't starting and your site is publicly accessible)"
msgstr ""

#: lib/menu_tools_diagnostic.php:957
#: lib/wfDiagnostic.php:1169
msgid "Enable SSL Verification (Disable this if you are consistently unable to connect to the Wordfence servers.)"
msgstr ""

#: lib/menu_tools_diagnostic.php:969
#: lib/wfDiagnostic.php:1170
msgid "Disable reading of php://input"
msgstr ""

#: lib/menu_tools_diagnostic.php:988
#: lib/wordfenceClass.php:6391
msgid "Restore Defaults"
msgstr ""

#: lib/menu_tools_diagnostic.php:989
#: lib/wordfenceClass.php:6315
msgid "Cancel Changes"
msgstr ""

#: lib/menu_tools_diagnostic.php:990
#: lib/wordfenceClass.php:6394
msgid "Save Changes"
msgstr ""

#: lib/menu_tools_diagnostic.php:1008
#: views/options/block-all-options-controls.php:162
#: views/options/block-controls.php:77
msgid "Confirm Restore Defaults"
msgstr ""

#: lib/menu_tools_diagnostic.php:1009
msgid "Are you sure you want to restore the default Diagnostics settings? This will undo any custom changes you have made to the options on this page."
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Defaults"
#. translators: word order may be reversed as long as HTML remains around "Restore"
#: lib/menu_tools_diagnostic.php:1011
#: views/options/block-all-options-controls.php:165
#: views/options/block-controls.php:80
msgid "Restore<span class=\"wf-hidden-xs\"> Defaults</span>"
msgstr ""

#. translators: URL to support page.
#: lib/menu_tools_importExport.php:16
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wf-help-link\">Learn more<span class=\"wf-hidden-xs\"> about importing and exporting options</span><span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/menu_tools_importExport.php:20
msgid "To clone one site's configuration to another, use the import/export tools below."
msgstr ""

#. translators: URL to support page.
#: lib/menu_tools_livetraffic.php:44
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wf-help-link\">Learn more<span class=\"wf-hidden-xs\"> about Live Traffic</span><span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/menu_tools_livetraffic.php:51
msgid "Live Updates Paused"
msgstr ""

#: lib/menu_tools_livetraffic.php:52
msgid "Click inside window to resume"
msgstr ""

#: lib/menu_tools_livetraffic.php:57
msgid "Wordfence Live Traffic shows you what is happening on your site in real-time, including user logins, hack attempts, and requests that were blocked by the Wordfence Firewall. You can choose to log security-related traffic only or all traffic. Traffic is logged directly on the server, which means it includes visits that don't execute JavaScript. Google and other JavaScript-based analytics packages typically only show visits from browsers that are operated by a human, while Live Traffic can show visits from crawlers like Google and Bing."
msgstr ""

#: lib/menu_tools_livetraffic.php:73
msgid "Traffic logging mode: Security-related traffic only"
msgstr ""

#. translators: URL to support page.
#: lib/menu_tools_livetraffic.php:77
#: lib/menu_tools_livetraffic.php:86
msgid " (host setting <a href=\"%s\" class=\"wfhelp\" target=\"_blank\" rel=\"noopener noreferrer\"><span class=\"screen-reader-text\"> (opens in new tab)</span></a>)"
msgstr ""

#: lib/menu_tools_livetraffic.php:78
msgid "Login and firewall activity will appear below."
msgstr ""

#: lib/menu_tools_livetraffic.php:82
msgid "Traffic logging mode: All traffic"
msgstr ""

#: lib/menu_tools_livetraffic.php:87
msgid "Regular traffic and security-related traffic will appear below."
msgstr ""

#: lib/menu_tools_livetraffic.php:100
#: lib/menu_tools_livetraffic.php:227
#: lib/menu_tools_livetraffic.php:458
#: lib/wordfenceClass.php:6353
msgid "Human"
msgstr ""

#: lib/menu_tools_livetraffic.php:101
#: lib/menu_tools_livetraffic.php:228
#: lib/menu_tools_livetraffic.php:458
#: lib/wordfenceClass.php:6314
msgid "Bot"
msgstr ""

#: lib/menu_tools_livetraffic.php:102
msgid "Warning"
msgstr ""

#: lib/menu_tools_livetraffic.php:103
#: lib/wfDiagnostic.php:934
#: lib/wordfenceClass.php:6306
msgid "Blocked"
msgstr ""

#: lib/menu_tools_livetraffic.php:117
msgid "Show Advanced Filters"
msgstr ""

#: lib/menu_tools_livetraffic.php:124
msgid "Expand All Results"
msgstr ""

#. translators: WordPress plugins directory.
#: lib/menu_tools_livetraffic.php:152
#: lib/wfDiagnostic.php:365
#: lib/wfDiagnostic.php:469
#: lib/wfDiagnostic.php:536
#: lib/wfDiagnostic.php:1034
#: lib/wfDiagnostic.php:1045
#: lib/wfDiagnostic.php:1047
#: lib/wfDiagnostic.php:1048
#: lib/wfDiagnostic.php:1061
#: lib/wfDiagnostic.php:1074
#: lib/wfDiagnostic.php:1075
#: lib/wfDiagnostic.php:1076
#: lib/wfDiagnostic.php:1077
#: lib/wfDiagnostic.php:1078
#: lib/wfDiagnostic.php:1079
#: lib/wfDiagnostic.php:1084
#: lib/wfDiagnostic.php:1085
#: lib/wfDiagnostic.php:1086
#: lib/wfDiagnostic.php:1087
#: lib/wfDiagnostic.php:1088
#: lib/wfDiagnostic.php:1092
#: lib/wfSupportController.php:429
#: views/onboarding/registration-prompt.php:51
#: views/reports/activity-report-email-inline.php:270
#: views/reports/activity-report.php:117
msgid "Yes"
msgstr ""

#: lib/menu_tools_livetraffic.php:153
#: lib/wfDiagnostic.php:365
#: lib/wfDiagnostic.php:469
#: lib/wfDiagnostic.php:536
#: lib/wfDiagnostic.php:1034
#: lib/wfDiagnostic.php:1061
#: lib/wfDiagnostic.php:1074
#: lib/wfDiagnostic.php:1075
#: lib/wfDiagnostic.php:1076
#: lib/wfDiagnostic.php:1077
#: lib/wfDiagnostic.php:1078
#: lib/wfDiagnostic.php:1079
#: lib/wfDiagnostic.php:1084
#: lib/wfDiagnostic.php:1085
#: lib/wfDiagnostic.php:1087
#: lib/wfDiagnostic.php:1088
#: lib/wfDiagnostic.php:1092
#: lib/wfSupportController.php:429
#: views/onboarding/registration-prompt.php:52
#: views/reports/activity-report-email-inline.php:270
#: views/reports/activity-report.php:117
msgid "No"
msgstr ""

#: lib/menu_tools_livetraffic.php:166
msgid "Add Filter"
msgstr ""

#: lib/menu_tools_livetraffic.php:173
msgid "From:"
msgstr ""

#: lib/menu_tools_livetraffic.php:176
#: lib/menu_tools_livetraffic.php:183
msgid "Clear"
msgstr ""

#: lib/menu_tools_livetraffic.php:180
msgid "To:"
msgstr ""

#: lib/menu_tools_livetraffic.php:187
msgid "Group By:"
msgstr ""

#: lib/menu_tools_livetraffic.php:212
msgid "An unknown location at IP"
msgstr ""

#: lib/menu_tools_livetraffic.php:217
#: lib/menu_tools_livetraffic.php:435
#: modules/login-security/views/email/login-verification.php:13
msgid "IP:"
msgstr ""

#: lib/menu_tools_livetraffic.php:226
#: views/scanner/issue-base.php:29
#: views/scanner/issue-base.php:39
msgid "Type:"
msgstr ""

#: lib/menu_tools_livetraffic.php:233
msgid "Username:"
msgstr ""

#: lib/menu_tools_livetraffic.php:239
msgid "HTTP Response Code:"
msgstr ""

#: lib/menu_tools_livetraffic.php:245
msgid "Firewall Response:"
msgstr ""

#: lib/menu_tools_livetraffic.php:256
msgid "Last Hit:"
msgstr ""

#. translators: Time ago.
#: lib/menu_tools_livetraffic.php:257
msgid "Last hit was %s ago."
msgstr ""

#: lib/menu_tools_livetraffic.php:263
#: lib/menu_tools_livetraffic.php:442
#: lib/menu_tools_livetraffic.php:465
msgid "Unblock IP"
msgstr ""

#: lib/menu_tools_livetraffic.php:266
#: lib/menu_tools_livetraffic.php:447
#: lib/menu_tools_livetraffic.php:471
msgid "Unblock range"
msgstr ""

#: lib/menu_tools_livetraffic.php:269
#: lib/menu_tools_livetraffic.php:453
#: lib/menu_tools_livetraffic.php:477
msgid "Block IP"
msgstr ""

#. translators: Number of HTTP requests.
#: lib/menu_tools_livetraffic.php:275
msgid "%s hits"
msgstr ""

#: lib/menu_tools_livetraffic.php:287
msgid "Location"
msgstr ""

#: lib/menu_tools_livetraffic.php:288
msgid "Page Visited"
msgstr ""

#: lib/menu_tools_livetraffic.php:290
msgid "IP Address"
msgstr ""

#: lib/menu_tools_livetraffic.php:291
#: lib/wordfenceClass.php:4301
#: views/blocking/blocking-create.php:193
msgid "Hostname"
msgstr ""

#: lib/menu_tools_livetraffic.php:292
#: lib/wfLicense.php:240
msgid "Response"
msgstr ""

#: lib/menu_tools_livetraffic.php:293
#: views/scanner/issue-wfPluginAbandoned.php:18
#: views/scanner/issue-wfPluginAbandoned.php:19
#: views/scanner/issue-wfPluginAbandoned.php:20
#: views/scanner/issue-wfPluginRemoved.php:17
#: views/scanner/issue-wfPluginRemoved.php:18
#: views/scanner/issue-wfPluginUpgrade.php:18
#: views/scanner/issue-wfPluginUpgrade.php:19
#: views/scanner/issue-wfPluginUpgrade.php:20
#: views/scanner/issue-wfPluginVulnerable.php:17
#: views/scanner/issue-wfPluginVulnerable.php:18
#: views/scanner/issue-wfThemeUpgrade.php:18
#: views/scanner/issue-wfThemeUpgrade.php:19
#: views/scanner/issue-wfUpgrade.php:17
msgid "View"
msgstr ""

#: lib/menu_tools_livetraffic.php:307
msgid "Unspecified"
msgstr ""

#: lib/menu_tools_livetraffic.php:341
msgid "Activity Detail"
msgstr ""

#. translators: 1. User agent. 2. IP address
#: lib/menu_tools_livetraffic.php:359
msgid "%1$s at an unknown location at IP %2$s"
msgstr ""

#. translators: IP address
#: lib/menu_tools_livetraffic.php:364
msgid "An unknown location at IP %s"
msgstr ""

#. translators: 1. User agent. 2. HTTP referer. 3. Server response.
#: lib/menu_tools_livetraffic.php:371
msgid "%1$s arrived from %2$s and %3$s"
msgstr ""

#. translators: 1. User agent. 2. HTTP referer. 3. Server response.
#: lib/menu_tools_livetraffic.php:376
msgid "%1$s left %2$s and %3$s"
msgstr ""

#. translators: User agent.
#: lib/menu_tools_livetraffic.php:382
msgid "%s tried to access a <span style=\"color: #F00;\">non-existent page</span>"
msgstr ""

#. translators: 1. User agent. 2. URL of page visited.
#: lib/menu_tools_livetraffic.php:388
msgid "%1$s visited %2$s"
msgstr ""

#. translators: 1. User agent. 2. URL of page visited.
#: lib/menu_tools_livetraffic.php:393
msgid "%1$s was redirected when visiting %2$s"
msgstr ""

#. translators: 1. User agent. 2. Firewall action (blocked, rate limited, etc). 3. Time ago.
#: lib/menu_tools_livetraffic.php:398
#: lib/menu_tools_livetraffic.php:403
msgid "%1$s was %2$s at %3$s"
msgstr ""

#. translators: 1. User agent. 2. WordPress username.
#: lib/menu_tools_livetraffic.php:409
msgid "%1$s logged in successfully as \"%2$s\"."
msgstr ""

#. translators: WordPress username.
#: lib/menu_tools_livetraffic.php:412
msgid "%s logged out successfully."
msgstr ""

#. translators: WordPress username.
#: lib/menu_tools_livetraffic.php:415
msgid "%s requested a password reset."
msgstr ""

#. translators: 1. User agent. 2. WordPress username.
#: lib/menu_tools_livetraffic.php:418
msgid "%1$s attempted a <span style=\"color: #F00;\">failed login</span> as \"%2$s\"."
msgstr ""

#. translators: 1. User agent. 2. WordPress username.
#: lib/menu_tools_livetraffic.php:421
msgid "%1$s attempted a <span style=\"color: #F00;\">failed login</span> using an invalid username \"%2$s\"."
msgstr ""

#. translators: WordPress username.
#: lib/menu_tools_livetraffic.php:424
msgid "%s changed their password."
msgstr ""

#: lib/menu_tools_livetraffic.php:458
msgid "Human/Bot:"
msgstr ""

#: lib/menu_tools_livetraffic.php:481
msgid "Run Whois"
msgstr ""

#: lib/menu_tools_livetraffic.php:484
msgid "See recent traffic"
msgstr ""

#: lib/menu_tools_livetraffic.php:484
msgid "Recent"
msgstr ""

#: lib/menu_tools_livetraffic.php:489
msgid "If this is a false positive, you can exclude this parameter from being filtered by the firewall"
msgstr ""

#: lib/menu_tools_livetraffic.php:490
msgid "Add Param to Firewall Allowlist"
msgstr ""

#: lib/menu_tools_livetraffic.php:508
msgid "No requests to report yet."
msgstr ""

#: lib/menu_tools_livetraffic.php:572
msgid "Live traffic defaults to a summary view of all security-related traffic. Details are viewable by clicking anywhere within the summary record. To switch to the expanded view, click the <strong>Expand All Records</strong> switch."
msgstr ""

#: lib/menu_tools_livetraffic.php:603
msgid "Live traffic now defaults to a summary view. Details are viewable by clicking anywhere within the summary record. To switch to the expanded view, click the <strong>Expand All Records</strong> switch. New installations will only log security-related traffic by default, though your previous setting has been preserved."
msgstr ""

#: lib/menu_tools_twoFactor.php:16
msgid "Learn more<span class=\"wf-hidden-xs\"> about Two-Factor Authentication</span>"
msgstr ""

#: lib/menu_tools_twoFactor.php:35
msgid "2FA Mode: Legacy"
msgstr ""

#: lib/menu_tools_twoFactor.php:35
msgid "Two-factor authentication is using legacy support, which enables SMS-based codes but is less compatible. An improved interface and use by non-administrators is available by activating the new login security module."
msgstr ""

#: lib/menu_tools_twoFactor.php:36
msgid "Switch to New 2FA"
msgstr ""

#: lib/menu_tools_twoFactor.php:42
#: views/tools/options-group-2fa.php:31
msgid "Take Login Security to the next level with Two-Factor Authentication"
msgstr ""

#: lib/menu_tools_twoFactor.php:43
#: views/tools/options-group-2fa.php:32
msgid "Used by banks, government agencies, and military worldwide, two-factor authentication is one of the most secure forms of remote system authentication available. With it enabled, an attacker needs to know your username, password, <em>and</em> have control of your phone to log into your site. Upgrade to Premium now to enable this powerful feature."
msgstr ""

#: lib/menu_tools_twoFactor.php:59
msgid "With Two-Factor Authentication enabled, an attacker needs to know your username, password <em>and</em> have control of your phone to log in to your site. We recommend you enable Two-Factor Authentication for all Administrator level accounts."
msgstr ""

#: lib/menu_tools_twoFactor.php:66
msgid "Are you sure you want to restore the default Two-Factor Authentication settings? This will undo any custom changes you have made to the options on this page. If you have configured any users to use two-factor authentication, they will not be changed."
msgstr ""

#: lib/menu_tools_twoFactor.php:78
msgid "<strong>Note:</strong> Two-Factor Authentication is disabled when the option \"Enable Brute Force Protection\" is off."
msgstr ""

#: lib/menu_tools_twoFactor.php:85
msgid "Enable Two-Factor Authentication"
msgstr ""

#: lib/menu_tools_twoFactor.php:94
msgid "Enter username to enable Two-Factor Authentication for"
msgstr ""

#: lib/menu_tools_twoFactor.php:104
msgid "Use authenticator app"
msgstr ""

#: lib/menu_tools_twoFactor.php:113
msgid "Send code to a phone number:"
msgstr ""

#: lib/menu_tools_twoFactor.php:115
msgid "+****************"
msgstr ""

#: lib/menu_tools_twoFactor.php:134
msgid "Two-Factor Authentication Users"
msgstr ""

#: lib/menu_tools_twoFactor.php:175
#: views/waf/option-whitelist.php:109
#: views/waf/options-group-whitelisted.php:86
#: views/waf/options-group-whitelisted.php:99
msgid "User"
msgstr ""

#: lib/menu_tools_twoFactor.php:176
msgid "Mode"
msgstr ""

#: lib/menu_tools_twoFactor.php:177
#: views/diagnostics/text.php:184
#: views/diagnostics/text.php:222
#: views/diagnostics/text.php:276
#: views/diagnostics/text.php:296
#: views/scanner/issue-base.php:52
#: views/scanner/issue-wafStatus.php:12
#: views/scanner/issue-wafStatus.php:21
msgid "Status"
msgstr ""

#: lib/menu_tools_twoFactor.php:178
#: views/waf/option-whitelist.php:102
msgid "Delete"
msgstr ""

#. translators: Phone number.
#: lib/menu_tools_twoFactor.php:186
msgid "Phone (%s)"
msgstr ""

#: lib/menu_tools_twoFactor.php:188
msgid "Authenticator"
msgstr ""

#: lib/menu_tools_twoFactor.php:192
msgid "Cellphone Sign-in Enabled"
msgstr ""

#: lib/menu_tools_twoFactor.php:196
msgid "Enter activation code:"
msgstr ""

#: lib/menu_tools_twoFactor.php:197
msgid "Code"
msgstr ""

#: lib/menu_tools_twoFactor.php:199
#: modules/login-security/views/manage/activate.php:44
msgid "Activate"
msgstr ""

#: lib/menu_tools_twoFactor.php:210
msgid "No users currently have cellphone sign-in enabled."
msgstr ""

#: lib/menu_tools_twoFactor.php:221
msgid "2FA Mode: Normal"
msgstr ""

#: lib/menu_tools_twoFactor.php:221
msgid "Legacy support for SMS-based two-factor authentication is being phased out, as it is less secure than using a modern authenticator app."
msgstr ""

#: lib/menu_tools_twoFactor.php:222
msgid "If you have a conflict with the new 2FA method, you can temporarily switch back to the Legacy version."
msgstr ""

#: lib/menu_tools_twoFactor.php:223
msgid "Revert to Legacy 2FA"
msgstr ""

#: lib/menu_tools_twoFactor.php:232
msgid "Migrate or switch to new two-factor authentication?"
msgstr ""

#: lib/menu_tools_twoFactor.php:233
msgid "Use the buttons below to migrate to the new two-factor authentication system or switch without migration. Migration will copy all existing authenticator-based user activations over to the new system while switching will use only users already set up in the new system. Existing SMS-based two-factor authentication activations must be disabled prior to migration."
msgstr ""

#: lib/menu_tools_twoFactor.php:234
msgid "Migrate"
msgstr ""

#: lib/menu_tools_twoFactor.php:235
msgid "Switch"
msgstr ""

#: lib/menu_tools_twoFactor.php:243
msgid "New Two-Factor Authentication Active"
msgstr ""

#: lib/menu_tools_twoFactor.php:244
msgid "Your site is now using the new login security module and two-factor authentication. Before logging out, we recommend testing your login in a different browser or a private/incognito window. If any plugins or your theme cause conflicts with logging in, you can revert to the old 2FA method."
msgstr ""

#: lib/menu_tools_twoFactor.php:245
msgid "Go To New 2FA"
msgstr ""

#: lib/menu_tools_twoFactor.php:253
msgid "Migration Cannot Proceed"
msgstr ""

#: lib/menu_tools_twoFactor.php:254
msgid "One or more users with two-factor authentication active are using SMS, which is unsupported in the new login security module. Please either deactivate two-factor authentication for those users or change them to use an authenticator app prior to migration."
msgstr ""

#: lib/menu_tools_twoFactor.php:262
msgid "Migration Failed"
msgstr ""

#: lib/menu_tools_twoFactor.php:263
msgid "Automatic migration of the 2FA-enabled accounts failed. Please verify that your server is reachable via the internet and try again."
msgstr ""

#: lib/menu_tools_twoFactor.php:271
msgid "Revert back to legacy two-factor authentication?"
msgstr ""

#: lib/menu_tools_twoFactor.php:272
msgid "All two-factor authentication settings and users' codes will revert to your older settings. If any users had set up two-factor authentication after the update, they will no longer have 2FA enabled until you switch to the new version again."
msgstr ""

#: lib/menu_tools_twoFactor.php:274
msgid "Revert"
msgstr ""

#: lib/menu_tools_twoFactor.php:282
msgid "Legacy Two-Factor Authentication Active"
msgstr ""

#: lib/menu_tools_twoFactor.php:283
msgid "Your site is now using the legacy two-factor authentication system."
msgstr ""

#. translators: URL to support page.
#: lib/menu_tools_whois.php:19
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wf-help-link\">Learn more<span class=\"wf-hidden-xs\"> about Whois Lookup</span><span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/menu_tools_whois.php:23
msgid "The whois service gives you a way to look up who owns an IP address or domain name that is visiting your website or is engaging in malicious activity on your website."
msgstr ""

#: lib/menu_tools_whois.php:36
msgid "How to block a network"
msgstr ""

#. translators: Hostname or IP address.
#: lib/menu_tools_whois.php:40
msgid "You've chosen to block the network that <span style=\"color: #F00;\">%s</span> is part of. We've marked the networks we found that this IP address belongs to in red below. Make sure you read all the WHOIS information so that you see all networks this IP belongs to. We recommend blocking the network with the lowest number of addresses. You may find this is listed at the end as part of the 'rWHOIS' query which contacts the local WHOIS server that is run by the network administrator."
msgstr ""

#: lib/menu_tools_whois.php:95
#: lib/wordfenceClass.php:6334
msgid "Enter a valid IP or domain"
msgstr ""

#: lib/menu_tools_whois.php:95
#: lib/wordfenceClass.php:6378
msgid "Please enter a valid IP address or domain name for your whois lookup."
msgstr ""

#: lib/menu_tools_whois.php:99
#: lib/wordfenceClass.php:6359
msgid "Loading..."
msgstr ""

#: lib/menu_tools_whois.php:104
#: lib/wordfenceClass.php:6365
msgid "Look up IP or Domain"
msgstr ""

#: lib/menu_wordfence_central.php:10
msgid "Testing initial communication with Wordfence Central."
msgstr ""

#: lib/menu_wordfence_central.php:11
msgid "Passing public key to Wordfence Central."
msgstr ""

#: lib/menu_wordfence_central.php:12
msgid "Testing public key authentication with Wordfence Central."
msgstr ""

#: lib/menu_wordfence_central.php:13
msgid "Testing that Wordfence Central is able to communicate with this site."
msgstr ""

#: lib/menu_wordfence_central.php:14
msgid "Retrieving access token using authorization grant."
msgstr ""

#: lib/menu_wordfence_central.php:15
msgid "Redirecting back to Wordfence Central."
msgstr ""

#. translators: 1. Email address. 2. Localized date.
#: lib/menu_wordfence_central.php:64
msgid "Activated - connected by %1$s on %2$s"
msgstr ""

#: lib/menu_wordfence_central.php:74
msgid "Wordfence Central Installation Process"
msgstr ""

#: lib/menu_wordfence_central.php:109
msgid "Disconnect Site"
msgstr ""

#: lib/menu_wordfence_central.php:115
msgid "To connect your site your site to Wordfence Central, use the link below:"
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:67
msgid "Site is not connected to Wordfence Central."
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:76
#: lib/rest-api/wfRESTAuthenticationController.php:130
msgid "Data is invalid."
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:81
#: lib/rest-api/wfRESTAuthenticationController.php:135
msgid "Nonce format is invalid."
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:86
#: lib/rest-api/wfRESTAuthenticationController.php:91
msgid "Site ID is invalid."
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:102
#: lib/rest-api/wfRESTAuthenticationController.php:153
msgid "Nonce is invalid."
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:108
#: lib/rest-api/wfRESTAuthenticationController.php:159
msgid "Signature is invalid."
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:140
msgid "Email address is invalid."
msgstr ""

#: lib/rest-api/wfRESTAuthenticationController.php:179
msgid "Admin user with this email address not found."
msgstr ""

#: lib/rest-api/wfRESTBaseController.php:26
#: lib/rest-api/wfRESTBaseController.php:49
#: lib/rest-api/wfRESTBaseController.php:81
msgid "Token is invalid."
msgstr ""

#: lib/rest-api/wfRESTBaseController.php:64
msgid "Authorization header format is invalid."
msgstr ""

#: lib/rest-api/wfRESTConfigController.php:42
msgid "Specific config options to return."
msgstr ""

#: lib/rest-api/wfRESTConfigController.php:52
msgid "Specific config options to set."
msgstr ""

#. translators: Error message.
#: lib/rest-api/wfRESTConfigController.php:225
#: lib/rest-api/wfRESTConfigController.php:268
#: lib/wordfenceClass.php:4600
#: modules/login-security/classes/controller/ajax.php:393
msgid "An error occurred while saving the configuration: %s"
msgstr ""

#. translators: Error message.
#: lib/rest-api/wfRESTConfigController.php:236
#: lib/rest-api/wfRESTConfigController.php:279
#: lib/wordfenceClass.php:4609
#: modules/login-security/classes/controller/ajax.php:401
msgid "Errors occurred while saving the configuration: %s"
msgstr ""

#: lib/rest-api/wfRESTConfigController.php:241
#: lib/rest-api/wfRESTConfigController.php:284
#: lib/wordfenceClass.php:4614
#: modules/login-security/classes/controller/ajax.php:407
msgid "Errors occurred while saving the configuration."
msgstr ""

#. translators: Error message.
#: lib/rest-api/wfRESTConfigController.php:257
#: lib/rest-api/wfRESTConfigController.php:298
msgid "A server error occurred while saving the configuration: %s"
msgstr ""

#: lib/rest-api/wfRESTConfigController.php:303
msgid "Validation error: 'fields' parameter is empty or not an array."
msgstr ""

#: lib/rest-api/wfRESTScanController.php:16
msgid "Scan result group or all results."
msgstr ""

#: lib/rest-api/wfRESTScanController.php:21
msgid "Offset of scan results to return."
msgstr ""

#: lib/rest-api/wfRESTScanController.php:26
msgid "Number of scan results to return."
msgstr ""

#. translators: Localized date.
#: lib/rest-api/wfRESTScanController.php:84
msgid "Wordfence scan starting at %s from Wordfence Central"
msgstr ""

#: lib/rest-api/wfRESTScanController.php:127
msgid "Scan stop request received from Wordfence Central."
msgstr ""

#: lib/rest-api/wfRESTScanController.php:128
msgid "SUM_KILLED:A request was received to stop the previous scan from Wordfence Central."
msgstr ""

#: lib/sysinfo.php:5
msgid "Wordfence System Info"
msgstr ""

#: lib/viewFullActivityLog.php:12
msgid "Wordfence Full Activity Log"
msgstr ""

#: lib/wf503.php:5
msgid "Your access to this site has been limited"
msgstr ""

#: lib/wf503.php:351
msgid "Your access to this site has been limited by the site owner"
msgstr ""

#: lib/wf503.php:352
msgid "Your access to this service has been limited. (HTTP response code 503)"
msgstr ""

#: lib/wf503.php:353
#: lib/wfLockedOut.php:358
msgid "If you think you have been blocked in error, contact the owner of this site for assistance."
msgstr ""

#: lib/wf503.php:361
#: lib/wfLockedOut.php:370
msgid "Block Technical Data"
msgstr ""

#: lib/wf503.php:364
msgid "Block Reason"
msgstr ""

#: lib/wf503.php:384
#: lib/wfLockedOut.php:393
msgid "About Wordfence"
msgstr ""

#: lib/wf503.php:385
#: lib/wfLockedOut.php:394
msgid "Wordfence is a security plugin installed on over 5 million WordPress sites. The owner of this site is using Wordfence to manage access to their site."
msgstr ""

#: lib/wf503.php:386
#: lib/wfLockedOut.php:395
msgid "You can also read the documentation to learn about Wordfence's blocking tools, or visit wordfence.com to learn more about Wordfence."
msgstr ""

#. translators: Support URL.
#: lib/wf503.php:390
msgid "Click here to learn more: <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Documentation<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: Localized date.
#: lib/wf503.php:391
#: lib/wfLockedOut.php:400
msgid "Generated by Wordfence at %s"
msgstr ""

#. translators: Localized date.
#: lib/wf503.php:391
#: lib/wfLockedOut.php:400
msgid "Your computer's time:"
msgstr ""

#. translators: URL to the WordPress admin panel.
#: lib/wfActivityReport.php:508
#: lib/wfIssues.php:496
msgid "No longer an administrator for this site? <a href=\"%s\" target=\"_blank\">Click here</a> to stop receiving security alerts."
msgstr ""

#. translators: 1. Site URL. 2. Localized date.
#: lib/wfActivityReport.php:509
msgid "Wordfence activity for %1$s on %2$s"
msgstr ""

#: lib/wfActivityReport.php:585
#: lib/wordfenceClass.php:8642
msgid "Blocked because the IP is blocklisted"
msgstr ""

#. translators: Reason for firewall action.
#: lib/wfActivityReport.php:588
#: lib/wordfenceClass.php:8645
msgid "Blocked for %s"
msgstr ""

#. translators: 1. Reason for firewall action. 2. Input parameter. 2. Input parameter value.
#: lib/wfActivityReport.php:600
#: lib/wordfenceClass.php:8659
msgid "Blocked for %1$s in query string: %2$s = %3$s"
msgstr ""

#. translators: 1. Reason for firewall action. 2. Input parameter. 2. Input parameter value.
#: lib/wfActivityReport.php:603
#: lib/wordfenceClass.php:8664
msgid "Blocked for %1$s in POST body: %2$s = %3$s"
msgstr ""

#. translators: 1. Reason for firewall action. 2. Input parameter. 2. Input parameter value.
#: lib/wfActivityReport.php:606
#: lib/wordfenceClass.php:8669
msgid "Blocked for %1$s in cookie: %2$s = %3$s"
msgstr ""

#. translators: 1. Reason for firewall action. 2. Input parameter. 2. Input parameter value.
#: lib/wfActivityReport.php:609
#: lib/wordfenceClass.php:8674
msgid "Blocked for %1$s in file: %2$s = %3$s"
msgstr ""

#: lib/wfActivityReport.php:753
#: lib/wfDiagnostic.php:590
#: models/block/wfBlock.php:95
msgid "Unknown"
msgstr ""

#: lib/wfAdminNoticeQueue.php:193
#: lib/wfCentralAPI.php:910
#: lib/wfSupportController.php:437
#: lib/wordfenceClass.php:6327
#: lib/wordfenceClass.php:6585
#: lib/wordfenceClass.php:6620
#: lib/wordfenceClass.php:9262
msgid "Dismiss"
msgstr ""

#. translators: IP address.
#: lib/wfAlerts.php:29
msgid "Wordfence has blocked IP address %s."
msgstr ""

#. translators: Description of firewall action.
#: lib/wfAlerts.php:30
msgid "The reason is: \"%s\"."
msgstr ""

#. translators: Time until.
#: lib/wfAlerts.php:32
msgid "The duration of the block is %s."
msgstr ""

#. translators: IP address.
#: lib/wfAlerts.php:34
msgid "Blocking IP %s"
msgstr ""

#. translators: Software version.
#: lib/wfAlerts.php:53
msgid "Wordfence Upgraded to version %s"
msgstr ""

#. translators: Software version.
#: lib/wfAlerts.php:53
msgid "Your Wordfence installation has been upgraded to version %s"
msgstr ""

#: lib/wfAlerts.php:75
msgid "Wordfence WAF Deactivated"
msgstr ""

#. translators: WP username.
#: lib/wfAlerts.php:75
msgid "A user with username \"%s\" deactivated the Wordfence Web Application Firewall on your WordPress site."
msgstr ""

#: lib/wfAlerts.php:96
msgid "Wordfence Deactivated"
msgstr ""

#. translators: WP username.
#: lib/wfAlerts.php:96
msgid "A user with username \"%s\" deactivated Wordfence on your WordPress site."
msgstr ""

#: lib/wfAlerts.php:118
msgid "Password recovery attempted"
msgstr ""

#. translators: Email address.
#: lib/wfAlerts.php:118
msgid "Someone tried to recover the password for user with email address: %s"
msgstr ""

#. translators: 1. IP address. 2. Description of firewall action.
#: lib/wfAlerts.php:142
msgid "A user with IP address %1$s has been locked out from signing in or using the password recovery form for the following reason: %2$s."
msgstr ""

#. translators: Time until.
#: lib/wfAlerts.php:144
msgid "The duration of the lockout is %s."
msgstr ""

#: lib/wfAlerts.php:146
msgid "User locked out from signing in"
msgstr ""

#: lib/wfAlerts.php:179
msgid "Admin Login"
msgstr ""

#. translators: WP username.
#: lib/wfAlerts.php:179
msgid "A user with username \"%s\" who has administrator access signed in to your WordPress site."
msgstr ""

#: lib/wfAlerts.php:213
msgid "User login"
msgstr ""

#. translators: WP username.
#: lib/wfAlerts.php:213
msgid "A non-admin user with username \"%s\" signed in to your WordPress site."
msgstr ""

#: lib/wfAlerts.php:241
msgid "User login blocked for insecure password"
msgstr ""

#. translators: 1. WP username. 2. Reset password URL. 3. Support URL.
#: lib/wfAlerts.php:243
msgid "A user with username \"%1$s\" tried to sign in to your WordPress site. Access was denied because the password being used exists on lists of passwords leaked in data breaches. Attackers use such lists to break into sites and install malicious code. Please change or reset the password (%2$s) to reactivate this account. Learn More: %3$s"
msgstr ""

#: lib/wfAlerts.php:260
msgid "Increased Attack Rate"
msgstr ""

#: lib/wfAPI.php:28
msgid "SSL is not supported by your web server and is required to use this function. Please ask your hosting provider or site admin to install cURL with openSSL to use this feature."
msgstr ""

#. translators: API call/action/endpoint.
#: lib/wfAPI.php:36
msgid "We received an empty data response from the Wordfence scanning servers when calling the '%s' function."
msgstr ""

#. translators: API call/action/endpoint.
#: lib/wfAPI.php:42
msgid "We received a data structure that is not the expected array when contacting the Wordfence scanning servers and calling the '%s' function."
msgstr ""

#: lib/wfAPI.php:80
msgid "The Wordfence license you're using does not match this site's address. Premium features are disabled."
msgstr ""

#. translators: API version.
#: lib/wfAPI.php:100
msgid "Calling Wordfence API v%s:"
msgstr ""

#. translators: Error message.
#: lib/wfAPI.php:126
msgid "There was an error connecting to the Wordfence scanning servers: %s"
msgstr ""

#: lib/wfAPI.php:128
msgid "There was an unknown error connecting to the Wordfence scanning servers."
msgstr ""

#. translators: HTTP status code.
#: lib/wfAPI.php:167
msgid "The Wordfence scanning servers are currently unavailable. This may be for maintenance or a temporary outage. If this still occurs in an hour, please contact support. [%s]"
msgstr ""

#: lib/wfAuditLog.php:183
msgid "Unknown Events"
msgstr ""

#: lib/wfBulkCountries.php:5
msgid "Andorra"
msgstr ""

#: lib/wfBulkCountries.php:6
msgid "United Arab Emirates"
msgstr ""

#: lib/wfBulkCountries.php:7
msgid "Afghanistan"
msgstr ""

#: lib/wfBulkCountries.php:8
msgid "Antigua and Barbuda"
msgstr ""

#: lib/wfBulkCountries.php:9
msgid "Anguilla"
msgstr ""

#: lib/wfBulkCountries.php:10
msgid "Albania"
msgstr ""

#: lib/wfBulkCountries.php:11
msgid "Armenia"
msgstr ""

#: lib/wfBulkCountries.php:12
msgid "Angola"
msgstr ""

#: lib/wfBulkCountries.php:13
msgid "Antarctica"
msgstr ""

#: lib/wfBulkCountries.php:14
msgid "Argentina"
msgstr ""

#: lib/wfBulkCountries.php:15
msgid "American Samoa"
msgstr ""

#: lib/wfBulkCountries.php:16
msgid "Austria"
msgstr ""

#: lib/wfBulkCountries.php:17
msgid "Australia"
msgstr ""

#: lib/wfBulkCountries.php:18
msgid "Aruba"
msgstr ""

#: lib/wfBulkCountries.php:19
msgid "Aland Islands"
msgstr ""

#: lib/wfBulkCountries.php:20
msgid "Azerbaijan"
msgstr ""

#: lib/wfBulkCountries.php:21
msgid "Bosnia and Herzegovina"
msgstr ""

#: lib/wfBulkCountries.php:22
msgid "Barbados"
msgstr ""

#: lib/wfBulkCountries.php:23
msgid "Bangladesh"
msgstr ""

#: lib/wfBulkCountries.php:24
msgid "Belgium"
msgstr ""

#: lib/wfBulkCountries.php:25
msgid "Burkina Faso"
msgstr ""

#: lib/wfBulkCountries.php:26
msgid "Bulgaria"
msgstr ""

#: lib/wfBulkCountries.php:27
msgid "Bahrain"
msgstr ""

#: lib/wfBulkCountries.php:28
msgid "Burundi"
msgstr ""

#: lib/wfBulkCountries.php:29
msgid "Benin"
msgstr ""

#: lib/wfBulkCountries.php:30
msgid "Saint Bartelemey"
msgstr ""

#: lib/wfBulkCountries.php:31
msgid "Bermuda"
msgstr ""

#: lib/wfBulkCountries.php:32
msgid "Brunei Darussalam"
msgstr ""

#: lib/wfBulkCountries.php:33
msgid "Bolivia"
msgstr ""

#: lib/wfBulkCountries.php:34
msgid "Bonaire, Saint Eustatius and Saba"
msgstr ""

#: lib/wfBulkCountries.php:35
msgid "Brazil"
msgstr ""

#: lib/wfBulkCountries.php:36
msgid "Bahamas"
msgstr ""

#: lib/wfBulkCountries.php:37
msgid "Bhutan"
msgstr ""

#: lib/wfBulkCountries.php:38
msgid "Bouvet Island"
msgstr ""

#: lib/wfBulkCountries.php:39
msgid "Botswana"
msgstr ""

#: lib/wfBulkCountries.php:40
msgid "Belarus"
msgstr ""

#: lib/wfBulkCountries.php:41
msgid "Belize"
msgstr ""

#: lib/wfBulkCountries.php:42
msgid "Canada"
msgstr ""

#: lib/wfBulkCountries.php:43
msgid "Cocos (Keeling) Islands"
msgstr ""

#: lib/wfBulkCountries.php:44
msgid "Congo, The Democratic Republic of the"
msgstr ""

#: lib/wfBulkCountries.php:45
msgid "Central African Republic"
msgstr ""

#: lib/wfBulkCountries.php:46
msgid "Congo"
msgstr ""

#: lib/wfBulkCountries.php:47
msgid "Switzerland"
msgstr ""

#: lib/wfBulkCountries.php:48
msgid "Cote dIvoire"
msgstr ""

#: lib/wfBulkCountries.php:49
msgid "Cook Islands"
msgstr ""

#: lib/wfBulkCountries.php:50
msgid "Chile"
msgstr ""

#: lib/wfBulkCountries.php:51
msgid "Cameroon"
msgstr ""

#: lib/wfBulkCountries.php:52
msgid "China"
msgstr ""

#: lib/wfBulkCountries.php:53
msgid "Colombia"
msgstr ""

#: lib/wfBulkCountries.php:54
msgid "Costa Rica"
msgstr ""

#: lib/wfBulkCountries.php:55
msgid "Cuba"
msgstr ""

#: lib/wfBulkCountries.php:56
msgid "Cape Verde"
msgstr ""

#: lib/wfBulkCountries.php:57
msgid "Curacao"
msgstr ""

#: lib/wfBulkCountries.php:58
msgid "Christmas Island"
msgstr ""

#: lib/wfBulkCountries.php:59
msgid "Cyprus"
msgstr ""

#: lib/wfBulkCountries.php:60
msgid "Czech Republic"
msgstr ""

#: lib/wfBulkCountries.php:61
msgid "Germany"
msgstr ""

#: lib/wfBulkCountries.php:62
msgid "Djibouti"
msgstr ""

#: lib/wfBulkCountries.php:63
msgid "Denmark"
msgstr ""

#: lib/wfBulkCountries.php:64
msgid "Dominica"
msgstr ""

#: lib/wfBulkCountries.php:65
msgid "Dominican Republic"
msgstr ""

#: lib/wfBulkCountries.php:66
msgid "Algeria"
msgstr ""

#: lib/wfBulkCountries.php:67
msgid "Ecuador"
msgstr ""

#: lib/wfBulkCountries.php:68
msgid "Estonia"
msgstr ""

#: lib/wfBulkCountries.php:69
msgid "Egypt"
msgstr ""

#: lib/wfBulkCountries.php:70
msgid "Western Sahara"
msgstr ""

#: lib/wfBulkCountries.php:71
msgid "Eritrea"
msgstr ""

#: lib/wfBulkCountries.php:72
msgid "Spain"
msgstr ""

#: lib/wfBulkCountries.php:73
msgid "Ethiopia"
msgstr ""

#: lib/wfBulkCountries.php:74
msgid "Europe"
msgstr ""

#: lib/wfBulkCountries.php:75
msgid "Finland"
msgstr ""

#: lib/wfBulkCountries.php:76
msgid "Fiji"
msgstr ""

#: lib/wfBulkCountries.php:77
msgid "Falkland Islands (Malvinas)"
msgstr ""

#: lib/wfBulkCountries.php:78
msgid "Micronesia, Federated States of"
msgstr ""

#: lib/wfBulkCountries.php:79
msgid "Faroe Islands"
msgstr ""

#: lib/wfBulkCountries.php:80
msgid "France"
msgstr ""

#: lib/wfBulkCountries.php:81
msgid "Gabon"
msgstr ""

#: lib/wfBulkCountries.php:82
msgid "United Kingdom"
msgstr ""

#: lib/wfBulkCountries.php:83
msgid "Grenada"
msgstr ""

#: lib/wfBulkCountries.php:84
msgid "Georgia"
msgstr ""

#: lib/wfBulkCountries.php:85
msgid "French Guiana"
msgstr ""

#: lib/wfBulkCountries.php:86
msgid "Guernsey"
msgstr ""

#: lib/wfBulkCountries.php:87
msgid "Ghana"
msgstr ""

#: lib/wfBulkCountries.php:88
msgid "Gibraltar"
msgstr ""

#: lib/wfBulkCountries.php:89
msgid "Greenland"
msgstr ""

#: lib/wfBulkCountries.php:90
msgid "Gambia"
msgstr ""

#: lib/wfBulkCountries.php:91
msgid "Guinea"
msgstr ""

#: lib/wfBulkCountries.php:92
msgid "Guadeloupe"
msgstr ""

#: lib/wfBulkCountries.php:93
msgid "Equatorial Guinea"
msgstr ""

#: lib/wfBulkCountries.php:94
msgid "Greece"
msgstr ""

#: lib/wfBulkCountries.php:95
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: lib/wfBulkCountries.php:96
msgid "Guatemala"
msgstr ""

#: lib/wfBulkCountries.php:97
msgid "Guam"
msgstr ""

#: lib/wfBulkCountries.php:98
msgid "Guinea-Bissau"
msgstr ""

#: lib/wfBulkCountries.php:99
msgid "Guyana"
msgstr ""

#: lib/wfBulkCountries.php:100
msgid "Hong Kong"
msgstr ""

#: lib/wfBulkCountries.php:101
msgid "Heard Island and McDonald Islands"
msgstr ""

#: lib/wfBulkCountries.php:102
msgid "Honduras"
msgstr ""

#: lib/wfBulkCountries.php:103
msgid "Croatia"
msgstr ""

#: lib/wfBulkCountries.php:104
msgid "Haiti"
msgstr ""

#: lib/wfBulkCountries.php:105
msgid "Hungary"
msgstr ""

#: lib/wfBulkCountries.php:106
msgid "Indonesia"
msgstr ""

#: lib/wfBulkCountries.php:107
msgid "Ireland"
msgstr ""

#: lib/wfBulkCountries.php:108
msgid "Israel"
msgstr ""

#: lib/wfBulkCountries.php:109
msgid "Isle of Man"
msgstr ""

#: lib/wfBulkCountries.php:110
msgid "India"
msgstr ""

#: lib/wfBulkCountries.php:111
msgid "British Indian Ocean Territory"
msgstr ""

#: lib/wfBulkCountries.php:112
msgid "Iraq"
msgstr ""

#: lib/wfBulkCountries.php:113
msgid "Iran, Islamic Republic of"
msgstr ""

#: lib/wfBulkCountries.php:114
msgid "Iceland"
msgstr ""

#: lib/wfBulkCountries.php:115
msgid "Italy"
msgstr ""

#: lib/wfBulkCountries.php:116
msgid "Jersey"
msgstr ""

#: lib/wfBulkCountries.php:117
msgid "Jamaica"
msgstr ""

#: lib/wfBulkCountries.php:118
msgid "Jordan"
msgstr ""

#: lib/wfBulkCountries.php:119
msgid "Japan"
msgstr ""

#: lib/wfBulkCountries.php:120
msgid "Kenya"
msgstr ""

#: lib/wfBulkCountries.php:121
msgid "Kyrgyzstan"
msgstr ""

#: lib/wfBulkCountries.php:122
msgid "Cambodia"
msgstr ""

#: lib/wfBulkCountries.php:123
msgid "Kiribati"
msgstr ""

#: lib/wfBulkCountries.php:124
msgid "Comoros"
msgstr ""

#: lib/wfBulkCountries.php:125
msgid "Saint Kitts and Nevis"
msgstr ""

#: lib/wfBulkCountries.php:126
msgid "North Korea"
msgstr ""

#: lib/wfBulkCountries.php:127
msgid "South Korea"
msgstr ""

#: lib/wfBulkCountries.php:128
msgid "Kuwait"
msgstr ""

#: lib/wfBulkCountries.php:129
msgid "Cayman Islands"
msgstr ""

#: lib/wfBulkCountries.php:130
msgid "Kazakhstan"
msgstr ""

#: lib/wfBulkCountries.php:131
msgid "Lao Peoples Democratic Republic"
msgstr ""

#: lib/wfBulkCountries.php:132
msgid "Lebanon"
msgstr ""

#: lib/wfBulkCountries.php:133
msgid "Saint Lucia"
msgstr ""

#: lib/wfBulkCountries.php:134
msgid "Liechtenstein"
msgstr ""

#: lib/wfBulkCountries.php:135
msgid "Sri Lanka"
msgstr ""

#: lib/wfBulkCountries.php:136
msgid "Liberia"
msgstr ""

#: lib/wfBulkCountries.php:137
msgid "Lesotho"
msgstr ""

#: lib/wfBulkCountries.php:138
msgid "Lithuania"
msgstr ""

#: lib/wfBulkCountries.php:139
msgid "Luxembourg"
msgstr ""

#: lib/wfBulkCountries.php:140
msgid "Latvia"
msgstr ""

#: lib/wfBulkCountries.php:141
msgid "Libyan Arab Jamahiriya"
msgstr ""

#: lib/wfBulkCountries.php:142
msgid "Morocco"
msgstr ""

#: lib/wfBulkCountries.php:143
msgid "Monaco"
msgstr ""

#: lib/wfBulkCountries.php:144
msgid "Moldova, Republic of"
msgstr ""

#: lib/wfBulkCountries.php:145
msgid "Montenegro"
msgstr ""

#: lib/wfBulkCountries.php:146
msgid "Saint Martin"
msgstr ""

#: lib/wfBulkCountries.php:147
msgid "Madagascar"
msgstr ""

#: lib/wfBulkCountries.php:148
msgid "Marshall Islands"
msgstr ""

#: lib/wfBulkCountries.php:149
msgid "North Macedonia, Republic of"
msgstr ""

#: lib/wfBulkCountries.php:150
msgid "Mali"
msgstr ""

#: lib/wfBulkCountries.php:151
msgid "Myanmar"
msgstr ""

#: lib/wfBulkCountries.php:152
msgid "Mongolia"
msgstr ""

#: lib/wfBulkCountries.php:153
msgid "Macao"
msgstr ""

#: lib/wfBulkCountries.php:154
msgid "Northern Mariana Islands"
msgstr ""

#: lib/wfBulkCountries.php:155
msgid "Martinique"
msgstr ""

#: lib/wfBulkCountries.php:156
msgid "Mauritania"
msgstr ""

#: lib/wfBulkCountries.php:157
msgid "Montserrat"
msgstr ""

#: lib/wfBulkCountries.php:158
msgid "Malta"
msgstr ""

#: lib/wfBulkCountries.php:159
msgid "Mauritius"
msgstr ""

#: lib/wfBulkCountries.php:160
msgid "Maldives"
msgstr ""

#: lib/wfBulkCountries.php:161
msgid "Malawi"
msgstr ""

#: lib/wfBulkCountries.php:162
msgid "Mexico"
msgstr ""

#: lib/wfBulkCountries.php:163
msgid "Malaysia"
msgstr ""

#: lib/wfBulkCountries.php:164
msgid "Mozambique"
msgstr ""

#: lib/wfBulkCountries.php:165
msgid "Namibia"
msgstr ""

#: lib/wfBulkCountries.php:166
msgid "New Caledonia"
msgstr ""

#: lib/wfBulkCountries.php:167
msgid "Niger"
msgstr ""

#: lib/wfBulkCountries.php:168
msgid "Norfolk Island"
msgstr ""

#: lib/wfBulkCountries.php:169
msgid "Nigeria"
msgstr ""

#: lib/wfBulkCountries.php:170
msgid "Nicaragua"
msgstr ""

#: lib/wfBulkCountries.php:171
msgid "Netherlands"
msgstr ""

#: lib/wfBulkCountries.php:172
msgid "Norway"
msgstr ""

#: lib/wfBulkCountries.php:173
msgid "Nepal"
msgstr ""

#: lib/wfBulkCountries.php:174
msgid "Nauru"
msgstr ""

#: lib/wfBulkCountries.php:175
msgid "Niue"
msgstr ""

#: lib/wfBulkCountries.php:176
msgid "New Zealand"
msgstr ""

#: lib/wfBulkCountries.php:177
msgid "Oman"
msgstr ""

#: lib/wfBulkCountries.php:178
msgid "Panama"
msgstr ""

#: lib/wfBulkCountries.php:179
msgid "Peru"
msgstr ""

#: lib/wfBulkCountries.php:180
msgid "French Polynesia"
msgstr ""

#: lib/wfBulkCountries.php:181
msgid "Papua New Guinea"
msgstr ""

#: lib/wfBulkCountries.php:182
msgid "Philippines"
msgstr ""

#: lib/wfBulkCountries.php:183
msgid "Pakistan"
msgstr ""

#: lib/wfBulkCountries.php:184
msgid "Poland"
msgstr ""

#: lib/wfBulkCountries.php:185
msgid "Saint Pierre and Miquelon"
msgstr ""

#: lib/wfBulkCountries.php:186
msgid "Pitcairn"
msgstr ""

#: lib/wfBulkCountries.php:187
msgid "Puerto Rico"
msgstr ""

#: lib/wfBulkCountries.php:188
msgid "Palestinian Territory"
msgstr ""

#: lib/wfBulkCountries.php:189
msgid "Portugal"
msgstr ""

#: lib/wfBulkCountries.php:190
msgid "Palau"
msgstr ""

#: lib/wfBulkCountries.php:191
msgid "Paraguay"
msgstr ""

#: lib/wfBulkCountries.php:192
msgid "Qatar"
msgstr ""

#: lib/wfBulkCountries.php:193
msgid "Reunion"
msgstr ""

#: lib/wfBulkCountries.php:194
msgid "Romania"
msgstr ""

#: lib/wfBulkCountries.php:195
msgid "Serbia"
msgstr ""

#: lib/wfBulkCountries.php:196
msgid "Russian Federation"
msgstr ""

#: lib/wfBulkCountries.php:197
msgid "Rwanda"
msgstr ""

#: lib/wfBulkCountries.php:198
msgid "Saudi Arabia"
msgstr ""

#: lib/wfBulkCountries.php:199
msgid "Solomon Islands"
msgstr ""

#: lib/wfBulkCountries.php:200
msgid "Seychelles"
msgstr ""

#: lib/wfBulkCountries.php:201
msgid "Sudan"
msgstr ""

#: lib/wfBulkCountries.php:202
msgid "Sweden"
msgstr ""

#: lib/wfBulkCountries.php:203
msgid "Singapore"
msgstr ""

#: lib/wfBulkCountries.php:204
msgid "Saint Helena"
msgstr ""

#: lib/wfBulkCountries.php:205
msgid "Slovenia"
msgstr ""

#: lib/wfBulkCountries.php:206
msgid "Svalbard and Jan Mayen"
msgstr ""

#: lib/wfBulkCountries.php:207
msgid "Slovakia"
msgstr ""

#: lib/wfBulkCountries.php:208
msgid "Sierra Leone"
msgstr ""

#: lib/wfBulkCountries.php:209
msgid "San Marino"
msgstr ""

#: lib/wfBulkCountries.php:210
msgid "Senegal"
msgstr ""

#: lib/wfBulkCountries.php:211
msgid "Somalia"
msgstr ""

#: lib/wfBulkCountries.php:212
msgid "Suriname"
msgstr ""

#: lib/wfBulkCountries.php:213
msgid "Sao Tome and Principe"
msgstr ""

#: lib/wfBulkCountries.php:214
msgid "El Salvador"
msgstr ""

#: lib/wfBulkCountries.php:215
msgid "Sint Maarten"
msgstr ""

#: lib/wfBulkCountries.php:216
msgid "Syrian Arab Republic"
msgstr ""

#: lib/wfBulkCountries.php:217
msgid "Swaziland"
msgstr ""

#: lib/wfBulkCountries.php:218
msgid "Turks and Caicos Islands"
msgstr ""

#: lib/wfBulkCountries.php:219
msgid "Chad"
msgstr ""

#: lib/wfBulkCountries.php:220
msgid "French Southern Territories"
msgstr ""

#: lib/wfBulkCountries.php:221
msgid "Togo"
msgstr ""

#: lib/wfBulkCountries.php:222
msgid "Thailand"
msgstr ""

#: lib/wfBulkCountries.php:223
msgid "Tajikistan"
msgstr ""

#: lib/wfBulkCountries.php:224
msgid "Tokelau"
msgstr ""

#: lib/wfBulkCountries.php:225
msgid "Timor-Leste"
msgstr ""

#: lib/wfBulkCountries.php:226
msgid "Turkmenistan"
msgstr ""

#: lib/wfBulkCountries.php:227
msgid "Tunisia"
msgstr ""

#: lib/wfBulkCountries.php:228
msgid "Tonga"
msgstr ""

#: lib/wfBulkCountries.php:229
msgid "Turkey"
msgstr ""

#: lib/wfBulkCountries.php:230
msgid "Trinidad and Tobago"
msgstr ""

#: lib/wfBulkCountries.php:231
msgid "Tuvalu"
msgstr ""

#: lib/wfBulkCountries.php:232
msgid "Taiwan"
msgstr ""

#: lib/wfBulkCountries.php:233
msgid "Tanzania, United Republic of"
msgstr ""

#: lib/wfBulkCountries.php:234
msgid "Ukraine"
msgstr ""

#: lib/wfBulkCountries.php:235
msgid "Uganda"
msgstr ""

#: lib/wfBulkCountries.php:236
msgid "United States Minor Outlying Islands"
msgstr ""

#: lib/wfBulkCountries.php:237
msgid "United States"
msgstr ""

#: lib/wfBulkCountries.php:238
msgid "Uruguay"
msgstr ""

#: lib/wfBulkCountries.php:239
msgid "Uzbekistan"
msgstr ""

#: lib/wfBulkCountries.php:240
msgid "Holy See (Vatican City State)"
msgstr ""

#: lib/wfBulkCountries.php:241
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: lib/wfBulkCountries.php:242
msgid "Venezuela"
msgstr ""

#: lib/wfBulkCountries.php:243
msgid "Virgin Islands, British"
msgstr ""

#: lib/wfBulkCountries.php:244
msgid "Virgin Islands, U.S."
msgstr ""

#: lib/wfBulkCountries.php:245
msgid "Vietnam"
msgstr ""

#: lib/wfBulkCountries.php:246
msgid "Vanuatu"
msgstr ""

#: lib/wfBulkCountries.php:247
msgid "Wallis and Futuna"
msgstr ""

#: lib/wfBulkCountries.php:248
msgid "Samoa"
msgstr ""

#: lib/wfBulkCountries.php:249
msgid "Kosovo"
msgstr ""

#: lib/wfBulkCountries.php:250
msgid "Yemen"
msgstr ""

#: lib/wfBulkCountries.php:251
msgid "Mayotte"
msgstr ""

#: lib/wfBulkCountries.php:252
msgid "South Africa"
msgstr ""

#: lib/wfBulkCountries.php:253
msgid "Zambia"
msgstr ""

#: lib/wfBulkCountries.php:254
msgid "Zimbabwe"
msgstr ""

#. translators: 1. HTTP status code. 2. Error message.
#: lib/wfCentralAPI.php:212
msgid "HTTP %1$d received from Wordfence Central: %2$s"
msgstr ""

#: lib/wfCentralAPI.php:268
msgid "Fetching token for Wordfence Central authentication due to configuration issue."
msgstr ""

#: lib/wfCentralAPI.php:277
#: lib/wfCentralAPI.php:339
msgid "Unable to authenticate with Wordfence Central."
msgstr ""

#: lib/wfCentralAPI.php:297
msgid "Wordfence Central site ID has not been created yet."
msgstr ""

#: lib/wfCentralAPI.php:301
msgid "Wordfence Central secret key has not been created yet."
msgstr ""

#: lib/wfCentralAPI.php:313
msgid "Invalid response received from Wordfence Central when fetching nonce."
msgstr ""

#: lib/wfCentralAPI.php:336
msgid "Invalid response received from Wordfence Central when fetching token."
msgstr ""

#: lib/wfCentralAPI.php:897
msgid "Your site is currently linked to Wordfence Central under a different site URL."
msgstr ""

#: lib/wfCentralAPI.php:899
msgid "This may cause duplicated scan issues if both sites are currently active and reporting and is generally caused by duplicating the database from one site to another (e.g., from a production site to staging). We recommend disconnecting this site only, which will leave the matching site still connected."
msgstr ""

#: lib/wfCentralAPI.php:901
msgid "If this is a single site with multiple domains or subdomains, you can dismiss this message."
msgstr ""

#: lib/wfCentralAPI.php:907
msgid "Disconnect All"
msgstr ""

#. translators: 1. Key in key-value store. 2. Value in key-value store.
#: lib/wfConfig.php:500
msgid "wfConfig::set() got an array as second param with key: %1$s and value: %2$s"
msgstr ""

#. translators: Key in key-value store.
#: lib/wfConfig.php:667
msgid "Error reassembling value for %s"
msgstr ""

#. translators: 1. Key in key-value store. 2. MySQL error number. 3. MySQL error message.
#: lib/wfConfig.php:763
#: lib/wfConfig.php:772
#: lib/wfConfig.php:779
#: lib/wfConfig.php:789
#: lib/wfConfig.php:796
msgid "Error writing value chunk for %1$s (MySQLi error: [%2$s] %3$s)"
msgstr ""

#. translators: Key in key-value store.
#: lib/wfConfig.php:808
msgid "Error writing value header for %s"
msgstr ""

#. translators: 1. Key in key-value store. 2. MySQL error number. 3. MySQL error message.
#: lib/wfConfig.php:821
#: lib/wfConfig.php:832
#: lib/wfConfig.php:842
msgid "Error writing value for %1$s (MySQLi error: [%2$s] %3$s)"
msgstr ""

#. translators: 1. Key in key-value store. 2. MySQL error number. 3. MySQL error message.
#: lib/wfConfig.php:849
msgid "Error finishing writing value for %1$s (MySQLi error: [%2$s] %3$s)"
msgstr ""

#. translators: Support URL.
#: lib/wfConfig.php:1023
msgid "Wordfence Upgrade not run. Please modify your .htaccess"
msgstr ""

#. translators: Support URL.
#: lib/wfConfig.php:1023
msgid ""
"To preserve the integrity of your website we are not running Wordfence auto-update.\n"
"You are running the LiteSpeed web server which has been known to cause a problem with Wordfence auto-update.\n"
"Please go to your website now and make a minor change to your .htaccess to fix this.\n"
"You can find out how to make this change at:\n"
"%s\n"
"\n"
"Alternatively you can disable auto-update on your website to stop receiving this message and upgrade Wordfence manually.\n"
""
msgstr ""

#: lib/wfConfig.php:1146
msgid "Unable to save the .htaccess file needed to disable script execution in the uploads directory. Please check your permissions on that directory."
msgstr ""

#: lib/wfConfig.php:1183
msgid "Unable to remove code execution protections applied to the .htaccess file in the uploads directory. Please check your permissions on that file."
msgstr ""

#: lib/wfConfig.php:1220
#: modules/login-security/classes/controller/settings.php:215
msgid "The grace period end time must be in the future."
msgstr ""

#: lib/wfConfig.php:1229
msgid "Unknown firewall mode."
msgstr ""

#: lib/wfConfig.php:1249
msgid "The following emails are invalid: "
msgstr ""

#. translators: Regular expression.
#: lib/wfConfig.php:1263
msgid "\"%s\" is not a valid regular expression."
msgstr ""

#: lib/wfConfig.php:1283
msgid "Please make sure you separate your IP addresses with commas or newlines. The following allowlisted IP addresses are invalid: "
msgstr ""

#: lib/wfConfig.php:1303
msgid "The following users you selected to ignore in live traffic reports are not valid on this system: "
msgstr ""

#: lib/wfConfig.php:1321
msgid "The following IPs you selected to ignore in live traffic reports are not valid: "
msgstr ""

#: lib/wfConfig.php:1339
msgid "The following IPs/ranges you selected to trust as proxies are not valid: "
msgstr ""

#: lib/wfConfig.php:1353
msgid "The selected trusted proxy preset is not valid: "
msgstr ""

#: lib/wfConfig.php:1364
msgid "An empty license key was entered."
msgstr ""

#: lib/wfConfig.php:1367
#: lib/wordfenceClass.php:4473
msgid "The license key entered is not in a valid format. It must contain only numbers and the letters A-F."
msgstr ""

#: lib/wfConfig.php:1379
msgid "A wildcard cannot be used to exclude all files from the scan."
msgstr ""

#: lib/wfConfig.php:1390
msgid "Invalid number of scan resume attempts specified: %d"
msgstr ""

#: lib/wfConfig.php:1976
#: lib/wfConfig.php:1998
msgid "The Wordfence server's response did not contain the expected elements."
msgstr ""

#: lib/wfConfig.php:1980
msgid "Your options have been saved, but you left your license key blank, so we tried to get you a free license key from the Wordfence servers. There was a problem fetching the free key: "
msgstr ""

#: lib/wfConfig.php:2002
msgid "Your options have been saved. However we noticed you changed your license key, and we tried to verify it with the Wordfence servers but received an error: "
msgstr ""

#: lib/wfConfig.php:2050
msgid "Your options have been saved. However we tried to verify your license key with the Wordfence servers and received an error: "
msgstr ""

#: lib/wfDashboard.php:184
msgid "Complex"
msgstr ""

#: lib/wfDashboard.php:189
msgid "Brute Force"
msgstr ""

#: lib/wfDashboard.php:194
msgid "Blocklist"
msgstr ""

#: lib/wfDeactivationOption.php:50
msgid "Keep all Wordfence tables and data"
msgstr ""

#: lib/wfDeactivationOption.php:51
msgid "Delete Wordfence tables and data, but keep Login Security tables and 2FA codes"
msgstr ""

#: lib/wfDeactivationOption.php:52
msgid "Delete Login Security tables and 2FA codes, but keep Wordfence tables and data"
msgstr ""

#: lib/wfDeactivationOption.php:53
msgid "Delete all Wordfence tables and data"
msgstr ""

#: lib/wfDiagnostic.php:57
msgid "General information about the Wordfence installation."
msgstr ""

#: lib/wfDiagnostic.php:59
msgid "Wordfence Version"
msgstr ""

#: lib/wfDiagnostic.php:60
msgid "GeoIP Version"
msgstr ""

#: lib/wfDiagnostic.php:61
msgid "Cron Status"
msgstr ""

#: lib/wfDiagnostic.php:65
msgid "Ability to read/write various files."
msgstr ""

#: lib/wfDiagnostic.php:67
msgid "Checking if web server can read from <code>~/plugins/wordfence</code>"
msgstr ""

#: lib/wfDiagnostic.php:68
msgid "Checking if web server can write to <code>~/plugins/wordfence</code>"
msgstr ""

#: lib/wfDiagnostic.php:69
msgid "Checking if web server can read from <code>~/wp-content/wflogs</code>"
msgstr ""

#: lib/wfDiagnostic.php:70
msgid "Checking if web server can write to <code>~/wp-content/wflogs</code>"
msgstr ""

#: lib/wfDiagnostic.php:74
msgid "Ability to save Wordfence settings to the database."
msgstr ""

#: lib/wfDiagnostic.php:76
msgid "Checking basic config reading/writing"
msgstr ""

#: lib/wfDiagnostic.php:77
msgid "Checking serialized config reading/writing"
msgstr ""

#: lib/wfDiagnostic.php:81
msgid "Current WAF configuration."
msgstr ""

#: lib/wfDiagnostic.php:83
msgid "WAF auto prepend active"
msgstr ""

#: lib/wfDiagnostic.php:84
msgid "Configured WAF storage engine (WFWAF_STORAGE_ENGINE)"
msgstr ""

#: lib/wfDiagnostic.php:85
msgid "Active WAF storage engine"
msgstr ""

#: lib/wfDiagnostic.php:86
msgid "WAF log path"
msgstr ""

#: lib/wfDiagnostic.php:87
msgid "WAF subdirectory installation"
msgstr ""

#: lib/wfDiagnostic.php:88
msgid "WORDFENCE_WAF_PREPEND_DIRECTORY path constant"
msgstr ""

#: lib/wfDiagnostic.php:89
msgid "wordfence-waf.php path"
msgstr ""

#: lib/wfDiagnostic.php:90
msgid "WAF File Permissions"
msgstr ""

#: lib/wfDiagnostic.php:91
msgid "Recently removed wflogs files"
msgstr ""

#: lib/wfDiagnostic.php:92
msgid "WAF Loaded Successfully"
msgstr ""

#: lib/wfDiagnostic.php:93
msgid "WAF .htaccess contents"
msgstr ""

#: lib/wfDiagnostic.php:94
msgid "WAF .user.ini contents"
msgstr ""

#: lib/wfDiagnostic.php:95
msgid ".htaccess other auto prepend"
msgstr ""

#: lib/wfDiagnostic.php:96
msgid ".user.ini other auto prepend"
msgstr ""

#: lib/wfDiagnostic.php:100
msgid "Database version and privileges."
msgstr ""

#: lib/wfDiagnostic.php:102
msgid "Database Version"
msgstr ""

#: lib/wfDiagnostic.php:103
msgid "Checking if MySQL user has <code>DELETE</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:104
msgid "Checking if MySQL user has <code>INSERT</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:105
msgid "Checking if MySQL user has <code>UPDATE</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:106
msgid "Checking if MySQL user has <code>SELECT</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:107
msgid "Checking if MySQL user has <code>CREATE TABLE</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:108
msgid "Checking if MySQL user has <code>ALTER TABLE</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:109
msgid "Checking if MySQL user has <code>DROP</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:110
msgid "Checking if MySQL user has <code>TRUNCATE</code> privilege"
msgstr ""

#: lib/wfDiagnostic.php:114
msgid "PHP version, important PHP extensions."
msgstr ""

#. translators: 1. PHP version, 2. Support URL.
#: lib/wfDiagnostic.php:116
msgid "PHP version >= PHP %s<br><em> (<a href=\"https://wordpress.org/about/requirements/\" target=\"_blank\" rel=\"noopener noreferrer\">WordPress requirements</a>)</em> <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wfhelp\"><span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#. translators: 1. PHP version, 2. Support URL.
#: lib/wfDiagnostic.php:117
msgid "Process Owner"
msgstr ""

#: lib/wfDiagnostic.php:118
msgid "Checking for OpenSSL support"
msgstr ""

#: lib/wfDiagnostic.php:119
msgid "Checking OpenSSL version"
msgstr ""

#: lib/wfDiagnostic.php:120
msgid "Checking for cURL support"
msgstr ""

#: lib/wfDiagnostic.php:121
msgid "cURL Features Code"
msgstr ""

#: lib/wfDiagnostic.php:122
msgid "cURL Host"
msgstr ""

#: lib/wfDiagnostic.php:123
msgid "cURL Support Protocols"
msgstr ""

#: lib/wfDiagnostic.php:124
msgid "cURL SSL Version"
msgstr ""

#: lib/wfDiagnostic.php:125
msgid "cURL libz Version"
msgstr ""

#: lib/wfDiagnostic.php:126
msgid "Checking <code>display_errors</code><br><em> (<a href=\"http://php.net/manual/en/errorfunc.configuration.php#ini.display-errors\" target=\"_blank\" rel=\"noopener noreferrer\">Should be disabled on production servers<span class=\"screen-reader-text\"> (opens in new tab)</span></a>)</em>"
msgstr ""

#: lib/wfDiagnostic.php:130
msgid "Ability to connect to the Wordfence servers and your own site."
msgstr ""

#: lib/wfDiagnostic.php:132
msgid "Connecting to Wordfence servers (https)"
msgstr ""

#: lib/wfDiagnostic.php:133
msgid "Connecting back to this site"
msgstr ""

#: lib/wfDiagnostic.php:134
msgid "Connecting back to this site via IPv6 (not required; failure to connect may not be an issue on some sites) <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wfhelp\"><span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/wfDiagnostic.php:135
msgid "IP(s) used by this server"
msgstr ""

#: lib/wfDiagnostic.php:139
msgid "Server time accuracy and applied offsets."
msgstr ""

#: lib/wfDiagnostic.php:141
msgid "Wordfence Network Time"
msgstr ""

#: lib/wfDiagnostic.php:142
#: lib/wfDiagnostic.php:1004
msgid "Server Time"
msgstr ""

#: lib/wfDiagnostic.php:143
msgid "Wordfence Network Time Offset"
msgstr ""

#: lib/wfDiagnostic.php:144
msgid "NTP Time Offset"
msgstr ""

#: lib/wfDiagnostic.php:145
msgid "NTP Status"
msgstr ""

#: lib/wfDiagnostic.php:146
msgid "TOTP Time Source"
msgstr ""

#: lib/wfDiagnostic.php:147
msgid "WordPress Time Zone"
msgstr ""

#. translators: Number of jobs.
#: lib/wfDiagnostic.php:204
msgid "%d Job Overdue"
msgid_plural "%d Jobs Overdue"
msgstr[0] ""
msgstr[1] ""

#. translators: Number of jobs.
#: lib/wfDiagnostic.php:204
msgid "Normal"
msgstr ""

#: lib/wfDiagnostic.php:209
#: lib/wfDiagnostic.php:444
#: lib/wfDiagnostic.php:514
#: lib/wfDiagnostic.php:1054
#: lib/wfDiagnostic.php:1081
#: lib/wfUpdateCheck.php:120
#: views/dashboard/option-howgetips.php:81
#: views/scanner/issue-base.php:118
msgid "None"
msgstr ""

#: lib/wfDiagnostic.php:223
#: lib/wfDiagnostic.php:226
msgid "No files readable"
msgstr ""

#. translators: File name.
#: lib/wfDiagnostic.php:238
#: lib/wfDiagnostic.php:274
msgid "File \"%s\" does not exist"
msgstr ""

#. translators: File path.
#: lib/wfDiagnostic.php:241
msgid "File \"%s\" is unreadable"
msgstr ""

#: lib/wfDiagnostic.php:259
#: lib/wfDiagnostic.php:262
msgid "No files writable"
msgstr ""

#. translators: File name.
#: lib/wfDiagnostic.php:277
msgid "File \"%s\" is unwritable"
msgstr ""

#: lib/wfDiagnostic.php:348
msgid "Basic config writing"
msgstr ""

#: lib/wfDiagnostic.php:360
msgid "Serialized config writing"
msgstr ""

#: lib/wfDiagnostic.php:370
#: lib/wfDiagnostic.php:388
msgid "(.htaccess not present)"
msgstr ""

#: lib/wfDiagnostic.php:373
#: lib/wfDiagnostic.php:391
msgid "(.htaccess not readable)"
msgstr ""

#: lib/wfDiagnostic.php:401
#: lib/wfDiagnostic.php:437
msgid "(not present)"
msgstr ""

#: lib/wfDiagnostic.php:406
#: lib/wfDiagnostic.php:424
msgid "(.user.ini not present)"
msgstr ""

#: lib/wfDiagnostic.php:409
#: lib/wfDiagnostic.php:427
msgid "(.user.ini not readable)"
msgstr ""

#: lib/wfDiagnostic.php:440
msgid "(default)"
msgstr ""

#: lib/wfDiagnostic.php:450
msgid "Unknown (mixed plugin version)"
msgstr ""

#. translators: Unix file permissions in octal (example 0777).
#: lib/wfDiagnostic.php:491
msgid "%s - using constant"
msgstr ""

#. translators: Unix file permissions in octal (example 0777).
#: lib/wfDiagnostic.php:504
msgid "%s - using template"
msgstr ""

#: lib/wfDiagnostic.php:508
msgid "0660 - using default"
msgstr ""

#: lib/wfDiagnostic.php:551
msgid "Unavailable"
msgstr ""

#: lib/wfDiagnostic.php:689
#: lib/wfDiagnostic.php:1036
#: lib/wfDiagnostic.php:1039
#: lib/wfDiagnostic.php:1040
#: views/waf/options-group-brute-force.php:42
#: views/waf/options-group-rate-limiting.php:42
msgid "On"
msgstr ""

#: lib/wfDiagnostic.php:689
#: lib/wfDiagnostic.php:1036
#: lib/wfDiagnostic.php:1039
#: lib/wfDiagnostic.php:1040
#: views/waf/options-group-brute-force.php:41
#: views/waf/options-group-rate-limiting.php:41
msgid "Off"
msgstr ""

#: lib/wfDiagnostic.php:716
#: lib/wfDiagnostic.php:719
msgid "wp_remote_post() test to noc1.wordfence.com failed! Response was: "
msgstr ""

#: lib/wfDiagnostic.php:720
msgid "This likely means that your hosting provider is blocking requests to noc1.wordfence.com or has set up a proxy that is not behaving itself."
msgstr ""

#: lib/wfDiagnostic.php:755
#: lib/wfDiagnostic.php:756
#: lib/wfDiagnostic.php:759
#: lib/wfDiagnostic.php:760
msgid "wp_remote_post() test back to this server failed! Response was: "
msgstr ""

#: lib/wfDiagnostic.php:762
#: lib/wfDiagnostic.php:763
msgid "Cloudflare appears to be blocking your site from connecting to itself."
msgstr ""

#: lib/wfDiagnostic.php:762
#: lib/wfDiagnostic.php:763
msgid "Get help with Cloudflare compatibility"
msgstr ""

#: lib/wfDiagnostic.php:765
#: lib/wfDiagnostic.php:766
msgid "This additional info may help you diagnose the issue. The response headers we received were:"
msgstr ""

#. translators: error message from failed request
#: lib/wfDiagnostic.php:794
#: lib/wfDiagnostic.php:796
msgid "This likely indicates that the server either does not support IPv6 or does not have an IPv6 address assigned or associated with the domain. Original error message: %s"
msgstr ""

#: lib/wfDiagnostic.php:801
msgid "IPv6 DNS resolution failed"
msgstr ""

#: lib/wfDiagnostic.php:811
msgid "This diagnostic is unavailable as cURL appears to be supported, but was not used by WordPress for this request"
msgstr ""

#: lib/wfDiagnostic.php:817
msgid "This diagnostic requires cURL"
msgstr ""

#. translators: PHP super global key.
#: lib/wfDiagnostic.php:865
msgid "We cannot read $_SERVER[%s]"
msgstr ""

#: lib/wfDiagnostic.php:877
msgid "Should be: "
msgstr ""

#: lib/wfDiagnostic.php:958
msgid "Disabled "
msgstr ""

#: lib/wfDiagnostic.php:960
msgid "(WORDFENCE_LS_DISABLE_NTP)"
msgstr ""

#: lib/wfDiagnostic.php:963
msgid "(failures exceeded limit)"
msgstr ""

#: lib/wfDiagnostic.php:966
msgid "(settings)"
msgstr ""

#: lib/wfDiagnostic.php:973
msgid " (%d of %d attempts remaining)"
msgstr ""

#: lib/wfDiagnostic.php:990
#: modules/login-security/views/options/option-ntp.php:8
msgid "NTP"
msgstr ""

#: lib/wfDiagnostic.php:1034
msgid "Return value of is_multisite()"
msgstr ""

#: lib/wfDiagnostic.php:1035
msgid "WordPress base path"
msgstr ""

#: lib/wfDiagnostic.php:1036
msgid "WordPress debug mode"
msgstr ""

#: lib/wfDiagnostic.php:1037
msgid "WordPress error logging override"
msgstr ""

#: lib/wfDiagnostic.php:1038
msgid "WordPress error display override"
msgstr ""

#: lib/wfDiagnostic.php:1039
msgid "WordPress script debug mode"
msgstr ""

#: lib/wfDiagnostic.php:1040
msgid "WordPress query debug mode"
msgstr ""

#: lib/wfDiagnostic.php:1041
msgid "Database character set"
msgstr ""

#: lib/wfDiagnostic.php:1042
msgid "Database collation"
msgstr ""

#: lib/wfDiagnostic.php:1043
msgid "Explicitly set site URL"
msgstr ""

#: lib/wfDiagnostic.php:1044
msgid "Explicitly set blog URL"
msgstr ""

#: lib/wfDiagnostic.php:1045
msgid "\"wp-content\" folder is in default location"
msgstr ""

#. translators: WordPress content directory.
#. translators: WordPress plugins directory.
#. translators: WordPress languages directory.
#: lib/wfDiagnostic.php:1045
#: lib/wfDiagnostic.php:1047
#: lib/wfDiagnostic.php:1048
msgid "No: %s"
msgstr ""

#. translators: WordPress content directory.
#: lib/wfDiagnostic.php:1046
msgid "URL to the \"wp-content\" folder"
msgstr ""

#: lib/wfDiagnostic.php:1047
msgid "\"plugins\" folder is in default location"
msgstr ""

#. translators: WordPress plugins directory.
#: lib/wfDiagnostic.php:1048
msgid "\"languages\" folder is in default location"
msgstr ""

#. translators: WordPress languages directory.
#: lib/wfDiagnostic.php:1049
msgid "Language choice"
msgstr ""

#: lib/wfDiagnostic.php:1050
msgid "Custom upload folder location"
msgstr ""

#: lib/wfDiagnostic.php:1051
msgid "Theme template folder override"
msgstr ""

#. translators: WordPress theme template directory.
#. translators: WordPress theme stylesheet directory.
#: lib/wfDiagnostic.php:1051
#: lib/wfDiagnostic.php:1052
msgid "Overridden: %s"
msgstr ""

#. translators: WordPress theme template directory.
#: lib/wfDiagnostic.php:1052
msgid "Theme stylesheet folder override"
msgstr ""

#. translators: WordPress theme stylesheet directory.
#: lib/wfDiagnostic.php:1053
msgid "Post editing automatic saving interval"
msgstr ""

#: lib/wfDiagnostic.php:1054
msgid "Post revisions saved by WordPress"
msgstr ""

#: lib/wfDiagnostic.php:1054
#: lib/wfDiagnostic.php:1100
#: views/waf/options-group-rate-limiting.php:67
msgid "Unlimited"
msgstr ""

#: lib/wfDiagnostic.php:1055
msgid "WordPress cookie domain"
msgstr ""

#: lib/wfDiagnostic.php:1056
msgid "WordPress cookie path"
msgstr ""

#: lib/wfDiagnostic.php:1057
msgid "WordPress site cookie path"
msgstr ""

#: lib/wfDiagnostic.php:1058
msgid "WordPress admin cookie path"
msgstr ""

#: lib/wfDiagnostic.php:1059
msgid "WordPress plugins cookie path"
msgstr ""

#: lib/wfDiagnostic.php:1060
msgid "URL redirected to if the visitor tries to access a nonexistent blog"
msgstr ""

#: lib/wfDiagnostic.php:1061
msgid "Concatenate JavaScript files"
msgstr ""

#: lib/wfDiagnostic.php:1062
msgid "WordPress memory limit"
msgstr ""

#: lib/wfDiagnostic.php:1063
msgid "Administrative memory limit"
msgstr ""

#: lib/wfDiagnostic.php:1064
msgid "Built-in caching"
msgstr ""

#: lib/wfDiagnostic.php:1065
msgid "Custom \"users\" table"
msgstr ""

#. translators: WordPress custom user table.
#. translators: WordPress custom user meta table.
#: lib/wfDiagnostic.php:1065
#: lib/wfDiagnostic.php:1066
msgid "Set: %s"
msgstr ""

#. translators: WordPress custom user table.
#: lib/wfDiagnostic.php:1066
msgid "Custom \"usermeta\" table"
msgstr ""

#. translators: WordPress custom user meta table.
#: lib/wfDiagnostic.php:1067
msgid "Overridden permissions for a new folder"
msgstr ""

#: lib/wfDiagnostic.php:1068
msgid "Overridden permissions for a new file"
msgstr ""

#: lib/wfDiagnostic.php:1069
msgid "Alternate WP cron"
msgstr ""

#: lib/wfDiagnostic.php:1070
msgid "WP cron status"
msgstr ""

#: lib/wfDiagnostic.php:1070
msgid "Cron is disabled"
msgstr ""

#: lib/wfDiagnostic.php:1070
msgid "Cron is enabled"
msgstr ""

#: lib/wfDiagnostic.php:1071
msgid "Cron running frequency lock"
msgstr ""

#: lib/wfDiagnostic.php:1072
msgid "Interval the trash is automatically emptied at in days"
msgstr ""

#: lib/wfDiagnostic.php:1072
#: lib/wordfenceClass.php:4319
msgid "Never"
msgstr ""

#: lib/wfDiagnostic.php:1073
msgid "Automatic database repair"
msgstr ""

#: lib/wfDiagnostic.php:1074
msgid "Do not upgrade global tables"
msgstr ""

#: lib/wfDiagnostic.php:1075
msgid "Disallow plugin/theme editing"
msgstr ""

#: lib/wfDiagnostic.php:1076
msgid "Disallow plugin/theme update and installation"
msgstr ""

#: lib/wfDiagnostic.php:1077
msgid "Overwrite image edits when restoring the original"
msgstr ""

#: lib/wfDiagnostic.php:1078
msgid "Force SSL for administrative logins"
msgstr ""

#: lib/wfDiagnostic.php:1079
msgid "Block external URL requests"
msgstr ""

#: lib/wfDiagnostic.php:1080
msgid "Allowlisted hosts"
msgstr ""

#: lib/wfDiagnostic.php:1081
msgid "Automatic WP Core updates"
msgstr ""

#: lib/wfDiagnostic.php:1081
msgid "Everything"
msgstr ""

#: lib/wfDiagnostic.php:1081
msgid "Default"
msgstr ""

#: lib/wfDiagnostic.php:1082
msgid "Hostname for a proxy server"
msgstr ""

#: lib/wfDiagnostic.php:1083
msgid "Port for a proxy server"
msgstr ""

#: lib/wfDiagnostic.php:1084
msgid "Multisite enabled"
msgstr ""

#: lib/wfDiagnostic.php:1085
msgid "Multisite/network ability enabled"
msgstr ""

#: lib/wfDiagnostic.php:1086
msgid "Multisite enabled, WordPress will load the /wp-content/sunrise.php file"
msgstr ""

#: lib/wfDiagnostic.php:1087
msgid "Multisite enabled, subdomain installation constant"
msgstr ""

#: lib/wfDiagnostic.php:1088
msgid "Multisite enabled, Older subdomain installation constant"
msgstr ""

#: lib/wfDiagnostic.php:1089
msgid "Defines the multisite domain for the current site"
msgstr ""

#: lib/wfDiagnostic.php:1090
msgid "Defines the multisite path for the current site"
msgstr ""

#: lib/wfDiagnostic.php:1091
msgid "Defines the multisite database ID for the current site"
msgstr ""

#: lib/wfDiagnostic.php:1092
msgid "Disable the fatal error handler"
msgstr ""

#: lib/wfDiagnostic.php:1093
msgid "Disables automatic updates"
msgstr ""

#: lib/wfDiagnostic.php:1093
msgid "Automatic updates disabled"
msgstr ""

#: lib/wfDiagnostic.php:1093
msgid "Automatic updates enabled"
msgstr ""

#. translators: Number of HTTP requests.
#: lib/wfDiagnostic.php:1101
#: lib/wfDiagnostic.php:1102
#: lib/wfDiagnostic.php:1103
#: lib/wfDiagnostic.php:1104
#: lib/wfDiagnostic.php:1105
#: lib/wfDiagnostic.php:1106
#: lib/wfDiagnostic.php:1107
#: lib/wfDiagnostic.php:1108
#: lib/wfDiagnostic.php:1109
#: lib/wfDiagnostic.php:1110
#: lib/wfDiagnostic.php:1111
#: lib/wfDiagnostic.php:1112
#: lib/wfDiagnostic.php:1113
#: lib/wfDiagnostic.php:1114
#: views/waf/options-group-rate-limiting.php:68
#: views/waf/options-group-rate-limiting.php:69
#: views/waf/options-group-rate-limiting.php:70
#: views/waf/options-group-rate-limiting.php:71
#: views/waf/options-group-rate-limiting.php:72
#: views/waf/options-group-rate-limiting.php:73
#: views/waf/options-group-rate-limiting.php:74
#: views/waf/options-group-rate-limiting.php:75
#: views/waf/options-group-rate-limiting.php:76
#: views/waf/options-group-rate-limiting.php:77
#: views/waf/options-group-rate-limiting.php:78
#: views/waf/options-group-rate-limiting.php:79
#: views/waf/options-group-rate-limiting.php:80
#: views/waf/options-group-rate-limiting.php:81
msgid "%d per minute"
msgstr ""

#: lib/wfDiagnostic.php:1117
#: views/waf/options-group-rate-limiting.php:84
msgid "throttle it"
msgstr ""

#: lib/wfDiagnostic.php:1118
#: views/waf/options-group-rate-limiting.php:85
msgid "block it"
msgstr ""

#: lib/wfDiagnostic.php:1131
msgid "Scanner"
msgstr ""

#: lib/wfDiagnostic.php:1134
#: views/scanner/options-group-general.php:33
msgid "Check if this website is being \"Spamvertised\""
msgstr ""

#: lib/wfDiagnostic.php:1160
#: views/scanner/options-group-performance.php:36
msgid "Maximum execution time for each scan stage "
msgstr ""

#: lib/wfDiagnostic.php:1161
#: views/scanner/options-group-advanced.php:35
msgid "Exclude files from scan that match these wildcard patterns (one per line)"
msgstr ""

#: lib/wfDiagnostic.php:1161
#: lib/wfDiagnostic.php:1162
#: lib/wfDiagnostic.php:1180
#: lib/wfDiagnostic.php:1188
#: lib/wfDiagnostic.php:1217
#: lib/wfDiagnostic.php:1219
#: lib/wfDiagnostic.php:1220
#: lib/wfDiagnostic.php:1228
msgid "(empty)"
msgstr ""

#: lib/wfDiagnostic.php:1162
#: views/scanner/options-group-advanced.php:46
msgid "Additional scan signatures (one per line)"
msgstr ""

#: lib/wfDiagnostic.php:1171
msgid "Enable Wordfence translations"
msgstr ""

#: lib/wfDiagnostic.php:1181
#: views/waf/options-group-brute-force.php:153
msgid "For admins only"
msgstr ""

#: lib/wfDiagnostic.php:1181
#: views/waf/options-group-brute-force.php:153
msgid "For all users with \"publish posts\" capability"
msgstr ""

#: lib/wfDiagnostic.php:1181
#: lib/wfDiagnostic.php:1182
#: lib/wfDiagnostic.php:1195
#: lib/wfDiagnostic.php:1197
#: lib/wfDiagnostic.php:1198
#: lib/wfDiagnostic.php:1200
#: lib/wfDiagnostic.php:1201
#: lib/wfDiagnostic.php:1203
#: lib/wfDiagnostic.php:1204
#: lib/wfDiagnostic.php:1206
#: lib/wfDiagnostic.php:1207
#: lib/wfDiagnostic.php:1209
#: lib/wfDiagnostic.php:1210
#: lib/wfDiagnostic.php:1216
msgid "(unknown)"
msgstr ""

#: lib/wfDiagnostic.php:1182
#: views/waf/options-group-brute-force.php:176
msgid "Force admins and publishers to use strong passwords (recommended)"
msgstr ""

#: lib/wfDiagnostic.php:1182
#: views/waf/options-group-brute-force.php:176
msgid "Force all members to use strong passwords"
msgstr ""

#: lib/wfDiagnostic.php:1184
#: views/waf/options-group-brute-force.php:202
msgid "Prevent users registering 'admin' username if it doesn't exist"
msgstr ""

#: lib/wfDiagnostic.php:1185
#: views/waf/options-group-brute-force.php:214
msgid "Prevent discovery of usernames through '/?author=N' scans, the oEmbed API, the WordPress REST API, and WordPress XML Sitemaps"
msgstr ""

#: lib/wfDiagnostic.php:1195
#: views/waf/options-group-rate-limiting.php:55
msgid "Verified Google crawlers will not be rate-limited"
msgstr ""

#: lib/wfDiagnostic.php:1195
#: views/waf/options-group-rate-limiting.php:56
msgid "Anyone claiming to be Google will not be rate-limited"
msgstr ""

#: lib/wfDiagnostic.php:1195
#: views/waf/options-group-rate-limiting.php:57
msgid "Treat Google like any other Crawler"
msgstr ""

#: lib/wfDiagnostic.php:1214
#: views/blocking/blocking-create.php:66
msgid "Country Blocking"
msgstr ""

#: lib/wfDiagnostic.php:1215
#: views/blocking/options-group-advanced-country.php:39
msgid "What to do when we block someone"
msgstr ""

#: lib/wfDiagnostic.php:1216
msgid "Show the standard Wordfence blocked message"
msgstr ""

#: lib/wfDiagnostic.php:1216
msgid "Redirect to the URL below"
msgstr ""

#: lib/wfDiagnostic.php:1217
#: views/blocking/options-group-advanced-country.php:49
msgid "URL to redirect blocked users to"
msgstr ""

#: lib/wfDiagnostic.php:1219
#: views/blocking/option-bypass-redirect.php:8
msgid "Bypass Redirect"
msgstr ""

#: lib/wfDiagnostic.php:1220
#: views/blocking/option-bypass-cookie.php:8
msgid "Bypass Cookie"
msgstr ""

#: lib/wfDiagnostic.php:1226
msgid "Connected"
msgstr ""

#: lib/wfDiagnostic.php:1226
#: lib/wfDiagnostic.php:1229
#: lib/wfDiagnostic.php:1231
#: lib/wfDiagnostic.php:1232
msgid "true"
msgstr ""

#: lib/wfDiagnostic.php:1226
#: lib/wfDiagnostic.php:1229
#: lib/wfDiagnostic.php:1231
#: lib/wfDiagnostic.php:1232
msgid "false"
msgstr ""

#: lib/wfDiagnostic.php:1227
msgid "Connect Timestamp"
msgstr ""

#: lib/wfDiagnostic.php:1228
msgid "Site ID"
msgstr ""

#: lib/wfDiagnostic.php:1229
msgid "Disconnected"
msgstr ""

#: lib/wfDiagnostic.php:1230
msgid "Disconnect Timestamp"
msgstr ""

#: lib/wfDiagnostic.php:1231
msgid "Configuration Issue"
msgstr ""

#: lib/wfDiagnostic.php:1232
msgid "Plugin Alerting Disabled"
msgstr ""

#: lib/wfImportExportController.php:43
#: lib/wfImportExportController.php:50
msgid "An error occurred: "
msgstr ""

#: lib/wfImportExportController.php:46
msgid "Invalid response: "
msgstr ""

#: lib/wfImportExportController.php:62
msgid "An error occurred: Invalid options format received."
msgstr ""

#. translators: Error message.
#: lib/wfImportExportController.php:107
#: lib/wfImportExportController.php:114
msgid "An error occurred: %s"
msgstr ""

#. translators: Error message.
#: lib/wfImportExportController.php:110
msgid "Invalid response: %s"
msgstr ""

#: lib/wfLicense.php:238
msgid "Wordfence Care"
msgstr ""

#: lib/wfLicense.php:238
msgid "Care"
msgstr ""

#: lib/wfLicense.php:240
msgid "Wordfence Response"
msgstr ""

#: lib/wfLicense.php:243
msgid "Wordfence Premium"
msgstr ""

#: lib/wfLicense.php:246
msgid "Wordfence Free"
msgstr ""

#: lib/wfLicense.php:246
msgid "Free"
msgstr ""

#: lib/wfLockedOut.php:10
msgid "You are temporarily locked out"
msgstr ""

#: lib/wfLockedOut.php:356
msgid "Your access to this site has been temporarily limited by the site owner"
msgstr ""

#: lib/wfLockedOut.php:357
msgid "Your access to this service has been temporarily limited. Please try again in a few minutes. (HTTP response code 503)"
msgstr ""

#: lib/wfLockedOut.php:365
msgid "Return to the site home page"
msgstr ""

#: lib/wfLockedOut.php:366
msgid "Attempt to return to the admin login page (you may still be locked out)"
msgstr ""

#: lib/wfLockedOut.php:373
msgid "Block Reason:"
msgstr ""

#: lib/wfLockedOut.php:374
msgid "You have been temporarily locked out of this system. This means that you will not be able to log in for a while."
msgstr ""

#: lib/wfLockedOut.php:399
msgid "Click here to learn more: <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Documentation<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/wfLog.php:216
msgid "Exceeded the maximum global requests per minute for crawlers or humans."
msgstr ""

#: lib/wfLog.php:219
msgid "Exceeded the maximum number of requests per minute for crawlers."
msgstr ""

#: lib/wfLog.php:222
msgid "Exceeded the maximum number of page not found errors per minute for a crawler."
msgstr ""

#: lib/wfLog.php:225
msgid "Exceeded the maximum number of page requests per minute for humans."
msgstr ""

#: lib/wfLog.php:228
msgid "Exceeded the maximum number of page not found errors per minute for humans."
msgstr ""

#. translators: Error message.
#: lib/wfLog.php:309
msgid "Invalid log type to wfLog: %s"
msgstr ""

#. translators: Error message.
#: lib/wfLog.php:343
msgid "getHits got invalid hitType: %s"
msgstr ""

#: lib/wfLog.php:353
msgid "UA/Hostname/Referrer/IP Range not allowed"
msgstr ""

#: lib/wfLog.php:566
msgid "UA/Referrer/IP Range not allowed"
msgstr ""

#: lib/wfLog.php:567
msgid "Advanced blocking in effect."
msgstr ""

#: lib/wfLog.php:579
msgid "redirected to bypass URL"
msgstr ""

#. translators: URL
#: lib/wfLog.php:593
msgid "blocked access via country blocking and redirected to URL (%s)"
msgstr ""

#: lib/wfLog.php:608
#: models/block/wfBlock.php:1603
msgid "blocked access via country blocking"
msgstr ""

#: lib/wfLog.php:611
#: models/block/wfBlock.php:1606
#: waf/wfWAFIPBlocksController.php:76
msgid "Access from your area has been temporarily limited for security reasons"
msgstr ""

#: lib/wfLog.php:626
#: lib/wordfenceClass.php:6366
#: waf/wfWAFIPBlocksController.php:102
msgid "Manual block by administrator"
msgstr ""

#. translators: 1. IP address. 2. Description of firewall action.
#: lib/wfLog.php:654
msgid "Blocking IP %1$s. %2$s"
msgstr ""

#. translators: 1. IP address. 2. Description of firewall action.
#: lib/wfLog.php:666
msgid "Throttling IP %1$s. %2$s"
msgstr ""

#. translators: Error message.
#: lib/wfScan.php:42
msgid "Could not connect to database to start scan: %s"
msgstr ""

#: lib/wfScan.php:45
msgid "Looks like the Wordfence database tables have been deleted. You can fix this by de-activating and re-activating the Wordfence plugin from your Plugins menu."
msgstr ""

#: lib/wfScan.php:49
msgid "Cron test received and message printed"
msgstr ""

#: lib/wfScan.php:53
msgid "Scan engine received request."
msgstr ""

#: lib/wfScan.php:56
msgid "Verifying start request signature."
msgstr ""

#: lib/wfScan.php:58
msgid "The signature on the request to start a scan is invalid. Please try again."
msgstr ""

#: lib/wfScan.php:62
msgid "Fetching stored cronkey for comparison."
msgstr ""

#: lib/wfScan.php:65
msgid "[invalid]"
msgstr ""

#: lib/wfScan.php:65
#: lib/wfScan.php:66
msgid "[none]"
msgstr ""

#. translators: 1. WordPress nonce. 2. WordPress nonce.
#: lib/wfScan.php:67
msgid "Checking cronkey: %1$s (expecting %2$s)"
msgstr ""

#: lib/wfScan.php:69
msgid "Wordfence scan script accessed directly, or WF did not receive a cronkey."
msgstr ""

#. translators: 1. Unix timestamp. 2. WordPress nonce. 3. Unix timestamp.
#: lib/wfScan.php:77
msgid "The key used to start a scan expired. The value is: %1$s and split is: %2$s and time is: %3$d"
msgstr ""

#: lib/wfScan.php:81
msgid "Wordfence could not find a saved cron key to start the scan so assuming it started and exiting."
msgstr ""

#: lib/wfScan.php:85
msgid "Checking saved cronkey against cronkey param"
msgstr ""

#. translators: 1. WordPress nonce (used for debugging). 2. WordPress nonce (used for debugging). 3. WordPress nonce (used for debugging).
#: lib/wfScan.php:90
msgid "Wordfence could not start a scan because the cron key does not match the saved key. Saved: %1$s Sent: %2$s Current unexploded: %3$s"
msgstr ""

#: lib/wfScan.php:114
msgid "Checking if scan is already running"
msgstr ""

#: lib/wfScan.php:116
msgid "There is already a scan running."
msgstr ""

#: lib/wfScan.php:125
msgid "Using low resource scanning"
msgstr ""

#: lib/wfScan.php:128
msgid "Requesting max memory"
msgstr ""

#: lib/wfScan.php:130
msgid "Setting up error handling environment"
msgstr ""

#: lib/wfScan.php:138
msgid "Setting up scanRunning and starting scan"
msgstr ""

#. translators: Error message (used for debugging).
#: lib/wfScan.php:143
msgid "Got a true deserialized value back from 'wfsd_engine' with type: %s"
msgstr ""

#. translators: Error message (used for debugging).
#: lib/wfScan.php:147
msgid "Scan can't continue - stored data not found after a fork. Got type: %s"
msgstr ""

#: lib/wfScan.php:149
#: lib/wfScan.php:152
msgid "Scan can't continue - stored data not found after a fork."
msgstr ""

#. translators: Error message.
#: lib/wfScan.php:153
#: lib/wfScan.php:319
msgid "Previous scan terminated with an error. See below."
msgstr ""

#: lib/wfScan.php:176
msgid "Contacting Wordfence to initiate scan"
msgstr ""

#. translators: Time until.
#: lib/wfScan.php:185
msgid "Deferring scheduled scan by %s"
msgstr ""

#: lib/wfScan.php:206
msgid "Initiating quick scan"
msgstr ""

#. translators: 1. Bytes of memory. 2. Bytes of memory.
#. translators: 1. Memory in bytes. 2. Memory in bytes.
#: lib/wfScan.php:217
#: lib/wfScan.php:229
#: lib/wfScan.php:252
#: lib/wfScan.php:267
#: lib/wfScan.php:282
#: lib/wfScan.php:297
#: lib/wfScanEngine.php:476
msgid "Wordfence used %1$s of memory for scan. Server peak memory usage was: %2$s"
msgstr ""

#. translators: Error message.
#: lib/wfScan.php:221
#: lib/wfScan.php:233
#: lib/wfScan.php:256
#: lib/wfScan.php:271
#: lib/wfScan.php:286
#: lib/wfScan.php:301
#: lib/wfScan.php:318
msgid "Scan terminated with error: %s"
msgstr ""

#: lib/wfScan.php:304
msgid "Wordfence scan failed because of license site URL conflict"
msgstr ""

#. translators: Error message.
#: lib/wfScan.php:356
msgid "Scan Engine Error: %s"
msgstr ""

#. translators: Number of scan results.
#: lib/wfScanEngine.php:139
msgid "%d issue found in most recent scan"
msgid_plural "%d issues found in most recent scan"
msgstr[0] ""
msgstr[1] ""

#. translators: 1. Time duration. 2. Support URL.
#: lib/wfScanEngine.php:310
msgid "The scan time limit of %1$s has been exceeded and the scan will be terminated. This limit can be customized on the options page. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Get More Information<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wfScanEngine.php:314
msgid "Scan Time Limit Exceeded"
msgstr ""

#. translators: 1. Number of files. 2. Number of plugins. 3. Number of themes. 4. Number of posts. 5. Number of comments. 6. Number of URLs. 7. Time duration.
#: lib/wfScanEngine.php:319
msgid "Scan interrupted. Scanned %1$d files, %2$d plugins, %3$d themes, %4$d posts, %5$d comments and %6$d URLs in %7$s."
msgstr ""

#. translators: Number of scan results.
#: lib/wfScanEngine.php:331
msgid "Scan interrupted. You have %d new issue to fix. See below."
msgid_plural "Scan interrupted. You have %d new issues to fix. See below."
msgstr[0] ""
msgstr[1] ""

#: lib/wfScanEngine.php:340
msgid "Scan interrupted. No problems found prior to stopping."
msgstr ""

#. translators: 1. Software version. 2. Software version.
#: lib/wfScanEngine.php:352
msgid "Aborting scan because WordPress updated from version %1$s to %2$s. The scan will be reattempted later."
msgstr ""

#: lib/wfScanEngine.php:385
#: lib/wordfenceScanner.php:481
msgid "Forking during malware scan to ensure continuity."
msgstr ""

#: lib/wfScanEngine.php:391
msgid "Entered fork()"
msgstr ""

#: lib/wfScanEngine.php:394
msgid "Calling startScan(true)"
msgstr ""

#. translators: 1. Number of files. 2. Number of plugins. 3. Number of themes. 4. Number of posts. 5. Number of comments. 6. Number of URLs. 7. Time duration.
#: lib/wfScanEngine.php:486
msgid "Scan Complete. Scanned %1$d files, %2$d plugins, %3$d themes, %4$d posts, %5$d comments and %6$d URLs in %7$s."
msgstr ""

#. translators: 1. Time duration.
#: lib/wfScanEngine.php:498
msgid "Quick Scan Complete. Scanned in %s."
msgstr ""

#. translators: Number of scan results.
#: lib/wfScanEngine.php:507
msgid "%d ignored issue was also detected."
msgid_plural "%d ignored issues were also detected."
msgstr[0] ""
msgstr[1] ""

#. translators: Number of scan results.
#: lib/wfScanEngine.php:518
msgid "Scan complete. You have %d new issue to fix."
msgid_plural "Scan complete. You have %d new issues to fix."
msgstr[0] ""
msgstr[1] ""

#: lib/wfScanEngine.php:526
msgid "See below."
msgstr ""

#: lib/wfScanEngine.php:529
msgid "Scan complete. Congratulations, no new problems found."
msgstr ""

#: lib/wfScanEngine.php:540
msgid "Checking if your site IP is generating spam"
msgstr ""

#: lib/wfScanEngine.php:559
msgid "Checking if your IP is generating spam is for paid members only"
msgstr ""

#: lib/wfScanEngine.php:566
msgid "Checking if your site is on a domain blocklist"
msgstr ""

#: lib/wfScanEngine.php:571
msgid "Checking if your site is on a domain blocklist is for paid members only"
msgstr ""

#. translators: Error message.
#: lib/wfScanEngine.php:607
msgid "Error checking domain blocklists: %s"
msgstr ""

#. translators: WordPress site ID.
#: lib/wfScanEngine.php:641
msgid "The multisite blog with ID %d is listed on Google's Safe Browsing malware list."
msgstr ""

#: lib/wfScanEngine.php:644
msgid "Your site is listed on Google's Safe Browsing malware list."
msgstr ""

#. translators: 1. URL. 2. URL.
#: lib/wfScanEngine.php:648
msgid "The URL %1$s is on the malware list. More info available at <a href=\"http://safebrowsing.clients.google.com/safebrowsing/diagnostic?site=%2$s&client=googlechrome&hl=en-US\" target=\"_blank\" rel=\"noopener noreferrer\">Google Safe Browsing diagnostic page<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: WordPress site ID.
#: lib/wfScanEngine.php:654
msgid "The multisite blog with ID %d is listed on Google's Safe Browsing phishing list."
msgstr ""

#: lib/wfScanEngine.php:657
msgid "Your site is listed on Google's Safe Browsing phishing list."
msgstr ""

#. translators: 1. URL. 2. URL.
#: lib/wfScanEngine.php:661
msgid "The URL %1$s is on the phishing list. More info available at <a href=\"http://safebrowsing.clients.google.com/safebrowsing/diagnostic?site=%2$s&client=googlechrome&hl=en-US\" target=\"_blank\" rel=\"noopener noreferrer\">Google Safe Browsing diagnostic page<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: WordPress site ID.
#: lib/wfScanEngine.php:667
msgid "The multisite blog with ID %d is listed on the Wordfence domain blocklist."
msgstr ""

#: lib/wfScanEngine.php:670
msgid "Your site is listed on the Wordfence domain blocklist."
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:674
msgid "The URL %s is on the blocklist."
msgstr ""

#. translators: WordPress site ID.
#: lib/wfScanEngine.php:680
msgid "The multisite blog with ID %d is listed on a domain blocklist."
msgstr ""

#: lib/wfScanEngine.php:683
msgid "Your site is listed on a domain blocklist."
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:685
msgid "The URL is: %s"
msgstr ""

#: lib/wfScanEngine.php:705
msgid "Checking for the most secure way to get IPs"
msgstr ""

#: lib/wfScanEngine.php:732
msgid "Unable to accurately detect IPs"
msgstr ""

#. translators: Support URL.
#: lib/wfScanEngine.php:733
msgid "Wordfence was unable to validate a test request to your website. This can happen if your website is behind a proxy that does not use one of the standard ways to convey the IP of the request or it is unreachable publicly. IP blocking and live traffic information may not be accurate. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Get More Information<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wfScanEngine.php:743
#: lib/wordfenceClass.php:6562
msgid "For maximum security use PHP's built in REMOTE_ADDR."
msgstr ""

#: lib/wfScanEngine.php:745
#: lib/wordfenceClass.php:6565
msgid "This site appears to be behind a front-end proxy, so using the X-Forwarded-For HTTP header will resolve to the correct IPs."
msgstr ""

#: lib/wfScanEngine.php:747
#: lib/wordfenceClass.php:6568
msgid "This site appears to be behind a front-end proxy, so using the X-Real-IP HTTP header will resolve to the correct IPs."
msgstr ""

#: lib/wfScanEngine.php:749
#: lib/wordfenceClass.php:6571
msgid "This site appears to be behind Cloudflare, so using the Cloudflare \"CF-Connecting-IP\" HTTP header will resolve to the correct IPs."
msgstr ""

#: lib/wfScanEngine.php:753
msgid "'How does Wordfence get IPs' is misconfigured"
msgstr ""

#. translators: Support URL.
#: lib/wfScanEngine.php:756
msgid "A test request to this website was detected on a different value for this setting. IP blocking and live traffic information may not be accurate. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Get More Information<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wfScanEngine.php:777
msgid "Check for publicly accessible configuration files, backup files and logs"
msgstr ""

#. translators: File path.
#: lib/wfScanEngine.php:829
msgid "Publicly accessible config, backup, or log file found: %s"
msgstr ""

#. translators: 1. URL to publicly accessible file. 2. Support URL.
#: lib/wfScanEngine.php:832
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">%1$s</a> is publicly accessible and may expose source code or sensitive information about your site. Files such as this one are commonly checked for by scanners and should be made inaccessible. Alternately, some can be removed if you are certain your site does not need them. Sites using the nginx web server may need manual configuration changes to protect such files. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn more<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wfScanEngine.php:862
msgid "Checking if your server discloses the path to the document root"
msgstr ""

#: lib/wfScanEngine.php:872
msgid "Web server exposes the document root"
msgstr ""

#: lib/wfScanEngine.php:873
msgid "Full Path Disclosure (FPD) vulnerabilities enable the attacker to see the path to the webroot/file. e.g.: /home/<USER>/htdocs/file/. Certain vulnerabilities, such as using the load_file() (within a SQL Injection) query to view the page source, require the attacker to have the full path to the file they wish to view."
msgstr ""

#: lib/wfScanEngine.php:899
msgid "Directory listing is enabled"
msgstr ""

#: lib/wfScanEngine.php:900
msgid "Directory listing provides an attacker with the complete index of all the resources located inside of the directory. The specific risks and consequences vary depending on which files are listed and accessible, but it is recommended that you disable it unless it is needed."
msgstr ""

#: lib/wfScanEngine.php:916
msgid "Checking if your site is being Spamvertised"
msgstr ""

#: lib/wfScanEngine.php:935
msgid "Check if your site is being Spamvertized is for paid members only"
msgstr ""

#: lib/wfScanEngine.php:965
msgid "Ignoring invalid scan path: %s"
msgstr ""

#: lib/wfScanEngine.php:999
msgid "Ignoring invalid expected scan file: %s"
msgstr ""

#: lib/wfScanEngine.php:1004
msgid "Wordfence could not read the content of your WordPress directory. This usually indicates your permissions are so strict that your web server can't read your WordPress directory."
msgstr ""

#: lib/wfScanEngine.php:1013
msgid "Ignoring invalid base scan file: %s"
msgstr ""

#: lib/wfScanEngine.php:1024
msgid "Checking for paths skipped due to scan settings"
msgstr ""

#. translators: Number of paths skipped in scan.
#: lib/wfScanEngine.php:1034
msgid ", and %d more."
msgstr ""

#. translators: Number of paths skipped in scan.
#: lib/wfScanEngine.php:1058
msgid "%d path was skipped for the malware scan due to scan settings"
msgid_plural "%d paths were skipped for the malware scan due to scan settings"
msgstr[0] ""
msgstr[1] ""

#. translators: 1. Number of paths skipped in scan. 2. Support URL. 3. List of skipped paths.
#: lib/wfScanEngine.php:1061
msgid "The option \"Scan files outside your WordPress installation\" is off by default, which means %1$d path and its file(s) will not be scanned for malware or unauthorized changes. To continue skipping this path, you may ignore this issue. Or to start scanning it, enable the option and subsequent scans will include it. Some paths may not be necessary to scan, so this is optional. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgid_plural "The option \"Scan files outside your WordPress installation\" is off by default, which means %1$d paths and their file(s) will not be scanned for malware or unauthorized changes. To continue skipping these paths, you may ignore this issue. Or to start scanning them, enable the option and subsequent scans will include them. Some paths may not be necessary to scan, so this is optional. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgstr[0] ""
msgstr[1] ""

#: lib/wfScanEngine.php:1089
msgid "Including files that are outside the WordPress installation in the scan."
msgstr ""

#: lib/wfScanEngine.php:1092
msgid "Getting plugin list from WordPress"
msgstr ""

#. translators: Number of plugins.
#: lib/wfScanEngine.php:1094
msgid "Found %d plugin"
msgid_plural "Found %d plugins"
msgstr[0] ""
msgstr[1] ""

#: lib/wfScanEngine.php:1096
msgid "Getting theme list from WordPress"
msgstr ""

#. translators: Number of themes.
#: lib/wfScanEngine.php:1098
msgid "Found %d theme"
msgid_plural "Found %d themes"
msgstr[0] ""
msgstr[1] ""

#: lib/wfScanEngine.php:1115
msgid "Scanning file contents for infections and vulnerabilities"
msgstr ""

#: lib/wfScanEngine.php:1118
msgid "Skipping scan of file contents for infections and vulnerabilities"
msgstr ""

#: lib/wfScanEngine.php:1122
msgid "Scanning file contents for URLs on a domain blocklist"
msgstr ""

#: lib/wfScanEngine.php:1125
msgid "Skipping scan of file contents for URLs on a domain blocklist"
msgstr ""

#: lib/wfScanEngine.php:1130
msgid "Starting scan of file contents"
msgstr ""

#: lib/wfScanEngine.php:1146
msgid "Done file contents scan"
msgstr ""

#. translators: Scan result description.
#: lib/wfScanEngine.php:1154
#: lib/wfScanEngine.php:2442
#: lib/wfScanEngine.php:2484
msgid "Adding issue: %s"
msgstr ""

#: lib/wfScanEngine.php:1187
msgid "Scanning for publicly accessible quarantined files"
msgstr ""

#. translators: File path.
#: lib/wfScanEngine.php:1192
msgid "Testing accessibility of: %s"
msgstr ""

#. translators: File path.
#: lib/wfScanEngine.php:1201
msgid "Publicly accessible quarantined file found: %s"
msgstr ""

#. translators: URL to publicly accessible file.
#: lib/wfScanEngine.php:1204
msgid "<a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">%1$s<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wfScanEngine.php:1228
msgid "Scanning posts for URLs on a domain blocklist"
msgstr ""

#. translators: Number of posts left to scan.
#: lib/wfScanEngine.php:1251
msgid "Scanning posts with %d left to scan."
msgstr ""

#: lib/wfScanEngine.php:1273
msgid "Post title contains suspicious code"
msgstr ""

#: lib/wfScanEngine.php:1274
msgid "This post contains code that is suspicious. Please check the title of the post and confirm that the code in the title is not malicious."
msgstr ""

#: lib/wfScanEngine.php:1297
msgid "Examining URLs found in posts we scanned for dangerous websites"
msgstr ""

#: lib/wfScanEngine.php:1299
#: lib/wfScanEngine.php:2405
msgid "Done examining URLs"
msgstr ""

#. translators: 1. WordPress Post type. 2. URL.
#: lib/wfScanEngine.php:1335
#: lib/wfScanEngine.php:1355
msgid "%1$s contains a suspected malware URL: %2$s"
msgstr ""

#. translators: 1. WordPress Post type. 2. URL. 3. URL.
#. translators: 1. WordPress post type. 2. URL. 3. URL.
#: lib/wfScanEngine.php:1341
#: lib/wfScanEngine.php:1484
msgid "This %1$s contains a suspected malware URL listed on Google's list of malware sites. The URL is: %2$s - More info available at <a href=\"http://safebrowsing.clients.google.com/safebrowsing/diagnostic?site=%3$s&client=googlechrome&hl=en-US\" target=\"_blank\" rel=\"noopener noreferrer\">Google Safe Browsing diagnostic page<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: 1. WordPress Post type. 2. URL.
#: lib/wfScanEngine.php:1347
msgid "%1$s contains a suspected phishing site URL: %2$s"
msgstr ""

#. translators: 1. WordPress Post type. 2. URL.
#. translators: 1. WordPress post type. 2. URL.
#: lib/wfScanEngine.php:1350
#: lib/wfScanEngine.php:1493
msgid "This %1$s contains a URL that is a suspected phishing site that is currently listed on Google's list of known phishing sites. The URL is: %2$s"
msgstr ""

#. translators: 1. WordPress Post type. 2. URL.
#. translators: 1. WordPress post type. 2. URL.
#: lib/wfScanEngine.php:1358
#: lib/wfScanEngine.php:1501
msgid "This %1$s contains a URL that is currently listed on Wordfence's domain blocklist. The URL is: %2$s"
msgstr ""

#. translators: Scan result description.
#: lib/wfScanEngine.php:1367
msgid "Adding issue: %1$s"
msgstr ""

#: lib/wfScanEngine.php:1403
msgid "Scanning comments for URLs on a domain blocklist"
msgstr ""

#. translators: Number of comments left to scan.
#: lib/wfScanEngine.php:1427
msgid "Scanning comments with %d left to scan."
msgstr ""

#. translators: 1. WordPress post type. 2. WordPress author username.
#: lib/wfScanEngine.php:1481
msgid "%1$s with author %2$s contains a suspected malware URL."
msgstr ""

#. translators: WordPress post type.
#: lib/wfScanEngine.php:1490
msgid "%s contains a suspected phishing site URL."
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:1498
msgid "%s contains a suspected malware URL."
msgstr ""

#. translators: WordPress username.
#: lib/wfScanEngine.php:1546
msgid "Author: %s"
msgstr ""

#. translators: Email address.
#: lib/wfScanEngine.php:1549
msgid "Email: %s"
msgstr ""

#. translators: IP address.
#: lib/wfScanEngine.php:1551
msgid "Source IP: %s"
msgstr ""

#. translators: Comment description.
#: lib/wfScanEngine.php:1552
msgid "Scanning comment with %s"
msgstr ""

#. translators: Comment description.
#: lib/wfScanEngine.php:1565
#: lib/wfScanEngine.php:1571
msgid "Marking comment as spam for containing a malware URL. Comment has %s"
msgstr ""

#. translators: Comment description.
#: lib/wfScanEngine.php:1568
msgid "Marking comment as spam for containing a phishing URL. Comment has %s"
msgstr ""

#. translators: Comment description.
#: lib/wfScanEngine.php:1578
msgid "Scanned comment with %s"
msgstr ""

#: lib/wfScanEngine.php:1629
msgid "Scanning for weak passwords"
msgstr ""

#: lib/wfScanEngine.php:1634
msgid "Skipping password strength check because WordPress version is >= 6.8 and MD5 is no longer used."
msgstr ""

#: lib/wfScanEngine.php:1647
msgid "We were unable to generate the user list for your password check."
msgstr ""

#. translators: Number of users.
#: lib/wfScanEngine.php:1663
msgid "Starting password strength check on %d user."
msgid_plural "Starting password strength check on %d users."
msgstr[0] ""
msgstr[1] ""

#. translators: Number of users.
#: lib/wfScanEngine.php:1672
msgid "Total of %d users left to process in password strength check."
msgid_plural "Total of %d users left to process in password strength check."
msgstr[0] ""
msgstr[1] ""

#. translators: WordPress user ID.
#: lib/wfScanEngine.php:1707
msgid "Could not get username for user with ID %d when checking password strength."
msgstr ""

#. translators: 1. WordPress username. 2. WordPress user ID.
#: lib/wfScanEngine.php:1713
msgid "Checking password strength of user '%1$s' with ID %2$d"
msgstr ""

#. translators: 1. WordPress username. 2. WordPress capability.
#: lib/wfScanEngine.php:1721
msgid "User \"%1$s\" with \"%2$s\" access has an easy password."
msgstr ""

#. translators: WordPress capability.
#: lib/wfScanEngine.php:1727
msgid "A user with the a role of '%s' has a password that is easy to guess. Please change this password yourself or ask the user to change it."
msgstr ""

#. translators: WordPress username.
#: lib/wfScanEngine.php:1735
msgid "User \"%s\" with 'subscriber' access has a very easy password."
msgstr ""

#: lib/wfScanEngine.php:1736
msgid "A user with 'subscriber' access has a password that is very easy to guess. Please either change it or ask the user to change their password."
msgstr ""

#. translators: Scan result description.
#: lib/wfScanEngine.php:1743
msgid "Adding issue %s"
msgstr ""

#. translators: WordPress username.
#: lib/wfScanEngine.php:1760
msgid "Completed checking password strength of user '%s'"
msgstr ""

#: lib/wfScanEngine.php:1793
msgid "Scanning to check available disk space"
msgstr ""

#: lib/wfScanEngine.php:1800
msgid "Unable to access available disk space information"
msgstr ""

#. translators: 1. Number of bytes. 2. Number of bytes.
#: lib/wfScanEngine.php:1809
msgid "Total disk space: %1$s -- Free disk space: %2$s"
msgstr ""

#. translators: Number of bytes.
#: lib/wfScanEngine.php:1814
msgid "The disk has %s MB available"
msgstr ""

#. translators: Number of bytes.
#: lib/wfScanEngine.php:1829
msgid "You have %s disk space remaining"
msgstr ""

#. translators: Number of bytes.
#: lib/wfScanEngine.php:1830
msgid "You only have %s of your disk space remaining. Please free up disk space or your website may stop serving requests."
msgstr ""

#: lib/wfScanEngine.php:1843
msgid "Checking Web Application Firewall status"
msgstr ""

#: lib/wfScanEngine.php:1854
msgid "Web Application Firewall is disabled"
msgstr ""

#. translators: Support URL.
#: lib/wfScanEngine.php:1855
msgid "Wordfence's Web Application Firewall has been unexpectedly disabled. If you see a notice at the top of the Wordfence admin pages that says \"The Wordfence Web Application Firewall cannot run,\" click the link in that message to rebuild the configuration. If this does not work, you may need to fix file permissions. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">More Details<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wfScanEngine.php:1870
msgid "Scanning for old themes, plugins and core files"
msgstr ""

#. translators: error message.
#: lib/wfScanEngine.php:1963
msgid "The update check performed during the scan encountered an error: %s"
msgstr ""

#: lib/wfScanEngine.php:1965
msgid "Wordfence cannot detect if the installed plugins and themes are up to date. This might be caused by a PHP compatibility issue in one or more plugins/themes."
msgstr ""

#: lib/wfScanEngine.php:1968
msgid "Wordfence cannot detect if this plugin/theme is up to date. This might be caused by a PHP compatibility issue in the plugin."
msgstr ""

#. translators: Support URL.
#: lib/wfScanEngine.php:1972
#: lib/wfScanEngine.php:2200
#: lib/wfScanEngine.php:2239
#: lib/wfScanEngine.php:2440
#: lib/wfScanEngine.php:2482
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Get more information.<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wfScanEngine.php:1981
msgid "Update Check Encountered Error"
msgstr ""

#. translators: plugin/theme slug.
#: lib/wfScanEngine.php:1981
msgid "Update Check Encountered Error on '%s'"
msgstr ""

#: lib/wfScanEngine.php:1997
msgid "Your WordPress version is out of date"
msgstr ""

#. translators: Software version.
#: lib/wfScanEngine.php:1998
msgid "WordPress version %s is now available. Please upgrade immediately to get the latest security updates from WordPress."
msgstr ""

#. translators: Software version.
#: lib/wfScanEngine.php:2007
msgid "WordPress version %s is now available for your site's current branch. Please upgrade immediately to get the latest fixes and compatibility updates from WordPress."
msgstr ""

#. translators: Software version.
#: lib/wfScanEngine.php:2010
msgid "WordPress version %s is now available for your site's current branch. Please upgrade immediately to get the latest security updates from WordPress."
msgstr ""

#. translators: Software version.
#: lib/wfScanEngine.php:2026
msgid "WordPress version %s is now available. Please upgrade immediately to get the latest fixes and compatibility updates from WordPress."
msgstr ""

#: lib/wfScanEngine.php:2031
msgid "Learn more"
msgstr ""

#. translators: 1. Plugin name. 2. Software version. 3. Software version.
#: lib/wfScanEngine.php:2069
msgid "The Plugin \"%1$s\" needs an upgrade (%2$s -> %3$s)."
msgstr ""

#. translators: Theme name.
#: lib/wfScanEngine.php:2076
#: lib/wfScanEngine.php:2110
msgid "You need to upgrade \"%s\" to the newest version to ensure you have any security fixes the developer has released."
msgstr ""

#. translators: 1. Theme name. 2. Software version. 3. Software version.
#: lib/wfScanEngine.php:2103
msgid "The Theme \"%1$s\" needs an upgrade (%2$s -> %3$s)."
msgstr ""

#. translators: 1. Plugin slug. 2. Malformed date string.
#: lib/wfScanEngine.php:2143
msgid "Encountered bad date string for plugin \"%s\" in abandoned plugin check: %s"
msgstr ""

#. translators: 1. Plugin name. 2. Software version. 3. Software version.
#: lib/wfScanEngine.php:2168
msgid "The Plugin \"%1$s\" appears to be abandoned (updated %2$s, tested to WP %3$s)."
msgstr ""

#. translators: 1. Plugin name. 2. Software version.
#: lib/wfScanEngine.php:2175
msgid "It was last updated %1$s ago and tested up to WordPress %2$s."
msgstr ""

#. translators: 1. Plugin name. 2. Software version.
#: lib/wfScanEngine.php:2182
msgid "The Plugin \"%1$s\" appears to be abandoned (updated %2$s)."
msgstr ""

#. translators: Time duration.
#: lib/wfScanEngine.php:2188
msgid "It was last updated %s ago."
msgstr ""

#: lib/wfScanEngine.php:2194
#: lib/wfScanEngine.php:2233
msgid "It has unpatched security issues and may have compatibility problems with the current version of WordPress."
msgstr ""

#: lib/wfScanEngine.php:2196
msgid "It may have compatibility problems with the current version of WordPress or unknown security issues."
msgstr ""

#. translators: Plugin name.
#: lib/wfScanEngine.php:2231
msgid "The Plugin \"%s\" has been removed from wordpress.org but is still installed on your site."
msgstr ""

#: lib/wfScanEngine.php:2235
msgid "Your site is still using this plugin, but it is not currently available on wordpress.org. Plugins can be removed from wordpress.org for various reasons. This can include benign issues like a plugin author discontinuing development or moving the plugin distribution to their own site, but some might also be due to security issues. In any case, future updates may or may not be available, so it is worth investigating the cause and deciding whether to temporarily or permanently replace or remove the plugin."
msgstr ""

#: lib/wfScanEngine.php:2258
msgid "The Plugin \"%s\" has a security vulnerability."
msgstr ""

#: lib/wfScanEngine.php:2261
msgid "To protect your site from this vulnerability, the safest option is to deactivate and completely remove \"%s\" until a patched version is available. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Get more information.<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/wfScanEngine.php:2297
msgid "Scanning for admin users not created through WordPress"
msgstr ""

#. translators: WordPress username.
#: lib/wfScanEngine.php:2319
msgid "An admin user with the username %s was created outside of WordPress."
msgstr ""

#. translators: WordPress username.
#: lib/wfScanEngine.php:2320
msgid "An admin user with the username %s was created outside of WordPress. It's possible a plugin could have created the account, but if you do not recognize the user, we suggest you remove it."
msgstr ""

#. translators: WordPress username.
#: lib/wfScanEngine.php:2346
msgid "An admin user with a suspicious username %s was found."
msgstr ""

#. translators: WordPress username.
#: lib/wfScanEngine.php:2347
msgid "An admin user with a suspicious username %s was found. Administrators accounts with usernames similar to this are commonly seen created by hackers. It's possible a plugin could have created the account, but if you do not recognize the user, we suggest you remove it."
msgstr ""

#: lib/wfScanEngine.php:2368
msgid "Scanning for suspicious site options"
msgstr ""

#: lib/wfScanEngine.php:2403
msgid "Examining URLs found in the options we scanned for dangerous websites"
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:2427
#: lib/wfScanEngine.php:2433
msgid "Option contains a suspected malware URL: %s"
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:2428
msgid "This option contains a suspected malware URL listed on Google's list of malware sites. It may indicate your site is infected with malware. The URL is: %s"
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:2430
msgid "Option contains a suspected phishing site URL: %s"
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:2431
msgid "This option contains a URL that is a suspected phishing site that is currently listed on Google's list of known phishing sites. It may indicate your site is infected with malware. The URL is: %s"
msgstr ""

#. translators: URL.
#: lib/wfScanEngine.php:2434
msgid "This option contains a URL that is currently listed on Wordfence's domain blocklist. It may indicate your site is infected with malware. The URL is: %s"
msgstr ""

#: lib/wfScanEngine.php:2474
msgid "Checking for future GeoIP support"
msgstr ""

#: lib/wfScanEngine.php:2479
msgid "PHP Update Needed for Country Blocking"
msgstr ""

#. translators: Software version.
#: lib/wfScanEngine.php:2480
msgid "The GeoIP database that is required for country blocking has been updated to a new format. This new format requires sites to run PHP 5.4 or newer, and this site is on PHP %s. To ensure country blocking continues functioning, please update PHP."
msgstr ""

#: lib/wfScanEngine.php:2530
msgid "Previous scan was stopped successfully."
msgstr ""

#: lib/wfScanEngine.php:2531
msgid "Scan was stopped on administrator request."
msgstr ""

#: lib/wfScanEngine.php:2547
msgid "Entering start scan routine"
msgstr ""

#: lib/wfScanEngine.php:2549
msgid "A scan is already running. Use the stop scan button if you would like to terminate the current scan."
msgstr ""

#: lib/wfScanEngine.php:2565
msgid "Cached result for scan start test: %s"
msgstr ""

#. translators: Scan start test result data.
#: lib/wfScanEngine.php:2581
msgid "Test result of scan start URL fetch: %s"
msgstr ""

#. translators: WordPress admin panel URL.
#: lib/wfScanEngine.php:2594
msgid "Starting cron with normal ajax at URL %s"
msgstr ""

#. translators: Error message.
#. translators: WordPress admin panel URL.
#: lib/wfScanEngine.php:2618
#: lib/wfScanEngine.php:2653
msgid "There was an error starting the scan: %s."
msgstr ""

#: lib/wfScanEngine.php:2620
#: lib/wfScanEngine.php:2655
msgid "There was an unknown error starting the scan."
msgstr ""

#: lib/wfScanEngine.php:2627
#: lib/wfScanEngine.php:2661
msgid "Scan process ended after forking."
msgstr ""

#. translators: WordPress admin panel URL.
#: lib/wfScanEngine.php:2631
msgid "Starting cron via proxy at URL %s"
msgstr ""

#. translators: Time in seconds.
#: lib/wfScanEngine.php:2707
msgid "Got value from wf config maxExecutionTime: %s"
msgstr ""

#. translators: Time in seconds.
#: lib/wfScanEngine.php:2711
msgid "getMaxExecutionTime() returning config value: %s"
msgstr ""

#. translators: PHP ini value.
#: lib/wfScanEngine.php:2718
msgid "Got max_execution_time value from ini: %s"
msgstr ""

#. translators: 1. PHP ini setting. 2. Time in seconds.
#: lib/wfScanEngine.php:2727
msgid "ini value of %1$d is higher than value for WORDFENCE_SCAN_MAX_INI_EXECUTION_TIME (%2$d), reducing"
msgstr ""

#. translators: PHP ini setting.
#: lib/wfScanEngine.php:2737
msgid "getMaxExecutionTime() returning half ini value: %d"
msgstr ""

#: lib/wfScanEngine.php:2744
msgid "getMaxExecutionTime() returning default of: 15"
msgstr ""

#. translators: 1. HTTP status code.
#: lib/wfScanEngine.php:2967
msgid "Got error response from Wordfence servers: %s"
msgstr ""

#: lib/wfScanEngine.php:2971
msgid "Invalid response from Wordfence servers."
msgstr ""

#: lib/wfScanMonitor.php:78
msgid "Attempting to resume scan stage (%d attempt(s) remaining)..."
msgstr ""

#: lib/wfSupportController.php:429
msgid "Are you enjoying using Wordfence Security?"
msgstr ""

#: lib/wfSupportController.php:431
msgid "Please consider leaving us a 5-star review on wordpress.org. Your review helps other members of the WordPress community find plugins that fit their needs."
msgstr ""

#: lib/wfSupportController.php:432
msgid "Leave Review"
msgstr ""

#: lib/wfSupportController.php:435
msgid "What can we do to improve Wordfence Security?"
msgstr ""

#: lib/wfSupportController.php:437
msgid "Submit Feedback"
msgstr ""

#: lib/wfSupportController.php:439
msgid "Thank you for providing your feedback on Wordfence Security"
msgstr ""

#: lib/wfUnlockMsg.php:2
msgid "If you are a WordPress user with administrative privileges on this site please enter your email in the box below and click &quot;Send&quot;. You will then receive an email that helps you regain access."
msgstr ""

#: lib/wfUnlockMsg.php:6
msgid "Send Unlock Email"
msgstr ""

#. translators: 1. Plugin slug.
#: lib/wfUpdateCheck.php:50
msgid "Outdated plugin scan adjusted invalid return value in plugins_api filter for %s"
msgstr ""

#: lib/wfUpdateCheck.php:112
#: views/dashboard/options-group-alert.php:73
#: views/scanner/issue-base.php:32
#: views/scanner/issue-base.php:41
#: views/scanner/issue-base.php:106
msgid "Critical"
msgstr ""

#: lib/wfUpdateCheck.php:114
#: views/dashboard/options-group-alert.php:74
#: views/scanner/issue-base.php:33
#: views/scanner/issue-base.php:42
#: views/scanner/issue-base.php:109
msgid "High"
msgstr ""

#: lib/wfUpdateCheck.php:116
#: views/dashboard/options-group-alert.php:75
#: views/scanner/issue-base.php:34
#: views/scanner/issue-base.php:43
#: views/scanner/issue-base.php:112
msgid "Medium"
msgstr ""

#: lib/wfUpdateCheck.php:118
#: views/dashboard/options-group-alert.php:76
#: views/scanner/issue-base.php:35
#: views/scanner/issue-base.php:44
#: views/scanner/issue-base.php:115
msgid "Low"
msgstr ""

#: lib/wfUpdateCheck.php:701
msgid "Failed obtaining core vulnerability data, skipping check."
msgstr ""

#: lib/wfUpdateCheck.php:735
msgid "Failed obtaining vulnerability data, skipping check."
msgstr ""

#: lib/wfUtils.php:39
msgid "a moment"
msgstr ""

#: lib/wfUtils.php:74
msgid "year"
msgid_plural "years"
msgstr[0] ""
msgstr[1] ""

#: lib/wfUtils.php:75
#: lib/wfUtils.php:78
#: lib/wfUtils.php:109
msgid "month"
msgid_plural "months"
msgstr[0] ""
msgstr[1] ""

#: lib/wfUtils.php:79
#: lib/wfUtils.php:82
#: lib/wfUtils.php:115
msgid "day"
msgid_plural "days"
msgstr[0] ""
msgstr[1] ""

#: lib/wfUtils.php:83
#: lib/wfUtils.php:86
#: lib/wfUtils.php:121
msgid "hour"
msgid_plural "hours"
msgstr[0] ""
msgstr[1] ""

#: lib/wfUtils.php:87
#: lib/wfUtils.php:90
#: lib/wfUtils.php:127
msgid "minute"
msgid_plural "minutes"
msgstr[0] ""
msgstr[1] ""

#: lib/wfUtils.php:94
msgid "less than a minute"
msgstr ""

#. translators: Number of seconds.
#: lib/wfUtils.php:96
msgid "%d seconds"
msgstr ""

#: lib/wfUtils.php:130
msgid "second"
msgid_plural "seconds"
msgstr[0] ""
msgstr[1] ""

#: lib/wfUtils.php:134
msgid "less than 1 second"
msgstr ""

#: lib/wfUtils.php:1661
#: lib/wfUtils.php:1670
msgid "Wordfence error: No encryption key found!"
msgstr ""

#. translators: Error message.
#: lib/wfUtils.php:1983
msgid "Call to Wordfence API to resolve IPs failed: %s"
msgstr ""

#: lib/wfVersionCheckController.php:50
#: lib/wfVersionCheckController.php:75
msgid "PHP version too old"
msgstr ""

#. translators: 1. PHP version. 2. PHP version.
#: lib/wfVersionCheckController.php:53
msgid "Your site is using a PHP version (%1$s) that will no longer be supported by Wordfence in an upcoming release and needs to be updated. We recommend using the newest version of PHP available but will currently support PHP versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#. translators: Support URL.
#. translators: 1. WordPress version. 2. WordPress version.
#: lib/wfVersionCheckController.php:58
#: lib/wfVersionCheckController.php:81
#: lib/wfVersionCheckController.php:157
#: lib/wfVersionCheckController.php:179
msgid "Learn More: %s"
msgstr ""

#. translators: 1. PHP version. 2. PHP version.
#: lib/wfVersionCheckController.php:66
msgid "<strong>WARNING: </strong> Your site is using a PHP version (%1$s) that will no longer be supported by Wordfence in an upcoming release and needs to be updated. We recommend using the newest version of PHP available but will currently support PHP versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#. translators: 1. PHP version. 2. PHP version.
#: lib/wfVersionCheckController.php:78
msgid "Your site is using a PHP version (%1$s) that is no longer supported by Wordfence and needs to be updated. We recommend using the newest version of PHP available but will currently support PHP versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#. translators: 1. PHP version. 2. PHP version.
#: lib/wfVersionCheckController.php:89
msgid "<strong>WARNING: </strong> Your site is using a PHP version (%1$s) that is no longer supported by Wordfence and needs to be updated. We recommend using the newest version of PHP available but will currently support PHP versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#: lib/wfVersionCheckController.php:151
#: lib/wfVersionCheckController.php:176
msgid "WordPress version too old"
msgstr ""

#. translators: 1. WordPress version. 2. WordPress version.
#: lib/wfVersionCheckController.php:154
msgid "Your site is using a WordPress version (%1$s) that will no longer be supported by Wordfence in an upcoming release and needs to be updated. We recommend using the newest version of WordPress but will currently support WordPress versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#. translators: 1. WordPress version. 2. WordPress version.
#: lib/wfVersionCheckController.php:165
msgid "<strong>WARNING: </strong> Your site is using a WordPress version (%1$s) that will no longer be supported by Wordfence in an upcoming release and needs to be updated. We recommend using the newest version of WordPress but will currently support WordPress versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#. translators: 1. WordPress version. 2. WordPress version.
#: lib/wfVersionCheckController.php:179
msgid "Your site is using a WordPress version (%1$s) that is no longer supported by Wordfence and needs to be updated. We recommend using the newest version of WordPress but will currently support WordPress versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#. translators: 1. WordPress version. 2. WordPress version.
#: lib/wfVersionCheckController.php:187
msgid "<strong>WARNING: </strong> Your site is using a WordPress version (%1$s) that is no longer supported by Wordfence and needs to be updated. We recommend using the newest version of WordPress but will currently support WordPress versions as old as %2$s. Version checks are run regularly, so if you have successfully updated, you can dismiss this notice or check that the update has taken effect later."
msgstr ""

#. translators: File path.
#: lib/wfView.php:52
msgid "The view %s does not exist or is not readable."
msgstr ""

#: lib/wfView.php:70
msgid "The view could not be loaded."
msgstr ""

#: lib/wfViewResult.php:8
msgid "Wordfence: File Viewer"
msgstr ""

#: lib/wfViewResult.php:11
msgid "File Size:"
msgstr ""

#: lib/wfViewResult.php:12
msgid "File last modified:"
msgstr ""

#: lib/wordfenceClass.php:201
msgid ""
"To ensure uninterrupted Premium Wordfence protection on your site,\n"
"please renew your license by visiting https://www.wordfence.com/ Sign in, go to your dashboard,\n"
"select the license about to expire and click the button to renew that license."
msgstr ""

#: lib/wordfenceClass.php:227
msgid "Your Premium Wordfence License is set to auto-renew in 10 days."
msgstr ""

#: lib/wordfenceClass.php:228
msgid "To update your license settings please visit https://www.wordfence.com/zz9/dashboard"
msgstr ""

#: lib/wordfenceClass.php:240
msgid "Your Premium Wordfence License expires in less than 2 weeks."
msgstr ""

#: lib/wordfenceClass.php:243
msgid "Your Premium Wordfence License expires in less than a week."
msgstr ""

#: lib/wordfenceClass.php:246
msgid "Your Premium Wordfence License expires in 2 days."
msgstr ""

#: lib/wordfenceClass.php:249
msgid "Your Premium Wordfence License expires in 1 day."
msgstr ""

#: lib/wordfenceClass.php:253
msgid "Your Wordfence Premium License has Expired!"
msgstr ""

#: lib/wordfenceClass.php:289
msgid "The Wordfence Premium License in use on this site has been removed from your account."
msgstr ""

#: lib/wordfenceClass.php:289
msgid "The license you were using has been removed from your account. Please reach <NAME_EMAIL> or create a Premium support case at https://support.wordfence.com/support/tickets for more information. Our staff is happy to help."
msgstr ""

#. translators: Wordfence license key.
#: lib/wordfenceClass.php:299
msgid "Could not verify Wordfence License: %s"
msgstr ""

#. translators: WordPress version.
#: lib/wordfenceClass.php:398
msgid "WordPress (v%s)"
msgstr ""

#. translators: Number of plugins.
#: lib/wordfenceClass.php:402
msgid "%d plugin"
msgid_plural "%d plugins"
msgstr[0] ""
msgstr[1] ""

#. translators: Number of themes.
#: lib/wordfenceClass.php:407
msgid "%d theme"
msgid_plural "%d themes"
msgstr[0] ""
msgstr[1] ""

#: lib/wordfenceClass.php:411
msgid "An update is available for "
msgid_plural "Updates are available for "
msgstr[0] ""
msgstr[1] ""

#: lib/wordfenceClass.php:416
msgid "and "
msgstr ""

#. translators: Wordfence version.
#: lib/wordfenceClass.php:464
msgid "`runInstall` called with previous version = %s"
msgstr ""

#: lib/wordfenceClass.php:861
msgid "Automatically generated from previous country blocking settings"
msgstr ""

#: lib/wordfenceClass.php:1307
#: views/user/disabled-application-passwords.php:15
msgid "Application passwords have been disabled by Wordfence."
msgstr ""

#: lib/wordfenceClass.php:1446
msgid "Upgrade To Premium"
msgstr ""

#: lib/wordfenceClass.php:1459
msgid "wp_mail from address is incomplete, attempting to fix"
msgstr ""

#. translators: Email address.
#: lib/wordfenceClass.php:1469
msgid "Fixing wp_mail from address: %s"
msgstr ""

#: lib/wordfenceClass.php:1573
msgid "You appear to have logged out or you are not an admin. Please sign-out and sign-in again."
msgstr ""

#: lib/wordfenceClass.php:1578
msgid "Your browser sent an invalid security token to Wordfence. Please try reloading this page or signing out and in again."
msgstr ""

#: lib/wordfenceClass.php:1584
msgid "Wordfence encountered an internal error executing that request."
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:1690
msgid "2FA Migration Error: %s"
msgstr ""

#: lib/wordfenceClass.php:1730
msgid ""
"<strong>ERROR</strong>: The password could not be changed. Please choose a stronger password and try again. A strong password will follow these guidelines: <ul class=\"wf-password-requirements\">\n"
"\t\t\t\t\t<li>At least 12 characters</li>\n"
"\t\t\t\t\t<li>Uppercase and lowercase letters</li>\n"
"\t\t\t\t\t<li>At least one symbol</li>\n"
"\t\t\t\t\t<li>At least one number</li>\n"
"\t\t\t\t\t<li>Avoid common words or sequences of letters/numbers</li>\n"
"\t\t\t\t</ul>"
msgstr ""

#: lib/wordfenceClass.php:1742
msgid "Passwords containing a space followed by \"wf\" without quotes are not allowed."
msgstr ""

#. translators: Support URL.
#: lib/wordfenceClass.php:1757
msgid "Please choose a different password. The password you are using exists on lists of passwords leaked in data breaches. Attackers use such lists to break into sites and install malicious code. <a href=\"%s\">Learn More</a>"
msgstr ""

#. translators: 1. Password reset limit (number). 2. WordPress username.
#: lib/wordfenceClass.php:1879
msgid "Exceeded the maximum number of tries to recover their password which is set at: %1$s. The last username or email they entered before getting locked out was: '%2$s'"
msgstr ""

#: lib/wordfenceClass.php:1931
#: lib/wordfenceClass.php:2114
msgid "Sorry but your browser sent an invalid security token when trying to use this form."
msgstr ""

#: lib/wordfenceClass.php:1936
msgid "Please wait 3 minutes and try again"
msgstr ""

#: lib/wordfenceClass.php:1937
msgid "You have used this form too much. Please wait 3 minutes and try again."
msgstr ""

#: lib/wordfenceClass.php:1976
msgid "Unlock email requested"
msgstr ""

#: lib/wordfenceClass.php:1978
msgid "Your request was received"
msgstr ""

#. translators: Email address.
#: lib/wordfenceClass.php:1979
msgid "We received a request to email \"%s\" instructions to unlock their access. If that is the email address of a site administrator or someone on the Wordfence alert list, they have been emailed instructions on how to regain access to this system. The instructions we sent will expire 30 minutes from now."
msgstr ""

#: lib/wordfenceClass.php:1985
msgid "Invalid key provided for authentication."
msgstr ""

#: lib/wordfenceClass.php:1996
msgid "Request received via unlock email link to unblock all IPs."
msgstr ""

#: lib/wordfenceClass.php:2005
msgid "Request received via unlock email link to unblock all IPs via disabling firewall rules."
msgstr ""

#: lib/wordfenceClass.php:2013
msgid "Invalid function specified. Please check the link we emailed you and make sure it was not cut-off by your email reader."
msgstr ""

#: lib/wordfenceClass.php:2058
msgid "Unsubscribe Requested"
msgstr ""

#: lib/wordfenceClass.php:2120
msgid "An error occurred while saving the license."
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:2122
#: lib/wordfenceClass.php:2142
msgid "An error occurred while saving the license: %s"
msgstr ""

#: lib/wordfenceClass.php:2158
msgid "Rescheduled missing daily cron"
msgstr ""

#: lib/wordfenceClass.php:2163
msgid "Rescheduled missing hourly cron"
msgstr ""

#: lib/wordfenceClass.php:2467
#: lib/wordfenceClass.php:2471
msgid "Accessed a banned URL"
msgstr ""

#: lib/wordfenceClass.php:2478
#: lib/wordfenceClass.php:2482
msgid "POST received with blank user-agent and referer"
msgstr ""

#: lib/wordfenceClass.php:2616
msgid "<strong>ERROR</strong>: You can't register using that username"
msgstr ""

#: lib/wordfenceClass.php:2656
msgid "Sorry, you are not allowed to list users."
msgstr ""

#: lib/wordfenceClass.php:2663
msgid "Invalid user ID."
msgstr ""

#: lib/wordfenceClass.php:2807
msgid "<strong>VERIFICATION FAILED</strong>: Two-factor authentication verification failed. Please try again."
msgstr ""

#. translators: 1. WordPress username. 2. Password reset URL.
#. translators: 1. WordPress username. 2. Reset password URL.
#: lib/wordfenceClass.php:2814
#: lib/wordfenceClass.php:3262
msgid "<strong>ERROR</strong>: The username or password you entered is incorrect. <a href=\"%2$s\" title=\"Password Lost and Found\">Lost your password</a>?"
msgstr ""

#. translators: 1. WordPress admin panel URL. 2. Support URL.
#: lib/wordfenceClass.php:2824
#: lib/wordfenceClass.php:2960
msgid "<strong>WARNING: </strong>The password you are using exists on lists of passwords leaked in data breaches. Attackers use such lists to break into sites and install malicious code. Please <a href=\"%1$s\">change your password</a>. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wordfenceClass.php:2848
msgid "<strong>INVALID CODE</strong>: Please sign in again and add a space, the letters <code>wf</code>, and the code from your authenticator app to the end of your password (e.g., <code>wf123456</code>)."
msgstr ""

#: lib/wordfenceClass.php:2852
msgid "<strong>INVALID CODE</strong>: Please sign in again and add a space, the letters <code>wf</code>, and the code sent to your phone to the end of your password (e.g., <code>wf123456</code>)."
msgstr ""

#: lib/wordfenceClass.php:2859
#: lib/wordfenceClass.php:2910
#: lib/wordfenceClass.php:2933
msgid "<strong>AUTHENTICATION FAILURE</strong>: A temporary failure was encountered while trying to log in. Please try again."
msgstr ""

#: lib/wordfenceClass.php:2869
msgid "<strong>INVALID CODE</strong>: You need to enter the code generated by your authenticator app. The code should be a six digit number (e.g., 123456)."
msgstr ""

#: lib/wordfenceClass.php:2873
msgid "<strong>INVALID CODE</strong>: You need to enter the code generated sent to your phone. The code should be a six digit number (e.g., 123456)."
msgstr ""

#: lib/wordfenceClass.php:2919
msgid "<strong>CODE EXPIRED. CHECK YOUR PHONE:</strong> The code you entered has expired. Codes are only valid for 30 minutes for security reasons. We have sent you a new code. Please sign in using your username, password, and the new code we sent you."
msgstr ""

#: lib/wordfenceClass.php:2942
msgid "<strong>INVALID CODE</strong>: You need to enter your password and the code we sent to your phone. The code should start with 'wf' and should be four characters (e.g., wfAB12)."
msgstr ""

#: lib/wordfenceClass.php:2993
msgid "<strong>CODE REQUIRED</strong>: Please check your authenticator app for the current code. Enter it below to sign in."
msgstr ""

#: lib/wordfenceClass.php:2998
msgid "<strong>CODE REQUIRED</strong>: Please check your authenticator app for the current code. Please sign in again and add a space, the letters <code>wf</code>, and the code to the end of your password (e.g., <code>wf123456</code>)."
msgstr ""

#: lib/wordfenceClass.php:3032
#: lib/wordfenceClass.php:3089
msgid "<strong>CHECK YOUR PHONE</strong>: A code has been sent to your phone and will arrive within 30 seconds. Enter it below to sign in."
msgstr ""

#: lib/wordfenceClass.php:3037
msgid "<strong>CHECK YOUR PHONE</strong>: A code has been sent to your phone and will arrive within 30 seconds. Please sign in again and add a space, the letters <code>wf</code>, and the code to the end of your password (e.g., <code>wf123456</code>)."
msgstr ""

#: lib/wordfenceClass.php:3094
msgid "<strong>CHECK YOUR PHONE</strong>: A code has been sent to your phone and will arrive within 30 seconds. Please sign in again and add a space and the code to the end of your password (e.g., <code>wfABCD</code>)."
msgstr ""

#. translators: 1. Reset password URL. 2. Support URL.
#: lib/wordfenceClass.php:3115
#: lib/wordfenceClass.php:3151
msgid "<strong>WARNING: </strong>Your login has been allowed because you have previously logged in from the same IP, but you will be blocked if your IP changes. The password you are using exists on lists of passwords leaked in data breaches. Attackers use such lists to break into sites and install malicious code. Please <a href=\"%1$s\">change your password</a>. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: 1. Reset password URL. 2. Support URL.
#: lib/wordfenceClass.php:3132
#: lib/wordfenceClass.php:3168
msgid "<strong>INSECURE PASSWORD:</strong> Your login attempt has been blocked because the password you are using exists on lists of passwords leaked in data breaches. Attackers use such lists to break into sites and install malicious code. Please <a href=\"%1$s\">reset your password</a> to reactivate your account. <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span style=\"font-size:0;\"> (opens in new tab)</span></a>"
msgstr ""

#: lib/wordfenceClass.php:3140
msgid "Admin Login Blocked"
msgstr ""

#. translators: WordPress username.
#: lib/wordfenceClass.php:3140
msgid "A user with username \"%s\" who has administrator access tried to sign in to your WordPress site. Access was denied because all administrator accounts are required to have Cellphone Sign-in enabled but this account does not."
msgstr ""

#. translators: WordPress username.
#: lib/wordfenceClass.php:3141
msgid "<strong>Cellphone Sign-in Required</strong>: Cellphone Sign-in is required for all administrator accounts. Please contact the site administrator to enable it for your account."
msgstr ""

#: lib/wordfenceClass.php:3181
msgid "Blocked by Wordfence Security Network"
msgstr ""

#: lib/wordfenceClass.php:3211
msgid "Blocked by login security setting"
msgstr ""

#. translators: WordPress username.
#: lib/wordfenceClass.php:3221
msgid "Used an invalid username '%s' to try to sign in"
msgstr ""

#. translators: 1. Login attempt limit. 2. WordPress username.
#: lib/wordfenceClass.php:3240
msgid "Exceeded the maximum number of login failures which is: %1$s. The last username they tried to sign in with was: '%2$s'"
msgstr ""

#: lib/wordfenceClass.php:3567
msgid "An invalid type was specified to get file."
msgstr ""

#: lib/wordfenceClass.php:3583
msgid "We could not fetch a core WordPress file from the Wordfence API."
msgstr ""

#: lib/wordfenceClass.php:3642
msgid "Wordfence Test Email"
msgstr ""

#. translators: 1. Site URL. 2. IP address.
#: lib/wordfenceClass.php:3642
msgid ""
"This is a test email from %1$s.\n"
"The IP address that requested this was: %2$s"
msgstr ""

#: lib/wordfenceClass.php:3649
msgid "Cellphone Sign-in is only available to paid members. <a href=\"https://www.wordfence.com/gnl1twoFac3/wordfence-signup/\" target=\"_blank\" rel=\"noopener noreferrer\">Click here to upgrade now.<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wordfenceClass.php:3656
msgid "The username you specified does not exist."
msgstr ""

#: lib/wordfenceClass.php:3665
msgid "The username you specified is already enabled."
msgstr ""

#: lib/wordfenceClass.php:3670
msgid "Unknown authentication mode."
msgstr ""

#: lib/wordfenceClass.php:3675
msgid "The phone number you entered must start with a '+', then country code and then area code and number. For example, a number in the United States with country code '1' would look like this: ******-555-1234"
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:3682
#: lib/wordfenceClass.php:3714
#: lib/wordfenceClass.php:3777
msgid "Could not contact Wordfence servers to generate a verification code: %s"
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:3694
#: lib/wordfenceClass.php:3738
msgid "Could not generate verification code: %s"
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:3695
#: lib/wordfenceClass.php:3739
msgid "We could not generate a verification code."
msgstr ""

#: lib/wordfenceClass.php:3755
msgid "Unknown two-factor authentication mode."
msgstr ""

#: lib/wordfenceClass.php:3788
msgid "The code you entered is invalid. Cellphone sign-in will not be enabled for this user until you enter a valid code."
msgstr ""

#: lib/wordfenceClass.php:3793
msgid "We could not find the user you are trying to activate. They may have been removed from the list of Cellphone Sign-in users. Please reload this page."
msgstr ""

#: lib/wordfenceClass.php:3875
msgid "That user has already been removed from the list."
msgstr ""

#: lib/wordfenceClass.php:3895
msgid "No scan is scheduled"
msgstr ""

#: lib/wordfenceClass.php:3900
msgid "Next scan is starting now"
msgstr ""

#. translators: 1. Time until. 2. Localized date.
#: lib/wordfenceClass.php:3903
msgid "Next scan in %1$s (%2$s)"
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:3920
msgid "Scheduled Wordfence scan starting at %s"
msgstr ""

#: lib/wordfenceClass.php:3943
msgid "Sorry but this feature is only available for paid customers."
msgstr ""

#. translators: Site URL.
#: lib/wordfenceClass.php:3957
msgid "SITE: %s"
msgstr ""

#. translators: Plugin version.
#: lib/wordfenceClass.php:3958
msgid "PLUGIN VERSION: %s"
msgstr ""

#. translators: WordPress version.
#: lib/wordfenceClass.php:3959
msgid "WORDPRESS VERSION: %s"
msgstr ""

#. translators: Wordfence license key.
#: lib/wordfenceClass.php:3960
msgid "LICENSE KEY: %s"
msgstr ""

#. translators: Email address.
#: lib/wordfenceClass.php:3961
msgid "ADMIN EMAIL: %s"
msgstr ""

#. translators: Email address.
#: lib/wordfenceClass.php:3962
msgid "LOG:"
msgstr ""

#: lib/wordfenceClass.php:3978
msgid "# Scan Issues"
msgstr ""

#. translators: Number of scan results.
#: lib/wordfenceClass.php:3983
msgid "## New Issues (%d total)"
msgstr ""

#. translators: Number of scan results.
#: lib/wordfenceClass.php:4010
msgid "## Ignored Issues (%d total)"
msgstr ""

#: lib/wordfenceClass.php:4031
msgid "No Ignored Issues"
msgstr ""

#: lib/wordfenceClass.php:4053
msgid "Wordfence Activity Log"
msgstr ""

#: lib/wordfenceClass.php:4077
msgid "Could not understand the response we received from the Wordfence servers when applying for a free license key."
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:4082
msgid "A free license key could not be fetched from Wordfence: %s"
msgstr ""

#: lib/wordfenceClass.php:4186
msgid "We could not find your .htaccess file to modify it."
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:4191
msgid "We found your .htaccess file but could not open it for writing: %s"
msgstr ""

#: lib/wordfenceClass.php:4274
msgid "All Countries"
msgstr ""

#: lib/wordfenceClass.php:4277
msgid "1 Country"
msgstr ""

#. translators: Number of countries.
#: lib/wordfenceClass.php:4280
msgid "%d Countries"
msgstr ""

#: lib/wordfenceClass.php:4284
msgid "Entire Site"
msgstr ""

#: lib/wordfenceClass.php:4287
msgid "Login Only"
msgstr ""

#: lib/wordfenceClass.php:4290
msgid "Site Except Login"
msgstr ""

#: lib/wordfenceClass.php:4298
msgid "IP Range"
msgstr ""

#. translators: 2FA backup codes.
#: lib/wordfenceClass.php:4299
#: lib/wordfenceClass.php:6429
msgid "User Agent"
msgstr ""

#: lib/wordfenceClass.php:4300
#: views/blocking/blocking-create.php:201
msgid "Referrer"
msgstr ""

#: lib/wordfenceClass.php:4315
msgid "Permanent"
msgstr ""

#: lib/wordfenceClass.php:4387
msgid "An error occurred while creating the block."
msgstr ""

#: lib/wordfenceClass.php:4393
msgid "No block parameters were provided."
msgstr ""

#: lib/wordfenceClass.php:4432
#: lib/wordfenceClass.php:4464
msgid "No blocks were provided."
msgstr ""

#: lib/wordfenceClass.php:4505
msgid "The license provided is already in use on another site."
msgstr ""

#: lib/wordfenceClass.php:4510
msgid "The Wordfence activation server returned an unexpected response. Please try again."
msgstr ""

#: lib/wordfenceClass.php:4516
msgid "We received an error while trying to activate the license with the Wordfence servers: "
msgstr ""

#: lib/wordfenceClass.php:4533
msgid "No license was provided to install."
msgstr ""

#: lib/wordfenceClass.php:4584
msgid "An unknown configuration section was provided."
msgstr ""

#: lib/wordfenceClass.php:4590
msgid "No configuration section was provided."
msgstr ""

#: lib/wordfenceClass.php:4636
#: lib/wordfenceClass.php:4641
msgid "An error occurred while saving the configuration."
msgstr ""

#: lib/wordfenceClass.php:4652
#: modules/login-security/classes/controller/ajax.php:427
msgid "No configuration changes were provided to save."
msgstr ""

#: lib/wordfenceClass.php:4661
msgid "Invalid option specified"
msgstr ""

#: lib/wordfenceClass.php:4711
#: lib/wordfenceClass.php:5361
#: lib/wordfenceClass.php:5392
#: lib/wordfenceClass.php:7631
#: lib/wordfenceClass.php:7666
#: lib/wordfenceClass.php:7727
msgid "We could not find that issue in our database."
msgstr ""

#: lib/wordfenceClass.php:4721
msgid "An error occurred while trying to hide the file."
msgstr ""

#: lib/wordfenceClass.php:4734
msgid "An invalid file was requested for hiding."
msgstr ""

#: lib/wordfenceClass.php:4761
#: lib/wordfenceClass.php:5372
msgid "You don't have permission to repair .htaccess. You need to either fix the file manually using FTP or change the file permissions and ownership so that your web server has write access to repair the file."
msgstr ""

#: lib/wordfenceClass.php:4785
msgid "Manual permanent block by admin"
msgstr ""

#: lib/wordfenceClass.php:4826
msgid "Please enter a valid IP address to block."
msgstr ""

#: lib/wordfenceClass.php:4829
msgid "You can't block your own IP address."
msgstr ""

#. translators: IP address.
#: lib/wordfenceClass.php:4833
msgid "The IP address %s is allowlisted and can't be blocked. You can remove this IP from the allowlist on the Wordfence options page."
msgstr ""

#. translators: IP address.
#: lib/wordfenceClass.php:4835
msgid "The IP address %s is in a range of IP addresses that Wordfence does not block. The IP range may be internal or belong to a service safe to allow access for."
msgstr ""

#: lib/wordfenceClass.php:4841
msgid "The IP address you're trying to block belongs to Google. Your options are currently set to not block these crawlers. Change this in Wordfence options if you want to manually block Google."
msgstr ""

#: lib/wordfenceClass.php:4884
msgid "An invalid operation was called."
msgstr ""

#: lib/wordfenceClass.php:4894
msgid "An invalid status was specified when trying to update that issue."
msgstr ""

#: lib/wordfenceClass.php:4906
msgid "Scan stop request received."
msgstr ""

#: lib/wordfenceClass.php:4907
msgid "A request was received to stop the previous scan."
msgstr ""

#: lib/wordfenceClass.php:4956
msgid "Idle"
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:4965
msgid "Scan completed on %s"
msgstr ""

#: lib/wordfenceClass.php:4972
msgid "Last scan failed"
msgstr ""

#. translators: Time until.
#: lib/wordfenceClass.php:5035
msgid "more than %s"
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:5039
msgid "The current scan looks like it has failed. Its last status update was <span id=\"wf-scan-failed-time-ago\">%s</span> ago. You may continue to wait in case it resumes or stop and restart the scan. Some sites may need adjustments to run scans reliably."
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:5039
#: lib/wordfenceClass.php:5047
#: lib/wordfenceClass.php:5078
msgid "Click here for steps you can try."
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:5040
msgid "Cancel Scan"
msgstr ""

#: lib/wordfenceClass.php:5047
msgid "The previous scan has failed. Some sites may need adjustments to run scans reliably."
msgstr ""

#. translators: Time limit (number).
#: lib/wordfenceClass.php:5053
msgid "The previous scan has terminated because the time limit of %s was reached. This limit can be customized on the options page."
msgstr ""

#: lib/wordfenceClass.php:5059
msgid "The previous scan has terminated because we detected an update occurring during the scan."
msgstr ""

#: lib/wordfenceClass.php:5068
msgid "Wordfence will make one attempt to resume each failed scan stage. This scan may recover if this attempt is successful."
msgstr ""

#: lib/wordfenceClass.php:5070
msgid "Wordfence will make up to %d attempts to resume each failed scan stage. This scan may recover if one of these attempts is successful."
msgstr ""

#: lib/wordfenceClass.php:5077
msgid "Scan Stage Failed"
msgstr ""

#: lib/wordfenceClass.php:5078
msgid "A scan stage has failed to start. This is often because the site either cannot make outbound requests or is blocked from connecting to itself."
msgstr ""

#: lib/wordfenceClass.php:5084
msgid "Scans are not functional because SSL is unavailable."
msgstr ""

#: lib/wordfenceClass.php:5090
msgid "The scan has failed because we were unable to contact the Wordfence servers. Some sites may need adjustments to run scans reliably."
msgstr ""

#: lib/wordfenceClass.php:5090
#: lib/wordfenceClass.php:5098
msgid "Click here for steps you can try"
msgstr ""

#: lib/wordfenceClass.php:5090
#: lib/wordfenceClass.php:5098
msgid "check for a server outage"
msgstr ""

#: lib/wordfenceClass.php:5098
msgid "The scan has failed because we received an unexpected response from the Wordfence servers. This may be a temporary error, though some sites may need adjustments to run scans reliably."
msgstr ""

#: lib/wordfenceClass.php:5125
msgid "Invalid email address given."
msgstr ""

#: lib/wordfenceClass.php:5162
#: lib/wordfenceClass.php:5283
msgid "Deleting an infected wp-config.php file must be done outside of Wordfence. The wp-config.php file contains your database credentials, which you will need to restore normal site operations. Your site will NOT function once the wp-config.php file has been deleted."
msgstr ""

#. translators: 1. File path. 2. Error message.
#: lib/wordfenceClass.php:5171
msgid "Could not delete file %1$s. Error was: %2$s"
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:5192
msgid "We could not retrieve the original file of %s to do a repair."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:5197
msgid "An invalid file %s was specified for repair."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:5205
msgid "You don't have permission to repair %s. You need to either fix the file manually using FTP or change the file permissions and ownership so that your web server has write access to repair the file."
msgstr ""

#. translators: 1. File path. 2. Error message.
#: lib/wordfenceClass.php:5208
msgid "We could not write to %1$s. The error was: %2$s"
msgstr ""

#. translators: 1. File path. 2. Number of bytes.
#: lib/wordfenceClass.php:5219
msgid "We could not write to %1$s. (%2$d bytes written) You may not have permission to modify files on your WordPress server."
msgstr ""

#: lib/wordfenceClass.php:5231
msgid "Deleted some files with errors"
msgstr ""

#: lib/wordfenceClass.php:5231
msgid "Repaired some files with errors"
msgstr ""

#. translators: 1. Number of files. 2. Error message.
#: lib/wordfenceClass.php:5234
msgid "Deleted %1$d files but we encountered the following errors with other files: %2$s"
msgstr ""

#. translators: 1. Number of files. 2. Error message.
#: lib/wordfenceClass.php:5236
msgid "Repaired %1$d files but we encountered the following errors with other files: %2$s"
msgstr ""

#. translators: Number of files.
#: lib/wordfenceClass.php:5240
msgid "Deleted %d files successfully"
msgstr ""

#. translators: Number of files.
#: lib/wordfenceClass.php:5240
msgid "Repaired %d files successfully"
msgstr ""

#. translators: Number of files.
#: lib/wordfenceClass.php:5241
msgid "Deleted %d files successfully. No errors were encountered."
msgstr ""

#. translators: Number of files.
#: lib/wordfenceClass.php:5241
msgid "Repaired %d files successfully. No errors were encountered."
msgstr ""

#: lib/wordfenceClass.php:5244
msgid "Could not delete files"
msgstr ""

#: lib/wordfenceClass.php:5244
msgid "Could not repair files"
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:5247
msgid "We could not delete any of the files you selected. We encountered the following errors: %s"
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:5249
msgid "We could not repair any of the files you selected. We encountered the following errors: %s"
msgstr ""

#: lib/wordfenceClass.php:5252
msgid "Nothing done"
msgstr ""

#: lib/wordfenceClass.php:5253
msgid "We didn't delete anything and no errors were found."
msgstr ""

#: lib/wordfenceClass.php:5253
msgid "We didn't repair anything and no errors were found."
msgstr ""

#: lib/wordfenceClass.php:5261
msgid "Invalid bulk operation selected"
msgstr ""

#: lib/wordfenceClass.php:5271
msgid "Could not delete file because we could not find that issue."
msgstr ""

#: lib/wordfenceClass.php:5274
msgid "Could not delete file because that issue does not appear to be a file related issue."
msgstr ""

#: lib/wordfenceClass.php:5279
msgid "An invalid file was requested for deletion."
msgstr ""

#. translators: 1. File path. 2. Error message.
#: lib/wordfenceClass.php:5322
msgid "Could not delete file %1$s. The error was: %2$s"
msgstr ""

#: lib/wordfenceClass.php:5335
msgid "Could not remove the option because we could not find that issue."
msgstr ""

#: lib/wordfenceClass.php:5338
msgid "Could not remove the option because that issue does not appear to be a database related issue."
msgstr ""

#. translators: 1. WordPress option. 2. Error message.
#: lib/wordfenceClass.php:5351
msgid "Could not remove the option %1$s. The error was: %2$s"
msgstr ""

#: lib/wordfenceClass.php:5378
msgid "Modifying the .htaccess file did not resolve the issue, so the original .htaccess file was restored. You can fix this manually by setting <code>display_errors</code> to <code>Off</code> in your php.ini if your site is on a VPS or dedicated server that you control."
msgstr ""

#: lib/wordfenceClass.php:5420
msgid "We could not get the original file to do a repair."
msgstr ""

#: lib/wordfenceClass.php:5424
msgid "An invalid file was specified for repair."
msgstr ""

#: lib/wordfenceClass.php:5444
msgid "We could not write to that file. You may not have permission to modify files on your WordPress server."
msgstr ""

#: lib/wordfenceClass.php:5448
msgid "Ajax request received to start scan."
msgstr ""

#. translators: Number of URLs.
#: lib/wordfenceClass.php:5503
msgid "Page contains %d malware URL: "
msgid_plural "Page contains %d malware URLs: "
msgstr[0] ""
msgstr[1] ""

#: lib/wordfenceClass.php:5506
msgid "Run a Scan"
msgstr ""

#: lib/wordfenceClass.php:5554
msgid "Unknown dashboard data set."
msgstr ""

#: lib/wordfenceClass.php:5585
msgid "Bad security token. It may have been more than 12 hours since you reloaded the page you came from. Try reloading the page you came from. If that doesn't work, please sign out and sign-in again."
msgstr ""

#: lib/wordfenceClass.php:5766
msgid "An invalid IP address was specified."
msgstr ""

#: lib/wordfenceClass.php:5808
msgid "This link has expired. Refresh the scan results page and try again."
msgstr ""

#: lib/wordfenceClass.php:5832
#: lib/wordfenceClass.php:5875
#: lib/wordfenceClass.php:5924
msgid "File access blocked. (WORDFENCE_DISABLE_FILE_VIEWER is true)"
msgstr ""

#: lib/wordfenceClass.php:5839
#: lib/wordfenceClass.php:5931
msgid "Invalid file requested. (Relative paths not allowed)"
msgstr ""

#: lib/wordfenceClass.php:5843
#: lib/wordfenceClass.php:5879
#: lib/wordfenceClass.php:5935
msgid "File contains illegal characters."
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:5853
msgid "We could not open the requested file for reading. The error was: %s"
msgstr ""

#: lib/wordfenceClass.php:5861
msgid "Greater than 2 Gigs"
msgstr ""

#: lib/wordfenceClass.php:5866
msgid "Unknown file size."
msgstr ""

#: lib/wordfenceClass.php:5888
msgid "We could not get the contents of the original file to do a comparison."
msgstr ""

#: lib/wordfenceClass.php:5897
msgid "Empty file path provided"
msgstr ""

#: lib/wordfenceClass.php:5902
msgid "Unable to read file contents"
msgstr ""

#: lib/wordfenceClass.php:5939
msgid "File does not exist."
msgstr ""

#: lib/wordfenceClass.php:6269
#: modules/login-security/classes/controller/wordfencels.php:375
#: views/dashboard/options-group-import.php:167
msgid "Reload"
msgstr ""

#: lib/wordfenceClass.php:6286
msgid "${totalIPs} addresses in this network"
msgstr ""

#. translators: 1. Description of firewall action. 2. Description of input parameters.
#: lib/wordfenceClass.php:6287
msgid "%s in POST body: %s"
msgstr ""

#. translators: 1. Description of firewall action. 2. Description of input parameters.
#: lib/wordfenceClass.php:6288
msgid "%s in cookie: %s"
msgstr ""

#. translators: 1. Description of firewall action. 2. Description of input parameters.
#: lib/wordfenceClass.php:6289
msgid "%s in file: %s"
msgstr ""

#. translators: 1. Description of firewall action. 2. Description of input parameters.
#: lib/wordfenceClass.php:6290
msgid "%s in query string: %s"
msgstr ""

#. translators: Domain name.
#: lib/wordfenceClass.php:6291
msgid "%s is not valid hostname"
msgstr ""

#. translators: Domain name.
#: lib/wordfenceClass.php:6292
msgid ".htaccess Updated"
msgstr ""

#: lib/wordfenceClass.php:6293
msgid ".htaccess change"
msgstr ""

#: lib/wordfenceClass.php:6294
msgid "404 Not Found"
msgstr ""

#: lib/wordfenceClass.php:6295
msgid "Activity Log Sent"
msgstr ""

#: lib/wordfenceClass.php:6296
msgid "Add action to allowlist"
msgstr ""

#: lib/wordfenceClass.php:6297
msgid "Add code to .htaccess"
msgstr ""

#: lib/wordfenceClass.php:6298
msgid "All Hits"
msgstr ""

#. translators: WordPress username.
#: lib/wordfenceClass.php:6299
msgid "All capabilties of admin user %s were successfully revoked."
msgstr ""

#: lib/wordfenceClass.php:6301
msgid "An error occurred when adding the request to the allowlist."
msgstr ""

#: lib/wordfenceClass.php:6302
msgid "Are you sure you want to allowlist this action?"
msgstr ""

#: lib/wordfenceClass.php:6303
msgid "Authentication Code"
msgstr ""

#: lib/wordfenceClass.php:6304
msgid "Background Request Blocked"
msgstr ""

#: lib/wordfenceClass.php:6305
msgid "Block This Network"
msgstr ""

#: lib/wordfenceClass.php:6307
msgid "Blocked By Firewall"
msgstr ""

#: lib/wordfenceClass.php:6308
msgid "Blocked WAF"
msgstr ""

#: lib/wordfenceClass.php:6309
msgid "Blocked by Wordfence"
msgstr ""

#: lib/wordfenceClass.php:6310
msgid "Blocked by Wordfence plugin settings"
msgstr ""

#: lib/wordfenceClass.php:6311
msgid "Blocked by the Wordfence Application Firewall and plugin settings"
msgstr ""

#: lib/wordfenceClass.php:6312
msgid "Blocked by the Wordfence Security Network"
msgstr ""

#: lib/wordfenceClass.php:6313
msgid "Blocked by the Wordfence Web Application Firewall"
msgstr ""

#: lib/wordfenceClass.php:6316
msgid "Cellphone Sign-In Recovery Codes"
msgstr ""

#: lib/wordfenceClass.php:6317
msgid "Cellphone Sign-in activated for user."
msgstr ""

#: lib/wordfenceClass.php:6318
msgid "Click here to download a backup copy of this file now"
msgstr ""

#: lib/wordfenceClass.php:6319
msgid "Click here to download a backup copy of your .htaccess file now"
msgstr ""

#: lib/wordfenceClass.php:6320
msgid "Click to fix .htaccess"
msgstr ""

#: lib/wordfenceClass.php:6322
msgid "Crawlers"
msgstr ""

#: lib/wordfenceClass.php:6323
msgid "Diagnostic report has been sent successfully."
msgstr ""

#: lib/wordfenceClass.php:6324
msgid "Directory Listing Disabled"
msgstr ""

#: lib/wordfenceClass.php:6325
msgid "Directory listing has been disabled on your server."
msgstr ""

#: lib/wordfenceClass.php:6328
msgid "Don't ask again"
msgstr ""

#: lib/wordfenceClass.php:6330
msgid "Download Backup File"
msgstr ""

#: lib/wordfenceClass.php:6331
msgid "Each line of 16 letters and numbers is a single recovery code, with optional spaces for readability. When typing your password, enter \"wf\" followed by the entire code like \"mypassword wf1234 5678 90AB CDEF\". If your site shows a separate prompt for entering a code after entering only your username and password, enter only the code like \"1234 5678 90AB CDEF\". Your recovery codes are:"
msgstr ""

#: lib/wordfenceClass.php:6332
msgid "Email Diagnostic Report"
msgstr ""

#: lib/wordfenceClass.php:6333
msgid "Email Wordfence Activity Log"
msgstr ""

#: lib/wordfenceClass.php:6335
msgid "Enter the email address you would like to send the Wordfence activity log to. Note that the activity log may contain thousands of lines of data. This log is usually only sent to a member of the Wordfence support team. It also contains your PHP configuration from the phpinfo() function for diagnostic data."
msgstr ""

#: lib/wordfenceClass.php:6336
#: views/offboarding/deactivation-prompt.php:65
#: views/onboarding/banner.php:33
msgid "Error"
msgstr ""

#: lib/wordfenceClass.php:6337
msgid "Error Enabling All Options Page"
msgstr ""

#: lib/wordfenceClass.php:6338
msgid "Error Restoring Defaults"
msgstr ""

#: lib/wordfenceClass.php:6339
msgid "Error Saving Option"
msgstr ""

#: lib/wordfenceClass.php:6340
msgid "Error Saving Options"
msgstr ""

#: lib/wordfenceClass.php:6341
msgid "Failed Login"
msgstr ""

#: lib/wordfenceClass.php:6342
msgid "Failed Login: Invalid Username"
msgstr ""

#: lib/wordfenceClass.php:6343
msgid "Failed Login: Valid Username"
msgstr ""

#: lib/wordfenceClass.php:6344
msgid "File hidden successfully"
msgstr ""

#: lib/wordfenceClass.php:6345
msgid "File restored OK"
msgstr ""

#: lib/wordfenceClass.php:6346
msgid "Filter Traffic"
msgstr ""

#: lib/wordfenceClass.php:6347
msgid "Firewall Response"
msgstr ""

#: lib/wordfenceClass.php:6348
#: views/scanner/issue-wpscan_fullPathDiscl.php:8
msgid "Full Path Disclosure"
msgstr ""

#: lib/wordfenceClass.php:6349
#: views/onboarding/registration-prompt.php:61
msgid "Get a new license"
msgstr ""

#: lib/wordfenceClass.php:6350
msgid "Google Bot"
msgstr ""

#: lib/wordfenceClass.php:6351
msgid "Google Crawlers"
msgstr ""

#: lib/wordfenceClass.php:6352
msgid "HTTP Response Code"
msgstr ""

#: lib/wordfenceClass.php:6354
msgid "Humans"
msgstr ""

#: lib/wordfenceClass.php:6356
msgid "Key:"
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:6357
msgid "Last Updated: %s"
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:6358
msgid "Learn more about repairing modified files."
msgstr ""

#: lib/wordfenceClass.php:6360
#: modules/login-security/classes/controller/wordfencels.php:490
#: modules/login-security/classes/controller/wordfencels.php:869
#: modules/login-security/views/manage/grace-period.php:22
msgid "Locked Out"
msgstr ""

#: lib/wordfenceClass.php:6361
msgid "Locked out from logging in"
msgstr ""

#: lib/wordfenceClass.php:6362
msgid "Logged In"
msgstr ""

#: lib/wordfenceClass.php:6363
msgid "Logins"
msgstr ""

#: lib/wordfenceClass.php:6364
msgid "Logins and Logouts"
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:6367
msgid "Next Update Check: %s"
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:6368
msgid "No activity to report yet. Please complete your first scan."
msgstr ""

#: lib/wordfenceClass.php:6369
msgid "No issues have been ignored."
msgstr ""

#: lib/wordfenceClass.php:6370
msgid "No new issues have been found."
msgstr ""

#: lib/wordfenceClass.php:6371
msgid "No rules were updated. Please verify you have permissions to write to the /wp-content/wflogs directory."
msgstr ""

#: lib/wordfenceClass.php:6372
msgid "No rules were updated. Please verify your website can reach the Wordfence servers."
msgstr ""

#: lib/wordfenceClass.php:6373
msgid "No rules were updated. Your website has reached the maximum number of rule update requests. Please try again later."
msgstr ""

#: lib/wordfenceClass.php:6374
msgid "Note: Status will update when changes are saved"
msgstr ""

#: lib/wordfenceClass.php:6375
msgid "OK"
msgstr ""

#: lib/wordfenceClass.php:6376
msgid "Pages Not Found"
msgstr ""

#: lib/wordfenceClass.php:6377
msgid "Paid Members Only"
msgstr ""

#: lib/wordfenceClass.php:6379
msgid "Please enter a valid email address."
msgstr ""

#: lib/wordfenceClass.php:6380
msgid "Please include your support ticket number or forum username."
msgstr ""

#: lib/wordfenceClass.php:6381
msgid "Please make a backup of this file before proceeding. If you need to restore this backup file, you can copy it to the following path from your site's root:"
msgstr ""

#: lib/wordfenceClass.php:6382
msgid "Please specify a reason"
msgstr ""

#: lib/wordfenceClass.php:6383
msgid "Please specify a valid IP address range in the form of \"******* - *******\" without quotes. Make sure the dash between the IP addresses in a normal dash (a minus sign on your keyboard) and not another character that looks like a dash."
msgstr ""

#: lib/wordfenceClass.php:6384
msgid "Please specify either an IP address range, Hostname or a web browser pattern to match."
msgstr ""

#: lib/wordfenceClass.php:6385
msgid "Recent Activity"
msgstr ""

#: lib/wordfenceClass.php:6386
#: modules/login-security/views/manage/regenerate.php:12
msgid "Recovery Codes"
msgstr ""

#: lib/wordfenceClass.php:6387
msgid "Redirected"
msgstr ""

#: lib/wordfenceClass.php:6388
msgid "Redirected by Country Blocking bypass URL"
msgstr ""

#: lib/wordfenceClass.php:6389
msgid "Referer"
msgstr ""

#: lib/wordfenceClass.php:6390
msgid "Registered Users"
msgstr ""

#: lib/wordfenceClass.php:6392
msgid "Rule Update Failed"
msgstr ""

#: lib/wordfenceClass.php:6393
msgid "Rules Updated"
msgstr ""

#: lib/wordfenceClass.php:6395
msgid "Scan Complete."
msgstr ""

#: lib/wordfenceClass.php:6396
#: modules/login-security/views/manage/code.php:16
msgid "Scan the code below with your authenticator app to add this account. Some authenticator apps also allow you to type in the text version instead."
msgstr ""

#: lib/wordfenceClass.php:6397
msgid "Security Event"
msgstr ""

#: lib/wordfenceClass.php:6398
#: modules/login-security/classes/controller/wordfencels.php:341
msgid "Send"
msgstr ""

#: lib/wordfenceClass.php:6399
msgid "Sorry, but no data for that IP or domain was found."
msgstr ""

#: lib/wordfenceClass.php:6400
msgid "Specify a valid IP range"
msgstr ""

#: lib/wordfenceClass.php:6401
msgid "Specify a valid hostname"
msgstr ""

#: lib/wordfenceClass.php:6402
msgid "Specify an IP range, Hostname or Browser pattern"
msgstr ""

#: lib/wordfenceClass.php:6403
msgid "Success deleting file"
msgstr ""

#: lib/wordfenceClass.php:6404
msgid "Success removing option"
msgstr ""

#: lib/wordfenceClass.php:6405
msgid "Success restoring file"
msgstr ""

#: lib/wordfenceClass.php:6406
msgid "Success updating option"
msgstr ""

#: lib/wordfenceClass.php:6407
msgid "Successfully deleted admin"
msgstr ""

#: lib/wordfenceClass.php:6408
msgid "Successfully revoked admin"
msgstr ""

#: lib/wordfenceClass.php:6409
msgid "Test Email Sent"
msgstr ""

#: lib/wordfenceClass.php:6410
msgid "The 'How does Wordfence get IPs' option was successfully updated to the recommended value."
msgstr ""

#: lib/wordfenceClass.php:6411
msgid "The Full Path disclosure issue has been fixed"
msgstr ""

#. translators: WordPress username.
#: lib/wordfenceClass.php:6412
msgid "The admin user %s was successfully deleted."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:6413
msgid "The file %s was successfully deleted."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:6414
msgid "The file %s was successfully hidden from public view."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:6415
msgid "The file %s was successfully restored."
msgstr ""

#. translators: WordPress option.
#: lib/wordfenceClass.php:6416
msgid "The option %s was successfully removed."
msgstr ""

#. translators: WordPress option.
#: lib/wordfenceClass.php:6417
msgid "The request has been allowlisted. Please try it again."
msgstr ""

#: lib/wordfenceClass.php:6418
msgid "There was an error while downgrading to a free license."
msgstr ""

#: lib/wordfenceClass.php:6419
msgid "There was an error while sending the email."
msgstr ""

#: lib/wordfenceClass.php:6420
msgid "This will be shown only once. Keep these codes somewhere safe."
msgstr ""

#: lib/wordfenceClass.php:6421
msgid "Throttled"
msgstr ""

#: lib/wordfenceClass.php:6422
msgid "Two Factor Status"
msgstr ""

#. translators: HTTP client type.
#: lib/wordfenceClass.php:6424
msgid "Type: %s"
msgstr ""

#. translators: HTTP client type.
#: lib/wordfenceClass.php:6425
#: views/scanner/issue-checkGSB.php:8
#: views/scanner/issue-commentBadURL.php:8
#: views/scanner/issue-configReadable.php:12
#: views/scanner/issue-configReadable.php:23
#: views/scanner/issue-optionBadURL.php:8
#: views/scanner/issue-postBadURL.php:8
#: views/scanner/issue-publiclyAccessible.php:12
#: views/scanner/issue-publiclyAccessible.php:23
#: views/scanner/issue-wpscan_directoryList.php:12
#: views/scanner/issue-wpscan_directoryList.php:23
#: views/scanner/issue-wpscan_fullPathDiscl.php:12
#: views/scanner/issue-wpscan_fullPathDiscl.php:23
#: views/waf/option-whitelist.php:9
#: views/waf/option-whitelist.php:106
#: views/waf/options-group-whitelisted.php:82
#: views/waf/options-group-whitelisted.php:95
msgid "URL"
msgstr ""

#: lib/wordfenceClass.php:6426
msgid "Unable to automatically hide file"
msgstr ""

#. translators: 2FA backup codes.
#: lib/wordfenceClass.php:6427
msgid "Use one of these %s codes to log in if you are unable to access your phone. Codes are 16 characters long, plus optional spaces. Each one may be used only once."
msgstr ""

#. translators: 2FA backup codes.
#: lib/wordfenceClass.php:6428
msgid "Use one of these %s codes to log in if you lose access to your authenticator device. Codes are 16 characters long, plus optional spaces. Each one may be used only once."
msgstr ""

#: lib/wordfenceClass.php:6430
msgid "User ID"
msgstr ""

#: lib/wordfenceClass.php:6432
msgid "WHOIS LOOKUP"
msgstr ""

#: lib/wordfenceClass.php:6433
msgid "We are about to change your <em>.htaccess</em> file. Please make a backup of this file before proceeding."
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:6434
msgid "We can't modify your .htaccess file for you because: %s"
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:6435
msgid "We encountered a problem"
msgstr ""

#. translators: URL.
#: lib/wordfenceClass.php:6436
msgid "Wordfence Firewall blocked a background request to WordPress for the URL %s. If this occurred as a result of an intentional action, you may consider allowlisting the request to allow it in the future."
msgstr ""

#. translators: URL.
#: lib/wordfenceClass.php:6437
msgid "Wordfence is working..."
msgstr ""

#: lib/wordfenceClass.php:6438
msgid "You are using Nginx as your web server. You'll need to disable autoindexing in your nginx.conf. See the <a target='_blank'  rel='noopener noreferrer' href='https://nginx.org/en/docs/http/ngx_http_autoindex_module.html'>Nginx docs for more info</a> on how to do this."
msgstr ""

#: lib/wordfenceClass.php:6439
msgid "You are using an Nginx web server and using a FastCGI processor like PHP5-FPM. You will need to manually delete or hide those files."
msgstr ""

#: lib/wordfenceClass.php:6440
msgid "You are using an Nginx web server and using a FastCGI processor like PHP5-FPM. You will need to manually modify your php.ini to disable <em>display_error</em>"
msgstr ""

#: lib/wordfenceClass.php:6441
msgid "You forgot to include a reason you're blocking this IP range. We ask you to include this for your own record keeping."
msgstr ""

#: lib/wordfenceClass.php:6442
#: modules/login-security/classes/controller/wordfencels.php:400
msgid "You have unsaved changes to your options. If you leave this page, those changes will be lost."
msgstr ""

#: lib/wordfenceClass.php:6443
msgid "You may close this alert and try again later, or click the button below to register for a new free Wordfence license."
msgstr ""

#: lib/wordfenceClass.php:6444
msgid "Your .htaccess has been updated successfully. Please verify your site is functioning normally."
msgstr ""

#. translators: Email address.
#: lib/wordfenceClass.php:6445
msgid "Your Wordfence activity log was sent to %s"
msgstr ""

#. translators: Email address.
#: lib/wordfenceClass.php:6446
msgid "Your rules have been updated successfully."
msgstr ""

#: lib/wordfenceClass.php:6447
msgid "Your rules have been updated successfully. You are currently using the free version of Wordfence. Upgrade to Wordfence premium to have your rules updated automatically as new threats emerge. <a href=\"https://www.wordfence.com/wafUpdateRules1/wordfence-signup/\">Click here to purchase a premium license</a>. <em>Note: Your rules will still update every 30 days as a free user.</em>"
msgstr ""

#. translators: wp_mail() return value.
#: lib/wordfenceClass.php:6448
msgid "Your test email was sent to the requested email address. The result we received from the WordPress wp_mail() function was: %s<br /><br />A 'True' result means WordPress thinks the mail was sent without errors. A 'False' result means that WordPress encountered an error sending your mail. Note that it's possible to get a 'True' response with an error elsewhere in your mail system that may cause emails to not be delivered."
msgstr ""

#. translators: wp_mail() return value.
#: lib/wordfenceClass.php:6449
msgid "blocked by firewall"
msgstr ""

#. translators: Reason for firewall action.
#: lib/wordfenceClass.php:6450
msgid "blocked by firewall for %s"
msgstr ""

#. translators: Reason for firewall action.
#: lib/wordfenceClass.php:6451
msgid "blocked by real-time IP blocklist"
msgstr ""

#: lib/wordfenceClass.php:6452
msgid "blocked by the Wordfence Security Network"
msgstr ""

#. translators: Reason for firewall action.
#: lib/wordfenceClass.php:6453
msgid "blocked for %s"
msgstr ""

#. translators: Reason for firewall action.
#: lib/wordfenceClass.php:6454
msgid "locked out from logging in"
msgstr ""

#: lib/wordfenceClass.php:6467
msgid "Wordfence generated an error on activation. The output we received during activation was:"
msgstr ""

#: lib/wordfenceClass.php:6474
msgid "Wordfence's license key is missing."
msgstr ""

#: lib/wordfenceClass.php:6475
msgid "This could be caused by a database problem. You may need to repair your \"wfconfig\" database table or fix your database user's privileges if they have changed recently, or you may need to reinstall Wordfence. Please <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">contact Wordfence support<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wordfenceClass.php:6489
msgid "The Wordfence Web Application Firewall cannot run."
msgstr ""

#. translators: 1. WordPress admin panel URL. 2. Support URL.
#: lib/wordfenceClass.php:6492
msgid "The configuration files are corrupt or inaccessible by the web server, which is preventing the WAF from functioning. Please verify the web server has permission to access the configuration files. You may also try to rebuild the configuration file by <a href=\"%1$s\">clicking here</a>. It will automatically resume normal operation when it is fixed. <a class=\"wfhelp\" target=\"_blank\" rel=\"noopener noreferrer\" href=\"%2$s\"><span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wordfenceClass.php:6498
msgid "The WAF storage engine is currently set to mysqli, but Wordfence is unable to use the database. The WAF will fall back to using local file system storage instead."
msgstr ""

#: lib/wordfenceClass.php:6511
#: lib/wordfenceClass.php:6530
msgid "The Wordfence Web Application Firewall needs a configuration update."
msgstr ""

#. translators: 1. WordPress admin panel URL. 2. Support URL.
#: lib/wordfenceClass.php:6514
msgid "It is currently configured to use an older version of PHP and may become deactivated if PHP is updated. You may perform the configuration update automatically by <a href=\"%1$s\">clicking here</a>. <a class=\"wfhelp\" target=\"_blank\" rel=\"noopener noreferrer\" href=\"%2$s\"><span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: 1. WordPress admin panel URL. 2. Support URL.
#: lib/wordfenceClass.php:6533
msgid "It is not currently in extended protection mode but was configured to use an older version of PHP and may have become deactivated when PHP was updated. You may perform the configuration update automatically by <a href=\"%1$s\">clicking here</a> or use the \"Optimize the Wordfence Firewall\" button on the Firewall Options page. <a class=\"wfhelp\" target=\"_blank\" rel=\"noopener noreferrer\" href=\"%2$s\"><span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wordfenceClass.php:6539
msgid "The Wordfence Web Application Firewall is in read-only mode."
msgstr ""

#: lib/wordfenceClass.php:6548
msgid "This site is currently using PHP's built in REMOTE_ADDR."
msgstr ""

#: lib/wordfenceClass.php:6551
msgid "This site is currently using the X-Forwarded-For HTTP header, which should only be used when the site is behind a front-end proxy that outputs this header."
msgstr ""

#: lib/wordfenceClass.php:6554
msgid "This site is currently using the X-Real-IP HTTP header, which should only be used when the site is behind a front-end proxy that outputs this header."
msgstr ""

#: lib/wordfenceClass.php:6557
msgid "This site is currently using the Cloudflare \"CF-Connecting-IP\" HTTP header, which should only be used when the site is behind Cloudflare."
msgstr ""

#: lib/wordfenceClass.php:6574
msgid "Your 'How does Wordfence get IPs' setting is misconfigured."
msgstr ""

#: lib/wordfenceClass.php:6576
msgid "Click here to use the recommended setting"
msgstr ""

#: lib/wordfenceClass.php:6578
msgid "or"
msgstr ""

#: lib/wordfenceClass.php:6580
msgid "visit the options page"
msgstr ""

#: lib/wordfenceClass.php:6582
msgid "to manually update it."
msgstr ""

#: lib/wordfenceClass.php:6590
msgid "Do you want Wordfence to stay up-to-date automatically?"
msgstr ""

#: lib/wordfenceClass.php:6592
msgid "Yes, enable auto-update."
msgstr ""

#: lib/wordfenceClass.php:6594
msgid "No thanks."
msgstr ""

#: lib/wordfenceClass.php:6618
msgid "Wordfence country blocking is currently set to block the United States. We recommend allowing access from the United States for Google and other benign crawlers, unless you choose to only block the login page."
msgstr ""

#. translators: Localized date.
#: lib/wordfenceClass.php:6736
msgid "The last rules update for the Wordfence Web Application Firewall was unsuccessful. The last successful update check was %s, so this site may be missing new rules added since then."
msgstr ""

#. translators: 1. Localized date. 2. WordPress admin panel URL.
#: lib/wordfenceClass.php:6744
msgid "You may wait for the next automatic attempt at %1$s or try to <a href=\"%2$s\">Manually Update</a> by clicking the \"Manually Refresh Rules\" button below the Rules list."
msgstr ""

#. translators: WordPress admin panel URL.
#: lib/wordfenceClass.php:6750
msgid "You may wait for the next automatic attempt or try to <a href=\"%s\">Manually Update</a> by clicking the \"Manually Refresh Rules\" button below the Rules list."
msgstr ""

#. translators: WordPress admin panel URL.
#: lib/wordfenceClass.php:6755
msgid "You may wait for the next automatic attempt at %s or log into the parent site to manually update by clicking the \"Manually Refresh Rules\" button below the Rules list."
msgstr ""

#: lib/wordfenceClass.php:6758
msgid "You may wait for the next automatic attempt or log into the parent site to manually update by clicking the \"Manually Refresh Rules\" button below the Rules list."
msgstr ""

#: lib/wordfenceClass.php:6885
#: models/page/wfPage.php:101
msgid "Dashboard"
msgstr ""

#: lib/wordfenceClass.php:6927
#: views/onboarding/modal-final-attempt.php:24
msgid "Install"
msgstr ""

#: lib/wordfenceClass.php:6938
msgid "Upgrade to Care"
msgstr ""

#: lib/wordfenceClass.php:6942
msgid "Upgrade to Response"
msgstr ""

#. translators: Number of notifications.
#: lib/wordfenceClass.php:6991
msgid "You have %d new Wordfence notification."
msgid_plural "You have %d new Wordfence notifications."
msgstr[0] ""
msgstr[1] ""

#: lib/wordfenceClass.php:7015
msgid "JavaScript Errors"
msgstr ""

#: lib/wordfenceClass.php:7021
msgid "Malware URLs"
msgstr ""

#. translators: WordPress admin panel URL.
#: lib/wordfenceClass.php:7150
#: lib/wordfenceClass.php:7213
msgid "<a href=\"%s\">Click here</a> to rebuild the configuration file."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:7161
#: lib/wordfenceClass.php:7224
msgid "We were unable to write to %s which the WAF uses for storage. Please update permissions on the parent directory so the web server can write to it."
msgstr ""

#: lib/wordfenceClass.php:7171
#: lib/wordfenceClass.php:7234
msgid "An error occured when fetching the WAF configuration from the database."
msgstr ""

#. translators: Plugin name.
#: lib/wordfenceClass.php:7256
msgid "The Wordfence Live Traffic feature has been disabled because you have %s active which is not compatible with Wordfence Live Traffic."
msgstr ""

#. translators: 1. Plugin name.
#: lib/wordfenceClass.php:7258
msgid "If you want to reenable Wordfence Live Traffic, you need to deactivate %1$s and then go to the Wordfence options page and reenable Live Traffic there. Wordfence does work with %1$s, however Live Traffic will be disabled and the Wordfence firewall will also count less hits per visitor because of the %1$s caching function. All other functions should work correctly."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:7351
msgid "The file <code>%s</code> was restored successfully."
msgstr ""

#: lib/wordfenceClass.php:7356
msgid "There was an error restoring the file."
msgstr ""

#: lib/wordfenceClass.php:7366
#: lib/wordfenceClass.php:7389
msgid "Return to scan results"
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:7375
msgid "The file <code>%s</code> was deleted successfully."
msgstr ""

#: lib/wordfenceClass.php:7379
msgid "There was an error deleting the file."
msgstr ""

#. translators: IP address.
#: lib/wordfenceClass.php:7449
msgid ""
"User IP: %s\n"
""
msgstr ""

#. translators: Domain name.
#: lib/wordfenceClass.php:7452
msgid ""
"User hostname: %s\n"
""
msgstr ""

#: lib/wordfenceClass.php:7456
msgid "User location: "
msgstr ""

#. translators: WordPress admin panel URL.
#: lib/wordfenceClass.php:7511
msgid "No longer an administrator for this site? Click here to stop receiving security alerts: %s"
msgstr ""

#: lib/wordfenceClass.php:7552
msgid "The IP you provided must be in dotted quad notation or use ranges with square brackets. e.g. *********** or 10.11.12.[1-50]"
msgstr ""

#: lib/wordfenceClass.php:7571
msgid "Invalid email address provided"
msgstr ""

#: lib/wordfenceClass.php:7576
msgid "Test email sent successfully"
msgstr ""

#: lib/wordfenceClass.php:7577
msgid "Test email failed to send"
msgstr ""

#. translators: Localized date range.
#: lib/wordfenceClass.php:7595
msgid "Wordfence activity in the past %s"
msgstr ""

#: lib/wordfenceClass.php:7635
#: lib/wordfenceClass.php:7639
#: lib/wordfenceClass.php:7670
#: lib/wordfenceClass.php:7697
#: lib/wordfenceClass.php:7701
msgid "We could not find that user in the database."
msgstr ""

#: lib/wordfenceClass.php:7643
msgid "This user's email is the network admin email. It will need to be changed before deleting this user."
msgstr ""

#: lib/wordfenceClass.php:7693
msgid "We could not find that issue in the database."
msgstr ""

#: lib/wordfenceClass.php:7736
msgid "Wordfence could not find your .htaccess file."
msgstr ""

#: lib/wordfenceClass.php:7752
msgid "Updating the .htaccess did not fix the issue. You may need to add <code>Options -Indexes</code> to your httpd.conf if using Apache, or find documentation on how to disable directory listing for your web server."
msgstr ""

#: lib/wordfenceClass.php:7758
msgid "There was an error writing to your .htaccess file."
msgstr ""

#: lib/wordfenceClass.php:7837
msgid "Required parameters not sent."
msgstr ""

#: lib/wordfenceClass.php:8033
msgid "Allowlisted via Live Traffic"
msgstr ""

#: lib/wordfenceClass.php:8134
#: lib/wordfenceClass.php:8241
msgid "A valid server configuration was not provided."
msgstr ""

#: lib/wordfenceClass.php:8145
#: lib/wordfenceClass.php:8262
msgid "Filesystem Credentials Required"
msgstr ""

#. translators: Support URL.
#: lib/wordfenceClass.php:8147
#: lib/wordfenceClass.php:8171
#: lib/wordfenceClass.php:8225
msgid "If you cannot complete the setup process, <a target=\"_blank\" rel=\"noopener noreferrer\" href=\"%s\">click here for help<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#. translators: Support URL.
#: lib/wordfenceClass.php:8148
msgid "Once you have entered credentials, click Continue to complete the setup."
msgstr ""

#: lib/wordfenceClass.php:8169
#: lib/wordfenceClass.php:8287
msgid "Filesystem Permission Error"
msgstr ""

#: lib/wordfenceClass.php:8195
msgid "Manual Installation Instructions"
msgstr ""

#: lib/wordfenceClass.php:8202
msgid "Installation Successful"
msgstr ""

#: lib/wordfenceClass.php:8223
msgid "Installation Failed"
msgstr ""

#. translators: Support URL.
#: lib/wordfenceClass.php:8264
#: lib/wordfenceClass.php:8289
#: lib/wordfenceClass.php:8320
#: lib/wordfenceClass.php:8360
#: lib/wordfenceClass.php:8407
#: views/waf/waf-uninstall.php:13
msgid "If you cannot complete the uninstall process, <a target=\"_blank\" rel=\"noopener noreferrer\" href=\"%s\">click here for help<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#. translators: Support URL.
#: lib/wordfenceClass.php:8265
msgid "Once you have entered credentials, click Continue to complete uninstallation."
msgstr ""

#: lib/wordfenceClass.php:8309
msgid "The <code>auto_prepend_file</code> setting has been successfully removed from <code>.htaccess</code> and <code>.user.ini</code>. Once this change takes effect, Extended Protection Mode will be disabled."
msgstr ""

#: lib/wordfenceClass.php:8311
msgid "Any previous value for <code>auto_prepend_file</code> will need to be re-enabled manually if still needed."
msgstr ""

#. translators: Time until.
#: lib/wordfenceClass.php:8315
msgid "Waiting for it to take effect. This may take up to %s."
msgstr ""

#: lib/wordfenceClass.php:8318
msgid "Waiting for Changes"
msgstr ""

#: lib/wordfenceClass.php:8351
msgid "Extended Protection Mode has not been disabled. This may be because <code>auto_prepend_file</code> is configured somewhere else or the value is still cached by PHP."
msgstr ""

#: lib/wordfenceClass.php:8353
msgid "Retrying Failed."
msgstr ""

#: lib/wordfenceClass.php:8355
#: modules/login-security/views/settings/user-stats.php:75
msgid "Try Again"
msgstr ""

#: lib/wordfenceClass.php:8358
msgid "Unable to Uninstall"
msgstr ""

#: lib/wordfenceClass.php:8394
msgid "Uninstallation Complete"
msgstr ""

#: lib/wordfenceClass.php:8405
msgid "Uninstallation Failed"
msgstr ""

#. translators: 1. Number of attacks/blocks. 2. Time since.
#: lib/wordfenceClass.php:8625
msgid "The Wordfence Web Application Firewall has blocked %1$d attacks over the last %2$s."
msgstr ""

#: lib/wordfenceClass.php:8630
msgid "Wordfence is blocking these attacks, and we're sending this notice to make you aware that there is a higher volume of the attacks than usual. Additionally, the Wordfence Real-Time IP Blocklist can block known attackers' IP addresses automatically for Premium users, including any probing requests that may not be malicious on their own. All Wordfence users can also opt to block the attacking IPs manually if desired. As always, be sure to watch your scan results and keep your plugins, themes and WordPress core version updated."
msgstr ""

#: lib/wordfenceClass.php:8632
msgid "Below is a sample of these recent attacks:"
msgstr ""

#: lib/wordfenceClass.php:9254
msgid "An error was detected with this site's configuration that is preventing a successful connection to Wordfence Central. Disconnecting from Central <a href=\"%s\">on the Wordfence Dashboard</a> and reconnecting may resolve it. If the issue persists, please contact Wordfence support."
msgstr ""

#: lib/wordfenceClass.php:9261
msgid "To make your site as secure as possible, take a moment to optimize the Wordfence Web Application Firewall:"
msgstr ""

#: lib/wordfenceClass.php:9261
msgid "Click here to configure"
msgstr ""

#. translators: Support URL.
#: lib/wordfenceClass.php:9264
msgid "If you cannot complete the setup process, <a target=\"_blank\" rel=\"noopener noreferrer\" href=\"%s\">click here for help<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: lib/wordfenceClass.php:9270
#: views/waf/waf-install-success.php:14
msgid "Nice work! The firewall is now optimized."
msgstr ""

#: lib/wordfenceClass.php:9272
#: lib/wordfenceClass.php:9284
#: views/waf/waf-install-success.php:16
#: views/waf/waf-uninstall-success.php:19
msgid "The changes have not yet taken effect. If you are using LiteSpeed or IIS as your web server or CGI/FastCGI interface, you may need to wait a few minutes for the changes to take effect since the configuration files are sometimes cached. You also may need to select a different server configuration in order to complete this step, but wait for a few minutes before trying. You can try refreshing this page."
msgstr ""

#: lib/wordfenceClass.php:9278
#: views/waf/waf-uninstall-success.php:15
msgid "Uninstallation was successful!"
msgstr ""

#: lib/wordfenceClass.php:9281
#: views/waf/waf-uninstall-success.php:17
msgid "Uninstallation from this site was successful! The Wordfence Firewall is still active because it is installed in another WordPress installation."
msgstr ""

#: lib/wordfenceClass.php:9289
msgid "The update was successful!"
msgstr ""

#: lib/wordfenceClass.php:9436
msgid "Auth grant is invalid."
msgstr ""

#: lib/wordfenceClass.php:9454
#: lib/wordfenceClass.php:9543
msgid "Internal error when connecting to Wordfence Central (see server error log)"
msgstr ""

#. translators: Error message.
#: lib/wordfenceClass.php:9465
msgid "Invalid response from Wordfence Central: %s"
msgstr ""

#. translators: JSON property.
#: lib/wordfenceClass.php:9471
#: lib/wordfenceClass.php:9484
msgid "Invalid response from Wordfence Central. Parameter %s not found in response."
msgstr ""

#: lib/wordfenceClass.php:9513
#: lib/wordfenceClass.php:9694
msgid "Access token not found."
msgstr ""

#: lib/wordfenceClass.php:9572
#: lib/wordfenceClass.php:9654
msgid "Invalid response from Wordfence Central."
msgstr ""

#: lib/wordfenceClass.php:9630
msgid "Auth grant not found."
msgstr ""

#: lib/wordfenceClass.php:9736
msgid "The current site URL does not match the Wordfence Central connection information. Local connection information has been removed, but %s is still registered in Wordfence Central."
msgstr ""

#: lib/wordfenceClass.php:9742
msgid "Unable to communicate with Wordfence Central"
msgstr ""

#: lib/wordfenceClass.php:9960
msgid "We were unable to create the <code>wordfence-waf.php</code> file in the root of the WordPress installation. It's possible WordPress cannot write to the <code>wordfence-waf.php</code> file because of file permissions. Please verify the permissions are correct and retry the installation."
msgstr ""

#: lib/wordfenceClass.php:10052
#: lib/wordfenceClass.php:10123
msgid "We were unable to make changes to the .htaccess file. It's possible WordPress cannot write to the .htaccess file because of file permissions, which may have been set by another security plugin, or you may have set them manually. Please verify the permissions allow the web server to write to the file, and retry the installation."
msgstr ""

#. translators: File path.
#: lib/wordfenceClass.php:10093
#: lib/wordfenceClass.php:10141
msgid "We were unable to make changes to the %1$s file. It's possible WordPress cannot write to the %1$s file because of file permissions. Please verify the permissions are correct and retry the installation."
msgstr ""

#: lib/wordfenceClass.php:10157
msgid "We were unable to remove the <code>wordfence-waf.php</code> file in the root of the WordPress installation. It's possible WordPress cannot remove the <code>wordfence-waf.php</code> file because of file permissions. Please verify the permissions are correct and retry the removal."
msgstr ""

#: lib/wordfenceHash.php:82
msgid "Fetching core, theme and plugin file signatures from Wordfence"
msgstr ""

#: lib/wordfenceHash.php:91
msgid "Fetching list of known malware files from Wordfence"
msgstr ""

#: lib/wordfenceHash.php:95
msgid "Using cached malware prefixes"
msgstr ""

#: lib/wordfenceHash.php:98
msgid "Fetching fresh malware prefixes"
msgstr ""

#: lib/wordfenceHash.php:103
msgid "Could not fetch malware signatures from Wordfence servers."
msgstr ""

#: lib/wordfenceHash.php:108
msgid "Malware data received from Wordfence servers was not valid."
msgstr ""

#: lib/wordfenceHash.php:120
msgid "Fetching list of known core files from Wordfence"
msgstr ""

#: lib/wordfenceHash.php:124
msgid "Using cached core hashes"
msgstr ""

#: lib/wordfenceHash.php:127
msgid "Fetching fresh core hashes"
msgstr ""

#: lib/wordfenceHash.php:132
msgid "Could not fetch core hashes from Wordfence servers."
msgstr ""

#: lib/wordfenceHash.php:137
msgid "Core hashes data received from Wordfence servers was not valid."
msgstr ""

#: lib/wordfenceHash.php:155
msgid "Comparing core WordPress files against originals in repository"
msgstr ""

#: lib/wordfenceHash.php:155
msgid "Skipping core scan"
msgstr ""

#: lib/wordfenceHash.php:156
msgid "Comparing open source themes against WordPress.org originals"
msgstr ""

#: lib/wordfenceHash.php:156
msgid "Skipping theme scan"
msgstr ""

#: lib/wordfenceHash.php:157
msgid "Comparing plugins against WordPress.org originals"
msgstr ""

#: lib/wordfenceHash.php:157
msgid "Skipping plugin scan"
msgstr ""

#: lib/wordfenceHash.php:158
msgid "Scanning for known malware files"
msgstr ""

#: lib/wordfenceHash.php:158
msgid "Skipping malware scan"
msgstr ""

#: lib/wordfenceHash.php:159
msgid "Scanning for unknown files in wp-admin and wp-includes"
msgstr ""

#: lib/wordfenceHash.php:159
msgid "Skipping unknown core file scan"
msgstr ""

#. translators: WordPress version.
#: lib/wordfenceHash.php:172
msgid "Unknown WordPress core version: %s"
msgstr ""

#. translators: WordPress version.
#: lib/wordfenceHash.php:173
msgid "The core files scan will not be run because this version of WordPress is not currently indexed by Wordfence. This may be due to using a prerelease version or because the servers are still indexing a new release. If you are using an official WordPress release, this issue will automatically dismiss once the version is indexed and another scan is run."
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:212
msgid "Wordfence file scanner detected a possible infinite loop. Exiting on file: %s"
msgstr ""

#. translators: Time in seconds.
#: lib/wordfenceHash.php:226
msgid "Index time: %s"
msgstr ""

#: lib/wordfenceHash.php:231
msgid "Beginning file hashing"
msgstr ""

#: lib/wordfenceHash.php:240
msgid "Processing pending issues"
msgstr ""

#. translators: 1. Number of files. 2. Data in bytes.
#: lib/wordfenceHash.php:243
msgid "Analyzed %1$d files containing %2$s of data."
msgstr ""

#: lib/wordfenceHash.php:252
msgid "Invalid response from Wordfence API during check_possible_malware"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:265
msgid "This file is suspected malware: %s"
msgstr ""

#. translators: Malware name/title.
#: lib/wordfenceHash.php:266
msgid "This file's signature matches a known malware file. The title of the malware is '%s'. Immediately inspect this file using the 'View' option below and consider deleting it from your server."
msgstr ""

#: lib/wordfenceHash.php:308
msgid "Ignoring invalid scan file child: %s"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:318
#: lib/wordfenceHash.php:351
msgid "Found .suspected file: %s"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:327
#: lib/wordfenceHash.php:360
msgid "Skipping unneeded hash: %s"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:337
msgid "Skipping inaccessible directory: %s"
msgstr ""

#. translators: Number of files.
#: lib/wordfenceHash.php:388
msgid "%d files indexed"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:417
msgid "Forking during indexing: %s"
msgstr ""

#. translators: PHP max execution time.
#: lib/wordfenceHash.php:420
msgid "Calling fork() from wordfenceHash with maxExecTime: %s"
msgstr ""

#. translators: 1. File path. 2. Memory in bytes.
#: lib/wordfenceHash.php:456
msgid "Scanning: %1$s (Mem:%2$s)"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:461
msgid "Scanning: %s"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:499
msgid "WordPress core file modified: %s"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:500
msgid "This WordPress core file has been modified and differs from the original file distributed with this version of WordPress."
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:537
msgid "Modified plugin file: %s"
msgstr ""

#. translators: 1. Plugin name. 2. Plugin version. 3. Support URL.
#: lib/wordfenceHash.php:540
msgid "This file belongs to plugin \"%1$s\" version \"%2$s\" and has been modified from the file that is distributed by WordPress.org for this version. Please use the link to see how the file has changed. If you have modified this file yourself, you can safely ignore this warning. If you see a lot of changed files in a plugin that have been made by the author, then try uninstalling and reinstalling the plugin to force an upgrade. Doing this is a workaround for plugin authors who don't manage their code correctly. <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:584
msgid "Modified theme file: %s"
msgstr ""

#. translators: 1. Plugin name. 2. Plugin version. 3. Support URL.
#: lib/wordfenceHash.php:587
msgid "This file belongs to theme \"%1$s\" version \"%2$s\" and has been modified from the original distribution. It is common for site owners to modify their theme files, so if you have modified this file yourself you can safely ignore this warning. <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:621
msgid "Old WordPress core file not removed during update: %s"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:622
msgid "This file is in a WordPress core location but is from an older version of WordPress and not used with your current version. Hosting or permissions issues can cause these files to get left behind when WordPress is updated and they should be removed if possible."
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:639
#: lib/wordfenceHash.php:660
msgid "Unknown file in WordPress core: %s"
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:640
msgid "This file is in a WordPress core location but is not distributed with this version of WordPress. This scan often includes files left over from a previous WordPress version, but it may also find files added by another plugin, files added by your host, or malicious files added by an attacker."
msgstr ""

#. translators: Support URL.
#: lib/wordfenceHash.php:661
msgid "This file is in a WordPress core location but is not distributed with this version of WordPress. This scan often includes files left over from a previous WordPress version, but it may also find files added by another plugin, files added by your host, or malicious files added by an attacker. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgstr ""

#. translators: File path.
#: lib/wordfenceHash.php:735
msgid "Skipping file larger than max size: %s"
msgstr ""

#. translators: 1. Number of files. 2. Data in bytes.
#: lib/wordfenceHash.php:765
msgid "Analyzed %1$d files containing %2$s of data so far"
msgstr ""

#. translators: Number of scan results.
#: lib/wordfenceHash.php:894
msgid "(+ %d more)"
msgstr ""

#. translators: Number of files.
#: lib/wordfenceHash.php:895
msgid "%d more similar files were found."
msgstr ""

#. translators: Number of files.
#: lib/wordfenceHash.php:895
msgid "1 more similar file was found."
msgstr ""

#. translators: Number of files.
#: lib/wordfenceHash.php:895
msgid "<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> ("
msgstr ""

#: lib/wordfenceScanner.php:87
msgid "Wordfence could not get the attack signature patterns from the scanning server."
msgstr ""

#: lib/wordfenceScanner.php:96
msgid "Wordfence received malformed attack signature patterns from the scanning server."
msgstr ""

#: lib/wordfenceScanner.php:102
msgid "Regex compilation failed for signature %d"
msgstr ""

#. translators: PHP ini setting (number).
#: lib/wordfenceScanner.php:199
msgid "Backtrack limit is %d, reducing to 1000000"
msgstr ""

#: lib/wordfenceScanner.php:212
msgid "Detected loop in malware scan, aborting."
msgstr ""

#: lib/wordfenceScanner.php:219
msgid "No files remaining for malware scan."
msgstr ""

#. translators: File path.
#: lib/wordfenceScanner.php:284
msgid "Encountered file that is too large: %s - Skipping."
msgstr ""

#. translators: 1. File path. 2. File size. 3. Memory in bytes.
#: lib/wordfenceScanner.php:293
msgid "Scanning contents: %1$s (Size: %2$s Mem: %3$s)"
msgstr ""

#. translators: 1. File path. 2. File size.
#: lib/wordfenceScanner.php:301
msgid "Scanning contents: %1$s (Size: %2$s)"
msgstr ""

#. translators: File path.
#: lib/wordfenceScanner.php:319
msgid "Seek error occurred in file: %s - Skipping."
msgstr ""

#: lib/wordfenceScanner.php:338
msgid "This file was detected because you have enabled \"Scan images, binary, and other files as if they were executable\", which treats non-PHP files as if they were PHP code. This option is more aggressive than the usual scans, and may cause false positives."
msgstr ""

#: lib/wordfenceScanner.php:341
msgid "This file was detected because you have enabled HIGH SENSITIVITY scanning. This option is more aggressive than the usual scans, and may cause false positives."
msgstr ""

#. translators: Malware signature rule ID.
#: lib/wordfenceScanner.php:356
msgid "Resuming malware scan at rule %s."
msgstr ""

#: lib/wordfenceScanner.php:390
msgid "This file appears to be installed or modified by a hacker to perform malicious activity. If you know about this file you can choose to ignore it to exclude it from future scans."
msgstr ""

#: lib/wordfenceScanner.php:401
msgid "File appears to be malicious or unsafe: %s"
msgstr ""

#: lib/wordfenceScanner.php:402
msgid "The matched text in this file is: %s"
msgstr ""

#. translators: Scan result type.
#: lib/wordfenceScanner.php:402
msgid "The issue type is: %s"
msgstr ""

#. translators: Scan result description.
#: lib/wordfenceScanner.php:402
msgid "Description: %s"
msgstr ""

#. translators: Malware signature rule ID.
#: lib/wordfenceScanner.php:422
msgid "Forking during malware scan (%s) to ensure continuity."
msgstr ""

#: lib/wordfenceScanner.php:443
msgid "This file may contain malicious executable code: "
msgstr ""

#. translators: Malware signature matched text.
#: lib/wordfenceScanner.php:444
msgid "This file is a PHP executable file and contains the word \"eval\" (without quotes) and the word \"%s\" (without quotes). The eval() function along with an encoding function like the one mentioned are commonly used by hackers to hide their code. If you know about this file you can choose to ignore it to exclude it from future scans. This file was detected because you have enabled HIGH SENSITIVITY scanning. This option is more aggressive than the usual scans, and may cause false positives."
msgstr ""

#: lib/wordfenceScanner.php:490
msgid "Asking Wordfence to check URLs against malware list."
msgstr ""

#: lib/wordfenceScanner.php:518
#: lib/wordfenceScanner.php:560
msgid "File contains suspected malware URL: "
msgstr ""

#. translators: 1. Malware signature matched text. 2. Malicious URL. 3. Malicious URL.
#: lib/wordfenceScanner.php:521
msgid "This file contains a suspected malware URL listed on Google's list of malware sites. Wordfence decodes %1$s when scanning files so the URL may not be visible if you view this file. The URL is: %2$s - More info available at <a href=\"http://safebrowsing.clients.google.com/safebrowsing/diagnostic?site=%3$s&client=googlechrome&hl=en-US\" target=\"_blank\" rel=\"noopener noreferrer\">Google Safe Browsing diagnostic page<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: lib/wordfenceScanner.php:542
msgid "File contains suspected phishing URL: "
msgstr ""

#: lib/wordfenceScanner.php:543
msgid "This file contains a URL that is a suspected phishing site that is currently listed on Google's list of known phishing sites. The URL is: "
msgstr ""

#: lib/wordfenceScanner.php:561
msgid "This file contains a URL that is currently listed on Wordfence's domain blocklist. The URL is: "
msgstr ""

#: lib/wordfenceScanner.php:577
msgid "Finalizing malware scan results"
msgstr ""

#. translators: 1. Number of fils. 2. Seconds in millisecond precision.
#: lib/wordfenceScanner.php:594
msgid "Scanned contents of %1$d additional files at %2$.2f per second"
msgstr ""

#: lib/wordfenceURLHoover.php:180
msgid "Gathering host keys."
msgstr ""

#: lib/wordfenceURLHoover.php:187
msgid "Using MySQLi directly."
msgstr ""

#. translators: Number of domains.
#: lib/wordfenceURLHoover.php:225
msgid "Checking %d host keys against Wordfence scanning servers."
msgstr ""

#: lib/wordfenceURLHoover.php:227
msgid "Done host key check."
msgstr ""

#. translators: 1. Number of URLs. 2. Number of files.
#: lib/wordfenceURLHoover.php:311
msgid "Checking %1$d URLs from %2$d sources."
msgstr ""

#: lib/wordfenceURLHoover.php:316
msgid "Done URL check."
msgstr ""

#: models/block/wfBlock.php:84
msgid "IP Block"
msgstr ""

#: models/block/wfBlock.php:86
msgid "IP Throttled"
msgstr ""

#: models/block/wfBlock.php:88
msgid "Lockout"
msgstr ""

#: models/block/wfBlock.php:90
msgid "Country Block"
msgstr ""

#: models/block/wfBlock.php:92
msgid "Advanced Block"
msgstr ""

#: models/block/wfBlock.php:174
msgid "Invalid block type."
msgstr ""

#: models/block/wfBlock.php:175
msgid "Invalid block duration."
msgstr ""

#: models/block/wfBlock.php:176
msgid "A block reason must be provided."
msgstr ""

#: models/block/wfBlock.php:179
msgid "Invalid IP address."
msgstr ""

#. translators: Support URL
#: models/block/wfBlock.php:180
msgid "This IP address is in a range of addresses that Wordfence does not block. The IP range may be internal or belong to a service that is always allowed. Allowlisting of external services can be disabled. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: models/block/wfBlock.php:183
#: models/block/wfBlock.php:184
msgid "Nothing selected to block."
msgstr ""

#: models/block/wfBlock.php:185
msgid "No countries selected."
msgstr ""

#: models/block/wfBlock.php:190
msgid "An invalid country was selected."
msgstr ""

#: models/block/wfBlock.php:200
msgid "Ranges mixing IPv4 and IPv6 addresses are not supported."
msgstr ""

#: models/block/wfBlock.php:206
msgid "Invalid IP range."
msgstr ""

#: models/block/wfBlock.php:214
msgid "Invalid hostname."
msgstr ""

#: models/block/wfBlock.php:219
msgid "No block parameters provided."
msgstr ""

#: models/firewall/wfFirewall.php:38
#: views/waf/options-group-basic-firewall.php:47
msgid "Learning Mode"
msgstr ""

#: models/firewall/wfFirewall.php:46
msgid "Extended Protection"
msgstr ""

#: models/firewall/wfFirewall.php:49
msgid "Basic Protection"
msgstr ""

#: models/firewall/wfFirewall.php:199
#: models/firewall/wfFirewall.php:367
#: models/firewall/wfFirewall.php:404
msgid "Enable firewall."
msgstr ""

#: models/firewall/wfFirewall.php:210
msgid "Enable Premium Rules."
msgstr ""

#: models/firewall/wfFirewall.php:244
msgid "Optimize the Wordfence Firewall."
msgstr ""

#: models/firewall/wfFirewall.php:252
msgid "Enable Rate Limiting and Advanced Blocking."
msgstr ""

#: models/firewall/wfFirewall.php:263
msgid "Repair the Wordfence Firewall configuration."
msgstr ""

#: models/firewall/wfFirewall.php:395
msgid "Re-enable %d firewall rule."
msgid_plural "Re-enable %d firewall rules."
msgstr[0] ""
msgstr[1] ""

#: models/firewall/wfFirewall.php:452
msgid "Enable Firewall."
msgstr ""

#: models/firewall/wfFirewall.php:461
#: models/firewall/wfFirewall.php:470
msgid "Enable Real-Time IP Blocklist."
msgstr ""

#: models/firewall/wfFirewall.php:569
msgid "Enable Real-Time Wordfence Security Network."
msgstr ""

#: models/firewall/wfFirewall.php:575
msgid "Enforce Strong Passwords."
msgstr ""

#: models/firewall/wfFirewall.php:581
msgid "Enable Mask Login Errors."
msgstr ""

#: models/firewall/wfFirewall.php:587
msgid "Enable Block Admin Registration."
msgstr ""

#: models/firewall/wfFirewall.php:593
msgid "Disable Author Scanning."
msgstr ""

#: models/firewall/wfFirewall.php:599
msgid "Enable Brute Force Protection."
msgstr ""

#: models/page/wfPage.php:127
msgid "Support"
msgstr ""

#: models/scanner/wfScanner.php:118
msgid "Quick"
msgstr ""

#: models/scanner/wfScanner.php:120
msgid "Limited"
msgstr ""

#: models/scanner/wfScanner.php:122
#: models/scanner/wfScanner.php:794
#: views/scanner/scan-type.php:32
msgid "High Sensitivity"
msgstr ""

#: models/scanner/wfScanner.php:124
#: views/scanner/scan-scheduling.php:50
msgid "Custom"
msgstr ""

#: models/scanner/wfScanner.php:127
msgid "Standard"
msgstr ""

#: models/scanner/wfScanner.php:141
msgid "Low resource utilization, limited detection capability"
msgstr ""

#: models/scanner/wfScanner.php:143
msgid "Standard detection capability, chance of false positives"
msgstr ""

#: models/scanner/wfScanner.php:145
msgid "Custom scan options selected"
msgstr ""

#: models/scanner/wfScanner.php:148
msgid "Standard detection capability"
msgstr ""

#: models/scanner/wfScanner.php:790
msgid "Quick Scan"
msgstr ""

#: models/scanner/wfScanner.php:792
#: views/scanner/scan-type.php:20
msgid "Limited Scan"
msgstr ""

#: models/scanner/wfScanner.php:796
#: views/scanner/scan-type.php:38
msgid "Custom Scan"
msgstr ""

#: models/scanner/wfScanner.php:799
#: views/scanner/scan-type.php:26
msgid "Standard Scan"
msgstr ""

#: models/scanner/wfScanner.php:878
msgid "Enable Premium Reputation Checks."
msgstr ""

#: models/scanner/wfScanner.php:886
msgid "Enable %d scan option."
msgid_plural "Enable %d scan options."
msgstr[0] ""
msgstr[1] ""

#: models/scanner/wfScanner.php:928
msgid "Enable scan option to check if this website is being \"Spamvertised\"."
msgstr ""

#: models/scanner/wfScanner.php:929
msgid "Enable scan option to check if your website IP is generating spam."
msgstr ""

#: models/scanner/wfScanner.php:930
msgid "Enable scan option to check if your website is on a domain blocklist."
msgstr ""

#: models/scanner/wfScanner.php:1034
msgid "User defined scan pattern"
msgstr ""

#. translators: 1. Day of week. 2. Hour of day. 3. Localized date.
#: models/scanner/wfScanner.php:1312
msgid "Scheduled time for day %s hour %s is: %s"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:60
#: modules/login-security/classes/controller/ajax.php:70
msgid "You do not have permission to change options."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:65
msgid "You do not have permission to send notifications."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:80
msgid "You do not have permission to reset reCAPTCHA statistics."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:85
msgid "You do not have permission to reset the 2FA grace period."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:90
msgid "You do not have permission to revoke the 2FA grace period."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:95
msgid "You do not have permission to reset the NTP failure count."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:100
msgid "You do not have permission to disable NTP."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:105
msgid "You do not have permission to dismiss this notice."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:139
msgid "An unknown action was provided."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:146
msgid "An expected parameter was not provided."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:154
msgid "Your browser sent an invalid security token. Please try reloading this page."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:186
msgid "<strong>ERROR</strong>: A username and password must be provided. <a href=\"%s\" title=\"Password Lost and Found\">Lost your password</a>?"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:210
#: modules/login-security/classes/controller/ajax.php:242
msgid "<strong>ERROR</strong>: The username or password you entered is incorrect. <a href=\"%s\" title=\"Password Lost and Found\">Lost your password</a>?"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:257
#: modules/login-security/classes/controller/ajax.php:268
#: modules/login-security/classes/controller/ajax.php:276
#: modules/login-security/classes/controller/ajax.php:299
msgid "<strong>ERROR</strong>: Unable to send message. Please refresh the page and try again."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:282
msgid "<strong>ERROR</strong>: Unable to send message. You have exceeded the maximum number of messages that may be sent at this time. Please try again later."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:287
msgid "Blocked User Registration Contact Form"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:288
msgid ""
"A visitor blocked from registration sent the following message.\n"
"\n"
"----------------------------------------\n"
"\n"
"IP: %s\n"
"Username: %s\n"
"Email: %s\n"
"reCAPTCHA Score: %f\n"
"\n"
"----------------------------------------\n"
"\n"
"%s"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:293
msgid "<strong>MESSAGE SENT</strong>: Your message was sent to the site owner."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:296
msgid "<strong>ERROR</strong>: An error occurred while sending the message. Please try again."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:307
msgid "You do not have permission to activate the given user."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:312
msgid "The given user does not exist."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:317
msgid "You do not have permission to activate 2FA."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:321
msgid "The given user already has two-factor authentication active."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:326
msgid "The code provided does not match the expected value. Please verify that the time on your authenticator device is correct and that this server's time is correct."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:331
#: modules/login-security/classes/controller/ajax.php:383
#: modules/login-security/views/manage/regenerate.php:17
msgid "%d unused recovery code remains. You may generate a new set by clicking below."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:331
#: modules/login-security/classes/controller/ajax.php:383
#: modules/login-security/views/manage/regenerate.php:17
msgid "%d unused recovery codes remain. You may generate a new set by clicking below."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:339
msgid "You do not have permission to deactivate the given user."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:344
#: modules/login-security/classes/controller/ajax.php:370
msgid "The user does not exist."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:349
msgid "You do not have permission to deactivate 2FA."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:353
#: modules/login-security/classes/controller/ajax.php:379
msgid "The user specified does not have two-factor authentication active."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:365
msgid "You do not have permission to generate new recovery codes for the given user."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:375
msgid "You do not have permission to generate new recovery codes."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:438
msgid "The specified URL is invalid."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:443
msgid "More than %d users exist for the selected role. This notification is not designed to handle large groups of users. In such instances, using a different solution for notifying users of upcoming 2FA requirements is recommended."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:448
msgid "2FA will soon be required on %s"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:458
msgid "<html><body><p>You do not currently have two-factor authentication active on your account, which will be required beginning %s.</p><p><a href=\"%s\">Configure 2FA</a></p></body></html>"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:469
msgid "No users currently exist with the selected role."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:472
msgid "All users with the selected role already have two-factor authentication activated or have been locked out."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:475
msgid "A reminder to activate two-factor authentication was sent to %d user."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:477
msgid "A reminder to activate two-factor authentication was sent to %d users."
msgstr ""

#: modules/login-security/classes/controller/ajax.php:522
#: modules/login-security/classes/controller/ajax.php:536
msgid "Invalid user specified"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:524
msgid "Invalid grace period override"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:529
msgid "Failed to reset grace period"
msgstr ""

#: modules/login-security/classes/controller/ajax.php:555
msgid "Unable to dismiss notice"
msgstr ""

#: modules/login-security/classes/controller/settings.php:203
msgid "The IP/range %s is invalid."
msgstr ""

#: modules/login-security/classes/controller/settings.php:209
msgid "An invalid IP source was provided."
msgstr ""

#: modules/login-security/classes/controller/settings.php:237
msgid "Unable to validate the reCAPTCHA site key. Please check the key and try again."
msgstr ""

#: modules/login-security/classes/controller/settings.php:241
msgid "An error was encountered while validating the reCAPTCHA site key: %s"
msgstr ""

#: modules/login-security/classes/controller/users.php:530
#: modules/login-security/classes/controller/wordfencels.php:486
msgid "2FA Status"
msgstr ""

#: modules/login-security/classes/controller/users.php:534
msgid "Last Login"
msgstr ""

#: modules/login-security/classes/controller/users.php:536
msgid "Last CAPTCHA"
msgstr ""

#: modules/login-security/classes/controller/users.php:546
msgid "Not Allowed"
msgstr ""

#: modules/login-security/classes/controller/users.php:554
msgid "Inactive<small class=\"wfls-sub-status\">(Grace Period)</small>"
msgstr ""

#: modules/login-security/classes/controller/users.php:557
msgid "Locked Out<small class=\"wfls-sub-status\">(Grace Period Disabled)</small>"
msgstr ""

#: modules/login-security/classes/controller/users.php:557
msgid "Locked Out<small class=\"wfls-sub-status\">(Grace Period Exceeded)</small>"
msgstr ""

#: modules/login-security/classes/controller/users.php:662
msgid "Edit two-factor authentication for %s"
msgstr ""

#: modules/login-security/classes/controller/users.php:662
#: modules/login-security/views/settings/options.php:9
msgid "2FA"
msgstr ""

#: modules/login-security/classes/controller/users.php:673
#: modules/login-security/views/settings/user-stats.php:25
msgid "2FA Active"
msgstr ""

#: modules/login-security/classes/controller/users.php:674
#: modules/login-security/views/settings/user-stats.php:26
msgid "2FA Inactive"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:111
msgid "Login Security: Enable 2FA for these roles"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:112
msgid "Login Security: Allow remembering device for 30 days"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:113
msgid "Login Security: Require 2FA for XML-RPC call authentication"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:114
msgid "Login Security: Disable XML-RPC authentication"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:115
msgid "Login Security: Allowlisted IP addresses that bypass 2FA and reCAPTCHA"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:116
msgid "Login Security: Enable reCAPTCHA on the login and user registration pages"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:118
#: modules/login-security/classes/controller/wordfencels.php:121
msgid "Login Security Options"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:119
msgid "Login Security options are available on the Login Security options page"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:191
msgid "XML-RPC authentication is disabled. Jetpack is currently active and requires XML-RPC authentication to work correctly. <a href=\"%s\">Manage Settings</a>"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:195
msgid "reCAPTCHA test mode is enabled. While enabled, login and registration requests will be checked for their score but will not be blocked if the score is below the minimum score. <a href=\"%s\">Manage Settings</a>"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:202
msgid "WooCommerce appears to be installed, but the Wordfence Login Security WooCommerce integration is not currently enabled. Without this feature, WooCommerce forms will not support all functionality provided by Wordfence Login Security, including CAPTCHA for the login page and user registration."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:203
msgid "Manage Settings"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:340
msgid "Message to Support"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:342
msgid "An error was encountered while trying to send the message. Please try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:343
msgid "<strong>ERROR</strong>: An error was encountered while trying to send the message. Please try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:344
msgid "Login failed with status code 403. Please contact the site administrator."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:345
msgid "<strong>ERROR</strong>: Login failed with status code 403. Please contact the site administrator."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:346
msgid "Login failed with status code 503. Please contact the site administrator."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:347
msgid "<strong>ERROR</strong>: Login failed with status code 503. Please contact the site administrator."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:348
msgid "Wordfence 2FA Code"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:349
msgid "Remember for 30 days"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:350
msgid "Log In"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:351
msgid "<strong>ERROR</strong>: An error was encountered while trying to authenticate. Please try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:352
msgid "The Wordfence 2FA Code can be found within the authenticator app you used when first activating two-factor authentication. You may also use one of your recovery codes."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:483
msgid "Wordfence Login Security"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:492
#: modules/login-security/views/manage/grace-period.php:44
msgid "Two-factor authentication is required for your account, but has not been configured."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:492
#: modules/login-security/views/manage/grace-period.php:45
msgid "Two-factor authentication is required for this account, but has not been configured."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:493
msgid "Wordfence 2FA is active."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:493
msgid "Wordfence 2FA is inactive."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:498
msgid "Two-factor authentication must be activated for your account prior to %s to avoid losing access."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:499
msgid "Two-factor authentication must be activated for this account prior to %s."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:502
msgid "Manage 2FA"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:502
msgid "Activate 2FA"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:506
msgid "Two-factor authentication is not currently enabled for this account type. To enable it, visit the Wordfence 2FA Settings page."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:523
msgid "Manage 2FA Settings"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:645
msgid "<strong>CAPTCHA EXPIRED</strong>: The CAPTCHA verification for this login attempt has expired. Please try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:685
msgid "Login Verification Required"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:690
msgid "<strong>VERIFICATION REQUIRED</strong>: Additional verification is required for login. If there is a valid account for the provided login credentials, please check the email address associated with it for a verification link to continue logging in."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:706
msgid "<strong>CODE INVALID</strong>: The 2FA code provided is either expired or invalid. Please try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:715
msgid "<strong>CODE REQUIRED</strong>: Please enter your 2FA code immediately after your password in the same field."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:717
msgid "<strong>CODE REQUIRED</strong>: Please provide your 2FA code when prompted."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:720
msgid "<strong>LOGIN BLOCKED</strong>: 2FA is required to be active on your account. Please contact the site administrator."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:723
msgid "You do not currently have two-factor authentication active on your account, which will be required beginning %s. <a href=\"%s\">Configure 2FA</a>"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:773
msgid "Email verification succeeded. Please continue logging in."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:776
msgid "Email verification invalid or expired. Please try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:830
#: modules/login-security/classes/controller/wordfencels.php:833
msgid "Login Security"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:861
#: modules/login-security/views/settings/options.php:23
#: modules/login-security/views/settings/user-stats.php:33
msgid "Super Administrator"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:865
#: modules/login-security/views/manage/grace-period.php:22
#: modules/login-security/views/options/option-roles.php:57
msgid "Grace Period"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:884
msgid "Users without 2FA active (%s)"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:903
msgid "Learn more<span class=\"wfls-hidden-xs\"> about Two-Factor Authentication</span>"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:912
msgid "Settings"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:913
msgid "Login Security Settings"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:913
msgid "Learn more<span class=\"wfls-hidden-xs\"> about Login Security</span>"
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:939
msgid "<strong>REGISTRATION ATTEMPT BLOCKED</strong>: This site requires a security token created when the page loads for all registration attempts. Please ensure JavaScript is enabled and try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:946
msgid "<strong>REGISTRATION ATTEMPT BLOCKED</strong>: The security token for the login attempt was invalid or expired. Please reload the page and try again."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:959
msgid "<strong>REGISTRATION BLOCKED</strong>: The registration request was blocked because it was flagged as spam. Please try again or <a href=\"#\" class=\"wfls-registration-captcha-contact\" data-token=\"%s\">contact the site owner</a> for help."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:962
msgid "<strong>REGISTRATION BLOCKED</strong>: The registration request was blocked because it was flagged as spam. Please try again or contact the site owner for help."
msgstr ""

#: modules/login-security/classes/controller/wordfencels.php:1032
msgid "Wordfence 2FA"
msgstr ""

#: modules/login-security/classes/model/notice.php:31
msgid "<a class=\"wfls-btn wfls-btn-default wfls-btn-sm wfls-dismiss-link\" href=\"#\" onclick=\"GWFLS.dismiss_notice('%s'); return false;\">Dismiss</a>"
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:7
msgid "Unable to Activate Grace Period"
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:7
msgid "Unable to Reset Grace Period"
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:11
msgid "Grace Period Override"
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:13
#: modules/login-security/views/options/option-roles.php:59
msgid "days"
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:17
msgid "Activate Grace Period"
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:17
msgid "Reset Grace Period"
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:48
msgid "An unexpected error occurred while attempting to activate the grace period."
msgstr ""

#: modules/login-security/views/common/reset-grace-period.php:48
msgid "An unexpected error occurred while attempting to reset the grace period."
msgstr ""

#: modules/login-security/views/common/revoke-grace-period.php:4
msgid "Unable to Revoke Grace Period"
msgstr ""

#: modules/login-security/views/common/revoke-grace-period.php:9
msgid "Revoke Grace Period"
msgstr ""

#: modules/login-security/views/common/revoke-grace-period.php:38
msgid "An unexpected error occurred while attempting to revoke the grace period."
msgstr ""

#: modules/login-security/views/email/login-verification.php:10
msgid "Please verify a login attempt for your account on: %s"
msgstr ""

#: modules/login-security/views/email/login-verification.php:12
msgid "Request Time:"
msgstr ""

#: modules/login-security/views/email/login-verification.php:15
msgid "The request was flagged as suspicious, and we need verification that you attempted to log in to allow it to proceed. This verification link <b>will be valid for 15 minutes</b> from the time it was sent. If you did not attempt this login, please change your password immediately."
msgstr ""

#: modules/login-security/views/email/login-verification.php:17
msgid "If you were attempting to log in to this site, <a href=\"%s\"><strong>Verify and Log In</strong></a>"
msgstr ""

#: modules/login-security/views/manage/activate.php:13
msgid "2. Enter Code from Authenticator App"
msgstr ""

#: modules/login-security/views/manage/activate.php:18
#: modules/login-security/views/manage/activate.php:131
msgid "Download Recovery Codes"
msgstr ""

#: modules/login-security/views/manage/activate.php:18
#: modules/login-security/views/options/option-roles.php:9
msgid "Optional"
msgstr ""

#: modules/login-security/views/manage/activate.php:19
#: modules/login-security/views/manage/regenerate.php:66
msgid "Use one of these %d codes to log in if you lose access to your authenticator device. Codes are %d characters long plus optional spaces. Each one may be used only once."
msgstr ""

#: modules/login-security/views/manage/activate.php:22
#: modules/login-security/views/manage/regenerate.php:68
msgid "Two-Factor Authentication Recovery Codes - %s (%s)"
msgstr ""

#: modules/login-security/views/manage/activate.php:23
#: modules/login-security/views/manage/regenerate.php:69
msgid "Each line of %d letters and numbers is a single recovery code, with optional spaces for readability. To use a recovery code, after entering your username and password, enter the code like \"1234 5678 90AB CDEF\" at the 2FA prompt. If your site has a custom login prompt and does not show a 2FA prompt, you can use the single-step method by entering your password and the code together in the Password field, like \"mypassword1234 5678 90AB CDEF\". Your recovery codes are:"
msgstr ""

#: modules/login-security/views/manage/activate.php:36
msgid "Enter the code from your authenticator app below to verify and activate two-factor authentication for this account."
msgstr ""

#: modules/login-security/views/manage/activate.php:42
msgid "For help on setting up an app, visit our help article."
msgstr ""

#: modules/login-security/views/manage/activate.php:89
#: modules/login-security/views/manage/activate.php:120
msgid "Error Activating 2FA"
msgstr ""

#: modules/login-security/views/manage/activate.php:120
msgid "An error was encountered while trying to activate two-factor authentication. Please try again."
msgstr ""

#: modules/login-security/views/manage/activate.php:132
msgid "Reminder: If you lose access to your authenticator device, you can use recovery codes to log in. If you have not saved a copy of your recovery codes, we recommend downloading them now."
msgstr ""

#: modules/login-security/views/manage/activate.php:134
msgid "Skip"
msgstr ""

#: modules/login-security/views/manage/code.php:11
msgid "1. Scan Code or Enter Key"
msgstr ""

#: modules/login-security/views/manage/deactivate.php:17
msgid "Wordfence 2FA Active"
msgstr ""

#: modules/login-security/views/manage/deactivate.php:22
msgid "Wordfence two-factor authentication is currently active on your account. You may deactivate it by clicking the button below."
msgstr ""

#: modules/login-security/views/manage/deactivate.php:22
msgid "Wordfence two-factor authentication is currently active on the account <strong>%s</strong>. You may deactivate it by clicking the button below."
msgstr ""

#: modules/login-security/views/manage/deactivate.php:23
#: modules/login-security/views/manage/deactivate.php:32
#: views/offboarding/deactivation-prompt.php:29
msgid "Deactivate"
msgstr ""

#: modules/login-security/views/manage/deactivate.php:29
msgid "Deactivate 2FA"
msgstr ""

#: modules/login-security/views/manage/deactivate.php:30
msgid "Are you sure you want to deactivate two-factor authentication?"
msgstr ""

#: modules/login-security/views/manage/deactivate.php:66
#: modules/login-security/views/manage/deactivate.php:75
msgid "Error Deactivating 2FA"
msgstr ""

#: modules/login-security/views/manage/deactivate.php:75
msgid "An error was encountered while trying to deactivate two-factor authentication. Please try again."
msgstr ""

#: modules/login-security/views/manage/grace-period.php:31
msgid "Two-factor authentication will be required for your account beginning <strong>%s</strong>"
msgstr ""

#: modules/login-security/views/manage/grace-period.php:32
msgid "Two-factor authentication will be required for user <strong>%s</strong> beginning <strong>%s</strong>."
msgstr ""

#: modules/login-security/views/manage/regenerate.php:18
msgid "Generate New Codes"
msgstr ""

#: modules/login-security/views/manage/regenerate.php:24
msgid "Generate New Recovery Codes"
msgstr ""

#: modules/login-security/views/manage/regenerate.php:25
msgid "Are you sure you want to generate new recovery codes? Any remaining unused codes will be disabled."
msgstr ""

#: modules/login-security/views/manage/regenerate.php:27
msgid "Generate"
msgstr ""

#: modules/login-security/views/manage/regenerate.php:61
#: modules/login-security/views/manage/regenerate.php:92
msgid "Error Generating New Codes"
msgstr ""

#: modules/login-security/views/manage/regenerate.php:80
msgid "New Recovery Codes"
msgstr ""

#: modules/login-security/views/manage/regenerate.php:92
msgid "An error was encountered while trying to generate new recovery codes. Please try again."
msgstr ""

#: modules/login-security/views/onboarding/standalone-header.php:9
msgid "Wordfence Login Security Installed"
msgstr ""

#: modules/login-security/views/onboarding/standalone-header.php:13
msgid "You have just installed the Wordfence Login Security plugin. It contains a subset of the functionality found in the full Wordfence plugin: Two-factor Authentication, XML-RPC Protection and Login Page CAPTCHA."
msgstr ""

#: modules/login-security/views/onboarding/standalone-header.php:14
msgid "If you're looking for a more comprehensive solution, the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">full Wordfence plugin</a> includes all of the features in this plugin as well as a full-featured WordPress firewall, a security scanner, live traffic, and more. The standard installation includes a robust set of free features that can be upgraded via a Premium license key."
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:7
msgid "1.0 (definitely a human)"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:8
msgid "0.9"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:9
msgid "0.8"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:10
msgid "0.7"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:11
msgid "0.6"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:12
msgid "0.5 (probably a human)"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:13
msgid "0.4"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:14
msgid "0.3"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:15
msgid "0.2"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:16
msgid "0.1 (probably a bot)"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:27
msgid "reCAPTCHA human/bot threshold score"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:28
msgid "A reCAPTCHA score equal to or higher than this value will be considered human. Anything lower will be treated as a bot and require additional verification for login and registration."
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:50
msgid "Reset Score Statistics"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:87
msgid "Requests"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:105
msgid "reCAPTCHA Score History"
msgstr ""

#: modules/login-security/views/options/option-captcha-threshold.php:112
msgid "Count"
msgstr ""

#: modules/login-security/views/options/option-captcha.php:20
msgid "Enable reCAPTCHA on the login and user registration pages"
msgstr ""

#: modules/login-security/views/options/option-captcha.php:22
msgid "reCAPTCHA v3 does not make users solve puzzles or click a checkbox like previous versions. The only visible part is the reCAPTCHA logo. If a visitor's browser fails the CAPTCHA, Wordfence will send an email to the user's address with a link they can click to verify that they are a user of your site. You can read further details <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">in our documentation</a>."
msgstr ""

#: modules/login-security/views/options/option-captcha.php:33
msgid "reCAPTCHA v3 Site Key"
msgstr ""

#: modules/login-security/views/options/option-captcha.php:37
msgid "reCAPTCHA v3 Secret"
msgstr ""

#: modules/login-security/views/options/option-captcha.php:47
msgid "Note: This feature requires a free site key and secret for the <a href=\"https://www.google.com/recaptcha/about/\" target=\"_blank\" rel=\"noopener noreferrer\">Google reCAPTCHA v3 Service</a>. To set up new reCAPTCHA keys, log into your Google account and go to the <a href=\"https://www.google.com/recaptcha/admin\" target=\"_blank\" rel=\"noopener noreferrer\">reCAPTCHA admin page</a>."
msgstr ""

#: modules/login-security/views/options/option-ip-source.php:8
msgid "Use the most secure method to get visitor IP addresses. Prevents spoofing and works with most sites."
msgstr ""

#: modules/login-security/views/options/option-ip-source.php:8
#: views/dashboard/option-howgetips.php:8
msgid "(Recommended)"
msgstr ""

#: modules/login-security/views/options/option-ip-source.php:9
#: views/dashboard/option-howgetips.php:9
msgid "Use PHP's built in REMOTE_ADDR and don't use anything else. Very secure if this is compatible with your site."
msgstr ""

#: modules/login-security/views/options/option-ip-source.php:10
#: views/dashboard/option-howgetips.php:10
msgid "Use the X-Forwarded-For HTTP header. Only use if you have a front-end proxy or spoofing may result."
msgstr ""

#: modules/login-security/views/options/option-ip-source.php:11
#: views/dashboard/option-howgetips.php:11
msgid "Use the X-Real-IP HTTP header. Only use if you have a front-end proxy or spoofing may result."
msgstr ""

#: modules/login-security/views/options/option-ip-source.php:19
msgid "How to get IPs"
msgstr ""

#: modules/login-security/views/options/option-ip-source.php:48
#: views/dashboard/option-howgetips.php:51
msgid "These IPs (or CIDR ranges) will be ignored when determining the requesting IP via the X-Forwarded-For HTTP header. Enter one IP or CIDR range per line."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:10
msgid "NTP is a protocol that allows for remote time synchronization. Wordfence Login Security uses this protocol to ensure that it has the most accurate time which is necessary for TOTP-based two-factor authentication."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:12
msgid "The constant WORDFENCE_LS_DISABLE_NTP is defined which disables NTP entirely. Remove this constant or set it to a falsy value to enable NTP."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:15
msgid "NTP is currently disabled as %d subsequent attempts have failed."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:17
msgid "NTP was manually disabled."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:19
#: views/dashboard/options-group-license.php:179
msgid "Reset"
msgstr ""

#: modules/login-security/views/options/option-ntp.php:21
msgid "NTP is currently <strong>enabled</strong>."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:25
msgid "NTP updates are currently failing."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:26
msgid "NTP will be automatically disabled after %d more attempts."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:26
msgid "NTP will be automatically disabled after 1 more attempt."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:29
#: views/waf/option-whitelist.php:102
msgid "Disable"
msgstr ""

#: modules/login-security/views/options/option-ntp.php:43
msgid "Error Resetting NTP"
msgstr ""

#: modules/login-security/views/options/option-ntp.php:44
msgid "An error was encountered while trying to reset the NTP state. Please try again."
msgstr ""

#: modules/login-security/views/options/option-ntp.php:68
msgid "Error Disabling NTP"
msgstr ""

#: modules/login-security/views/options/option-ntp.php:69
msgid "An error was encountered while trying to disable NTP. Please try again."
msgstr ""

#: modules/login-security/views/options/option-roles.php:10
#: modules/login-security/views/settings/options.php:65
msgid "Required"
msgstr ""

#: modules/login-security/views/options/option-roles.php:23
msgid "Requiring 2FA for customers is not recommended as some customers may experience difficulties setting up or using two-factor authentication. Instead, using the \"Optional\" mode for users with the customer role is recommended which will allow customers to enable 2FA, but will not require them to do so."
msgstr ""

#: modules/login-security/views/options/option-roles.php:28
msgid "2FA Roles"
msgstr ""

#: modules/login-security/views/options/option-roles.php:53
msgid "In order to use 2FA with the WooCommerce customer role, you must either enable the \"WooCommerce integration\" option or use the \"wordfence_2fa_management\" shortcode to provide customers with access to the 2FA management interface. The default interface is only available through WordPress admin pages which are not accessible to users in the customer role."
msgstr ""

#: modules/login-security/views/options/option-roles.php:61
msgid "Setting the grace period to 0 will prevent users in roles where 2FA is required, including newly created users, from logging in if they have not already enabled two-factor authentication."
msgstr ""

#: modules/login-security/views/options/option-roles.php:64
msgid "For roles that require 2FA, users will have this many days to set up 2FA. Failure to set up 2FA during this period will result in the user losing account access. This grace period will apply to new users from the time of account creation. For existing users, this grace period will apply relative to the time at which the requirement is implemented. This grace period will not automatically apply to admins and must be manually enabled for each admin user."
msgstr ""

#: modules/login-security/views/options/option-roles.php:68
msgid "2FA Notifications"
msgstr ""

#: modules/login-security/views/options/option-roles.php:70
msgid "Send an email to users with the selected role to notify them of the grace period for enabling 2FA. Select the desired role and optionally specify the URL to be sent in the email to setup 2FA. If left blank, the URL defaults to the standard wordpress login and Wordfence’s Two-Factor Authentication plugin page. For example, if using WooCommerce, input the relative URL of the account page."
msgstr ""

#: modules/login-security/views/options/option-roles.php:74
msgid "2FA Role"
msgstr ""

#: modules/login-security/views/options/option-roles.php:82
msgid "2FA Relative URL (optional)"
msgstr ""

#: modules/login-security/views/options/option-roles.php:85
msgid "Notify"
msgstr ""

#: modules/login-security/views/options/option-roles.php:106
msgid "Send Anyway"
msgstr ""

#: modules/login-security/views/options/option-roles.php:110
#: modules/login-security/views/options/option-roles.php:120
msgid "Error Sending Notification"
msgstr ""

#: modules/login-security/views/options/option-roles.php:113
msgid "Notification Sent"
msgstr ""

#: modules/login-security/views/options/option-roles.php:120
msgid "An error was encountered while trying to send the notification. Please try again."
msgstr ""

#: modules/login-security/views/options/option-roles.php:161
msgid "Not Recommended"
msgstr ""

#: modules/login-security/views/options/option-roles.php:165
msgid "Make Optional"
msgstr ""

#: modules/login-security/views/options/option-roles.php:170
msgid "Proceed"
msgstr ""

#: modules/login-security/views/page/manage-embedded.php:26
msgid "Two-Factor Authentication, or 2FA, significantly improves login security for your account. Wordfence 2FA works with a number of TOTP-based apps like Google Authenticator, FreeOTP, and Authy. For a full list of tested TOTP-based apps, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">click here</a>."
msgstr ""

#: modules/login-security/views/page/manage.php:24
msgid "Two-Factor Authentication, or 2FA, significantly improves login security for your website. Wordfence 2FA works with a number of TOTP-based apps like Google Authenticator, FreeOTP, and Authy. For a full list of tested TOTP-based apps, <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">click here</a>."
msgstr ""

#: modules/login-security/views/page/manage.php:31
msgid "Editing User:&nbsp;&nbsp;%s <span class=\"wfls-text-plain\">%s</span>"
msgstr ""

#: modules/login-security/views/page/manage.php:31
msgid "(you)"
msgstr ""

#: modules/login-security/views/page/manage.php:108
msgid "Server Time:"
msgstr ""

#: modules/login-security/views/page/manage.php:109
msgid "Browser Time:"
msgstr ""

#: modules/login-security/views/page/manage.php:112
msgid "Corrected Time (NTP):"
msgstr ""

#: modules/login-security/views/page/manage.php:115
msgid "Corrected Time (WF):"
msgstr ""

#: modules/login-security/views/page/manage.php:118
msgid "Detected IP:"
msgstr ""

#: modules/login-security/views/page/manage.php:118
msgid "allowlisted"
msgstr ""

#: modules/login-security/views/page/permission-denied.php:6
msgid "Permission Denied"
msgstr ""

#: modules/login-security/views/page/permission-denied.php:7
msgid "You do not have permission to manage 2FA settings for your account."
msgstr ""

#: modules/login-security/views/page/role.php:5
msgid "This page only shows users and roles on the main site of this network"
msgstr ""

#: modules/login-security/views/page/role.php:10
msgid "2FA is not required for the %s role"
msgstr ""

#: modules/login-security/views/page/role.php:16
msgid "No users found in the %s state for the %s role"
msgstr ""

#: modules/login-security/views/page/role.php:18
msgid "Page %d is out of range"
msgstr ""

#: modules/login-security/views/page/role.php:35
msgid "N/A"
msgstr ""

#: modules/login-security/views/page/role.php:46
msgid "Page "
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Changes"
#: modules/login-security/views/page/settings.php:5
msgid "Cancel<span class=\"wfls-visible-sm-inline\"> Changes</span>"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Changes"
#: modules/login-security/views/page/settings.php:5
msgid "Save<span class=\"wfls-visible-sm-inline\"> Changes</span>"
msgstr ""

#: modules/login-security/views/settings/options.php:51
msgid "Allow remembering device for 30 days"
msgstr ""

#: modules/login-security/views/settings/options.php:52
msgid "If enabled, users with 2FA enabled may choose to be prompted for a code only once every 30 days per device."
msgstr ""

#: modules/login-security/views/settings/options.php:61
msgid "Require 2FA for XML-RPC call authentication"
msgstr ""

#: modules/login-security/views/settings/options.php:62
msgid "If enabled, XML-RPC calls that require authentication will also require a valid 2FA code to be appended to the password. You must choose the \"Skipped\" option if you use the WordPress app, the Jetpack plugin, or other services that require XML-RPC."
msgstr ""

#: modules/login-security/views/settings/options.php:64
msgid "Skipped"
msgstr ""

#: modules/login-security/views/settings/options.php:79
msgid "Disable XML-RPC authentication"
msgstr ""

#: modules/login-security/views/settings/options.php:80
msgid "If disabled, XML-RPC requests that attempt authentication will be rejected, whether the user has 2FA enabled or not."
msgstr ""

#: modules/login-security/views/settings/options.php:94
msgid "WooCommerce & Custom Integrations"
msgstr ""

#: modules/login-security/views/settings/options.php:107
msgid "WooCommerce integration"
msgstr ""

#: modules/login-security/views/settings/options.php:108
msgid "When enabled, reCAPTCHA and 2FA prompt support will be added to WooCommerce login and registration forms in addition to the default WordPress forms. Testing WooCommerce forms after enabling this feature is recommended to ensure plugin compatibility."
msgstr ""

#: modules/login-security/views/settings/options.php:119
msgid "Show Wordfence 2FA menu on WooCommerce Account page"
msgstr ""

#: modules/login-security/views/settings/options.php:120
msgid "When enabled, a Wordfence 2FA tab will be added to the WooCommerce account menu which will provide access for users to manage 2FA settings outside of the WordPress admin area. Testing the WooCommerce account interface after enabling this feature is recommended to ensure theme compatibility."
msgstr ""

#: modules/login-security/views/settings/options.php:134
msgid "2FA management shortcode"
msgstr ""

#: modules/login-security/views/settings/options.php:135
msgid "When enabled, the \"wordfence_2fa_management\" shortcode may be used to provide access for users to manage 2FA settings on custom pages."
msgstr ""

#: modules/login-security/views/settings/options.php:147
msgid "Use single-column layout for WooCommerce/shortcode 2FA management interface"
msgstr ""

#: modules/login-security/views/settings/options.php:148
msgid "When enabled, the 2FA management interface embedded through the WooCommerce integration or via a shortcode will use a vertical stacked layout as opposed to horizontal columns. Adjust this setting as appropriate to match your theme. This may be overridden using the \"stacked\" attribute for individual shortcodes."
msgstr ""

#: modules/login-security/views/settings/options.php:163
msgid "reCAPTCHA"
msgstr ""

#: modules/login-security/views/settings/options.php:188
msgid "Run reCAPTCHA in test mode"
msgstr ""

#: modules/login-security/views/settings/options.php:189
msgid "While in test mode, reCAPTCHA will score login and registration requests but not actually block them. The scores will be recorded and can be used to select a human/bot threshold value."
msgstr ""

#: modules/login-security/views/settings/options.php:214
msgid "Allowlisted IP addresses that bypass 2FA and reCAPTCHA"
msgstr ""

#: modules/login-security/views/settings/options.php:216
msgid "Allowlisted IPs must be placed on separate lines. You can specify ranges using the following formats: 127.0.0.1/24, 127.0.0.[1-100], or 127.0.0.1-***********."
msgstr ""

#: modules/login-security/views/settings/options.php:242
msgid "Show last login column on WP Users page"
msgstr ""

#: modules/login-security/views/settings/options.php:243
msgid "When enabled, the last login timestamp will be displayed for each user on the WP Users page. When used in conjunction with reCAPTCHA, the most recent score will also be displayed for each user."
msgstr ""

#: modules/login-security/views/settings/options.php:254
msgid "Delete Login Security tables and data on deactivation"
msgstr ""

#: modules/login-security/views/settings/options.php:255
msgid "If enabled, all settings and 2FA records will be deleted on deactivation. If later reactivated, all users that previously had 2FA active will need to set it up again."
msgstr ""

#: modules/login-security/views/settings/user-stats.php:11
msgid "User Summary"
msgstr ""

#: modules/login-security/views/settings/user-stats.php:15
msgid "Manage Users"
msgstr ""

#: modules/login-security/views/settings/user-stats.php:23
msgid "Role"
msgstr ""

#: modules/login-security/views/settings/user-stats.php:24
msgid "Total Users"
msgstr ""

#: modules/login-security/views/settings/user-stats.php:34
msgid "Custom Capabilities / Multiple Roles"
msgstr ""

#: modules/login-security/views/settings/user-stats.php:52
msgid "View users"
msgstr ""

#: modules/login-security/views/settings/user-stats.php:66
msgid "* User counts currently only reflect the main site on multisite installations."
msgstr ""

#: modules/login-security/views/settings/user-stats.php:74
msgid "User counts are hidden by default on sites with large numbers of users in order to improve performance."
msgstr ""

#: modules/login-security/views/settings/user-stats.php:74
msgid "User counts are currently disabled as the most recent attempt to count users failed to complete successfully."
msgstr ""

#: modules/login-security/views/settings/user-stats.php:75
msgid "Show User Counts"
msgstr ""

#: modules/login-security/views/user/grace-period-toggle.php:7
msgid "2FA Grace Period"
msgstr ""

#: modules/login-security/views/user/grace-period-toggle.php:10
msgid "Allow a grace period for this user prior to requiring Wordfence 2FA"
msgstr ""

#. translators: Site URL
#: views/blocking/block-list.php:11
msgid "Current blocks<span class=\"wf-hidden-xs\"> for %s</span>"
msgstr ""

#: views/blocking/block-list.php:17
msgid "Show<span class=\"wf-hidden-xs\"> Wordfence</span> Automatic<span class=\"wf-hidden-xs\"> Blocks</span>"
msgstr ""

#: views/blocking/block-list.php:34
msgid "Filter by Type, Detail, or Reason"
msgstr ""

#: views/blocking/block-list.php:35
#: views/blocking/block-list.php:379
#: views/waf/option-whitelist.php:113
msgid "Filter"
msgstr ""

#: views/blocking/block-list.php:39
#: views/blocking/block-list.php:502
msgid "Unblock"
msgstr ""

#: views/blocking/block-list.php:39
msgid "Make Permanent"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "All IPs"
#: views/blocking/block-list.php:39
msgid "Export<span class=\"wf-hidden-xs\"> All IPs</span>"
msgstr ""

#: views/blocking/block-list.php:68
msgid "Block Type"
msgstr ""

#: views/blocking/block-list.php:69
msgid "Detail"
msgstr ""

#: views/blocking/block-list.php:70
msgid "Rule Added"
msgstr ""

#: views/blocking/block-list.php:71
msgid "Reason"
msgstr ""

#: views/blocking/block-list.php:72
msgid "Expiration"
msgstr ""

#: views/blocking/block-list.php:74
msgid "Last Attempt"
msgstr ""

#: views/blocking/block-list.php:79
msgid "No blocks are currently active."
msgstr ""

#: views/blocking/block-list.php:84
msgid "No blocks match the current filter."
msgstr ""

#: views/blocking/block-list.php:382
msgid "Clear Filter"
msgstr ""

#: views/blocking/block-list.php:385
msgid "Change Filter"
msgstr ""

#: views/blocking/block-list.php:499
msgid "Unblocking"
msgstr ""

#: views/blocking/block-list.php:500
msgid "Are you sure you want to stop blocking the selected IP, range, or country?"
msgstr ""

#: views/blocking/block-list.php:500
msgid "Are you sure you want to stop blocking the ${count} selected IPs, ranges, and countries?"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "this IP Address"
#: views/blocking/blocking-create.php:13
msgid "Block<span class=\"wf-hidden-xs\"> this IP Address</span>"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "Address"
#: views/blocking/blocking-create.php:13
msgid "IP<span class=\"wf-hidden-xs\"> Address</span>"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "the Selected Countries"
#: views/blocking/blocking-create.php:14
msgid "Block<span class=\"wf-hidden-xs\"> the Selected Countries</span>"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Block"
#: views/blocking/blocking-create.php:14
msgid "Update<span class=\"wf-hidden-xs\"> Block</span>"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "Visitors Matching this Pattern"
#: views/blocking/blocking-create.php:15
msgid "Block<span class=\"wf-hidden-xs\"> Visitors Matching this Pattern</span>"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Custom"
#: views/blocking/blocking-create.php:15
msgid "<span class=\"wf-hidden-xs\">Custom </span>Pattern"
msgstr ""

#: views/blocking/blocking-create.php:113
msgid "Block Duration"
msgstr ""

#: views/blocking/blocking-create.php:115
msgid "Enter a duration (default is forever)"
msgstr ""

#: views/blocking/blocking-create.php:132
msgid "<span class=\"wf-hidden-xs\">What to </span>Block"
msgstr ""

#: views/blocking/blocking-create.php:137
msgid "Login Form"
msgstr ""

#: views/blocking/blocking-create.php:141
msgid "<span class=\"wf-hidden-xs\">Block access to the rest of the site</span><span class=\"wf-visible-xs\">Rest of site</span>"
msgstr ""

#. translators: Support URL
#: views/blocking/blocking-create.php:149
msgid "If you use Google Ads, blocking countries from accessing the entire site is not recommended. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "to Block"
#: views/blocking/blocking-create.php:153
msgid "Countries<span class=\"wf-hidden-xs\"> to Block</span>"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "from List"
#: views/blocking/blocking-create.php:153
msgid "Pick<span class=\"wf-hidden-xs\"> from List</span>"
msgstr ""

#: views/blocking/blocking-create.php:176
#: views/blocking/options-group-advanced-country.php:82
msgid "Put Geographic Protection In Place With Country Blocking"
msgstr ""

#: views/blocking/blocking-create.php:177
#: views/blocking/options-group-advanced-country.php:83
msgid "Wordfence country blocking is designed to stop an attack, prevent content theft, or end malicious activity that originates from a geographic region in less than 1/300,000th of a second. Blocking countries who are regularly creating failed logins, a large number of page not found errors, and are clearly engaged in malicious activity is an effective way to protect your site during an attack."
msgstr ""

#: views/blocking/blocking-create.php:185
msgid "IP<span class=\"wf-hidden-xs\"> Address to Block</span>"
msgstr ""

#: views/blocking/blocking-create.php:186
msgid "Enter an IP address"
msgstr ""

#: views/blocking/blocking-create.php:189
msgid "IP<span class=\"wf-hidden-xs\"> Address</span> Range"
msgstr ""

#: views/blocking/blocking-create.php:190
msgid "e.g., *************** - *************** or *************/24"
msgstr ""

#: views/blocking/blocking-create.php:194
msgid "e.g., *.amazonaws.com or *.linode.com"
msgstr ""

#: views/blocking/blocking-create.php:197
msgid "<span class=\"wf-hidden-xs\">Browser </span>User Agent"
msgstr ""

#: views/blocking/blocking-create.php:198
msgid "e.g., *badRobot*, *MSIE*, or *browserSuffix"
msgstr ""

#: views/blocking/blocking-create.php:202
msgid "e.g., *badwebsite.example.com*"
msgstr ""

#: views/blocking/blocking-create.php:205
msgid "<span class=\"wf-hidden-xs\">Block </span>Reason"
msgstr ""

#: views/blocking/blocking-create.php:206
msgid "Enter a reason"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "Visitors Matching this"
#: views/blocking/blocking-create.php:212
msgid "Block<span class=\"wf-hidden-xs\"> Visitors Matching this</span> Pattern"
msgstr ""

#: views/blocking/blocking-create.php:512
msgid "Error Saving Block"
msgstr ""

#: views/blocking/blocking-create.php:527
msgid "U.S. Blocked"
msgstr ""

#: views/blocking/blocking-create.php:528
msgid "For most sites, we recommend allowing access from the United States, where Google and other benign crawlers are located. Some well-known crawlers also have locations in Europe, so be careful when blocking other countries, especially if you have not seen significant attacks from them."
msgstr ""

#: views/blocking/blocking-status.php:15
msgid "Manage global blocking options."
msgstr ""

#: views/blocking/blocking-status.php:24
msgid "Enable country blocking by upgrading to Premium."
msgstr ""

#: views/blocking/country-modal.php:24
msgid "Select Countries to Block from List"
msgstr ""

#: views/blocking/country-modal.php:28
msgid "Block All"
msgstr ""

#: views/blocking/country-modal.php:28
msgid "Unblock All"
msgstr ""

#: views/blocking/country-modal.php:75
msgid "Back"
msgstr ""

#: views/blocking/country-modal.php:75
msgid "Save"
msgstr ""

#: views/blocking/option-bypass-cookie.php:12
msgid "If user who is allowed to access the site views the relative URL"
msgstr ""

#: views/blocking/option-bypass-cookie.php:13
msgid "/set-country-bypass/"
msgstr ""

#: views/blocking/option-bypass-cookie.php:16
msgid "then set a cookie that will bypass country blocking in the future in case that user hits the site from a blocked country."
msgstr ""

#: views/blocking/option-bypass-redirect.php:12
msgid "If user hits the relative URL"
msgstr ""

#: views/blocking/option-bypass-redirect.php:13
msgid "/bypassurl/"
msgstr ""

#: views/blocking/option-bypass-redirect.php:16
msgid "then redirect that user to"
msgstr ""

#: views/blocking/option-bypass-redirect.php:17
msgid "/page-name/"
msgstr ""

#: views/blocking/option-bypass-redirect.php:21
msgid "and set a cookie that will bypass all country blocking."
msgstr ""

#: views/blocking/options-group-advanced-country.php:22
msgid "Advanced Country Blocking Options"
msgstr ""

#: views/blocking/options-group-advanced-country.php:50
msgid "Enter a full URL (e.g., http://example.com/blocked/)"
msgstr ""

#: views/common/license.php:15
msgid "Wordfence License Installation Successful"
msgstr ""

#: views/common/license.php:18
msgid "Wordfence License Installation Failed"
msgstr ""

#: views/common/license.php:21
msgid "Install Wordfence License"
msgstr ""

#: views/common/license.php:105
msgid "The Wordfence license provided has been installed."
msgstr ""

#. translators: WordPress admin URL
#: views/common/license.php:106
msgid "Return to the <a href=\"%s\">Wordfence Admin Page</a>"
msgstr ""

#: views/common/license.php:108
msgid "The Wordfence license could not be installed."
msgstr ""

#: views/common/license.php:110
msgid "Please enter the license to install."
msgstr ""

#: views/common/license.php:118
#: views/common/unsubscribe.php:127
msgid "Generated by Wordfence at "
msgstr ""

#: views/common/license.php:118
#: views/common/unsubscribe.php:127
msgid "Your computer's time: "
msgstr ""

#: views/common/page-title.php:18
msgid "Go:"
msgstr ""

#: views/common/page-title.php:20
msgid "Go to"
msgstr ""

#: views/common/status-tooltip.php:18
msgid "<strong>Congratulations!</strong> You've optimized configurations for this feature! If you want to learn more about how this score is determined, click the link below."
msgstr ""

#: views/common/status-tooltip.php:19
#: views/common/status-tooltip.php:30
msgid "How does Wordfence determine this?"
msgstr ""

#: views/common/status-tooltip.php:21
msgid "How do I get to 100%?"
msgstr ""

#: views/common/unsubscribe.php:16
msgid "Unsubscribe from Security Alerts"
msgstr ""

#: views/common/unsubscribe.php:19
msgid "Unsubscription Confirmation Sent"
msgstr ""

#: views/common/unsubscribe.php:22
msgid "Unsubscribe Successful"
msgstr ""

#: views/common/unsubscribe.php:25
msgid "Confirm Unsubscribe"
msgstr ""

#: views/common/unsubscribe.php:109
msgid "The email address provided has been unsubscribed from future alert emails."
msgstr ""

#: views/common/unsubscribe.php:111
msgid "If the email address provided was on the alert email list, it has been sent an unsubscribe link."
msgstr ""

#: views/common/unsubscribe.php:113
msgid "Please enter an email address to unsubscribe from alerts. If this email address exists on the alert email list, it will receive a confirmation link to unsubscribe."
msgstr ""

#. translators: Email address.
#: views/common/unsubscribe.php:120
msgid "Please confirm the unsubscribe request for %s."
msgstr ""

#: views/dashboard/global-status.php:16
msgid "Wordfence Protection Activated"
msgstr ""

#: views/dashboard/option-howgetips.php:8
msgid "Let Wordfence use the most secure method to get visitor IP addresses. Prevents spoofing and works with most sites."
msgstr ""

#: views/dashboard/option-howgetips.php:12
msgid "Use the Cloudflare \"CF-Connecting-IP\" HTTP header to get a visitor IP. Only use if you're using Cloudflare."
msgstr ""

#: views/dashboard/option-howgetips.php:32
msgid "Detected IP(s):"
msgstr ""

#: views/dashboard/option-howgetips.php:33
msgid "Your IP with this setting:"
msgstr ""

#: views/dashboard/option-howgetips.php:34
msgid "Edit trusted proxies"
msgstr ""

#: views/dashboard/option-howgetips.php:69
msgid "In addition to the above list, the IPs (or CIDR ranges) in the selected preset will be ignored when determining the requesting IP via the X-Forwarded-For HTTP header."
msgstr ""

#: views/dashboard/options-group-alert.php:22
msgid "Email Alert Preferences"
msgstr ""

#: views/dashboard/options-group-alert.php:37
msgid "If you have automatic updates enabled (see above), you'll get an email when an update occurs."
msgstr ""

#: views/dashboard/options-group-alert.php:79
msgid "Alert me with scan results of this severity level or greater:"
msgstr ""

#: views/dashboard/options-group-alert.php:112
msgid "Alert when someone is blocked from logging in for using a password found in a breach"
msgstr ""

#: views/dashboard/options-group-alert.php:178
msgid "0 means unlimited alerts will be sent."
msgstr ""

#: views/dashboard/options-group-dashboard.php:22
msgid "Dashboard Notification Options"
msgstr ""

#: views/dashboard/options-group-dashboard.php:106
msgid "Dashboard notifications will also be displayed for Security Alerts, Promotions, Blog Highlights, and Product Updates. These notifications can be disabled by upgrading to a premium license."
msgstr ""

#: views/dashboard/options-group-email-summary.php:22
msgid "Activity Report"
msgstr ""

#: views/dashboard/options-group-email-summary.php:38
msgid "Once a day"
msgstr ""

#: views/dashboard/options-group-email-summary.php:39
msgid "Once a week"
msgstr ""

#: views/dashboard/options-group-email-summary.php:40
msgid "Once a month"
msgstr ""

#: views/dashboard/options-group-general.php:22
msgid "General Wordfence Options"
msgstr ""

#: views/dashboard/options-group-general.php:31
msgid "Automatically updates Wordfence to the newest version within 24 hours of a new release."
msgstr ""

#: views/dashboard/options-group-general.php:34
msgid "<span class=\"wf-red-dark\">Warning:</span> You are running the LiteSpeed web server and Wordfence can't determine whether \"noabort\" is set. Please verify that the environmental variable \"noabort\" is set for the local site, or the server's global External Application Abort is set to \"No Abort\"."
msgstr ""

#: views/dashboard/options-group-general.php:36
msgid "Please read this article in our FAQ to make an important change that will ensure your site stability during an update."
msgstr ""

#: views/dashboard/options-group-general.php:56
msgid "Separate multiple addresses with commas"
msgstr ""

#: views/dashboard/options-group-general.php:74
msgid "If this option is disabled, Wordfence can look up countries for visitor IP addresses using a local database, but cannot look up regions or cities"
msgstr ""

#: views/dashboard/options-group-general.php:121
msgid "Setting higher will reduce browser traffic but slow scan starts, live traffic &amp; status updates."
msgstr ""

#: views/dashboard/options-group-general.php:146
msgid "Note: This does not include Login Security settings and tables. An option to delete those must be selected separately on the Login Security settings page."
msgstr ""

#. translators: word order may be altered as long as HTML remains around "Wordfence"
#: views/dashboard/options-group-import.php:33
msgid "Export<span class=\"wf-hidden-xs\"> Wordfence</span> Options"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "Wordfence"
#: views/dashboard/options-group-import.php:56
msgid "Import<span class=\"wf-hidden-xs\"> Wordfence</span> Options"
msgstr ""

#: views/dashboard/options-group-import.php:145
msgid "Export Successful"
msgstr ""

#: views/dashboard/options-group-import.php:146
msgid "We successfully exported your site options. To import your site options on another site, copy and paste the token below into the import text box on the destination site. Keep this token secret &mdash; it is like a password. If anyone else discovers the token it will allow them to import your options excluding your license."
msgstr ""

#: views/dashboard/options-group-import.php:155
msgid "Error during Export"
msgstr ""

#: views/dashboard/options-group-import.php:165
msgid "Import Successful"
msgstr ""

#: views/dashboard/options-group-import.php:166
msgid "We successfully imported the site options."
msgstr ""

#: views/dashboard/options-group-import.php:175
msgid "Error during Import"
msgstr ""

#: views/dashboard/options-group-license.php:22
msgid "Wordfence License"
msgstr ""

#: views/dashboard/options-group-license.php:31
msgid "Your Wordfence License"
msgstr ""

#: views/dashboard/options-group-license.php:44
msgid "License Status:"
msgstr ""

#: views/dashboard/options-group-license.php:50
msgid "%s License Expired"
msgstr ""

#: views/dashboard/options-group-license.php:53
msgid "Premium License Deactivated"
msgstr ""

#: views/dashboard/options-group-license.php:56
msgid "%s License Active"
msgstr ""

#: views/dashboard/options-group-license.php:62
msgid "Reset site to a free license"
msgstr ""

#: views/dashboard/options-group-license.php:67
msgid "Click here to manage your Wordfence licenses"
msgstr ""

#: views/dashboard/options-group-license.php:70
msgid "Remove Invalid License"
msgstr ""

#: views/dashboard/options-group-license.php:74
#: views/onboarding/registration-prompt.php:57
msgid "Install License"
msgstr ""

#: views/dashboard/options-group-license.php:163
msgid "This was a premium license key, but it is no longer valid, so premium features are disabled. You can either remove the invalid key and continue using Wordfence's free features, or enter a new premium key to upgrade. If you have questions, contact <a href=\"mailto:<EMAIL>\"><EMAIL></a>."
msgstr ""

#: views/dashboard/options-group-license.php:176
msgid "Confirm Reset"
msgstr ""

#: views/dashboard/options-group-license.php:177
msgid "<p>Are you sure you want to reset this site's Wordfence License? This will disable Premium features and return the site to the free version of Wordfence. Your settings will still be retained when reinstalling a license.</p><p>If autorenew is enabled for the current license, the license will renew at the next expiration date. If you would like to turn renewal off or assign the license to another site, log into wordfence.com to change it.</p>"
msgstr ""

#: views/dashboard/options-group-view-customization.php:22
msgid "View Customization"
msgstr ""

#: views/dashboard/options-group-view-customization.php:36
msgid "Display \"All Options\" menu item"
msgstr ""

#: views/dashboard/options-group-view-customization.php:47
msgid "Display \"Blocking\" menu item"
msgstr ""

#: views/dashboard/options-group-view-customization.php:58
msgid "Display \"Live Traffic\" menu item"
msgstr ""

#: views/dashboard/options-group-view-customization.php:69
msgid "Display \"Audit Log\" menu item"
msgstr ""

#: views/diagnostics/text.php:146
msgid "Setting Name"
msgstr ""

#: views/diagnostics/text.php:147
#: views/waf/options-group-advanced-firewall.php:138
msgid "Description"
msgstr ""

#: views/diagnostics/text.php:184
#: views/diagnostics/text.php:222
#: views/diagnostics/text.php:276
#: views/diagnostics/text.php:296
#: views/diagnostics/text.php:588
msgid "Name"
msgstr ""

#: views/diagnostics/text.php:340
msgid "Run Time"
msgstr ""

#: views/diagnostics/text.php:340
msgid "Job"
msgstr ""

#. translators: 1. Number of tables
#: views/diagnostics/text.php:419
msgid "%1$s Table in Database"
msgid_plural "%1$s Tables in Database"
msgstr[0] ""
msgstr[1] ""

#: views/diagnostics/text.php:557
msgid "Setting"
msgstr ""

#: views/gdpr/banner.php:8
msgid "Wordfence's terms of service and privacy policy have changed"
msgstr ""

#: views/gdpr/banner.php:9
#: views/gdpr/disabled-overlay.php:8
msgid "Review"
msgstr ""

#: views/gdpr/banner.php:50
msgid "We have updated our policies. To continue using Wordfence, you will need to read and agree to the <a href=\"https://www.wordfence.com/license-terms-and-conditions/\" target=\"_blank\" rel=\"noopener noreferrer\">Wordfence License Terms and Conditions<span class=\"screen-reader-text\"> (opens in new tab)</span></a>, the <a href=\"https://www.wordfence.com/services-subscription-agreement\" rel=\"noopener noreferrer\" target=\"_blank\">Services Subscription Agreement<span class=\"screen-reader-text\"> (opens in new tab)</span></a>, and <a href=\"https://www.wordfence.com/terms-of-service/\" target=\"_blank\" rel=\"noopener noreferrer\">Terms of Service<span class=\"screen-reader-text\"> (opens in new tab)</span></a>, and read and acknowledge the <a href=\"https://www.wordfence.com/privacy-policy/\" target=\"_blank\" rel=\"noopener noreferrer\">Wordfence Privacy Policy<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: views/gdpr/banner.php:51
msgid "You can log in to <a href=\"https://www.wordfence.com/\" target=\"_blank\" rel=\"noopener noreferrer\">wordfence.com<span class=\"screen-reader-text\"> (opens in new tab)</span></a> to accept the updated terms and privacy policy for all of your license keys at once."
msgstr ""

#: views/gdpr/banner.php:56
msgid "Agree"
msgstr ""

#: views/gdpr/disabled-overlay.php:7
msgid "We have updated our policies. To continue using Wordfence, you will need to read and agree to the Wordfence License Terms and Conditions, the Services Subscription Agreement, and Terms of Service, and read and acknowledge the Wordfence Privacy Policy by clicking Review."
msgstr ""

#: views/offboarding/deactivation-prompt.php:10
msgid "Deactivate Wordfence"
msgstr ""

#: views/offboarding/deactivation-prompt.php:14
msgid "You are about to deactivate Wordfence. Would you like to delete its data or keep it in place?"
msgstr ""

#: views/offboarding/deactivation-prompt.php:28
#: views/offboarding/deactivation-prompt.php:58
msgid "Deactivate and Delete Data"
msgstr ""

#: views/offboarding/deactivation-prompt.php:37
msgid "Extended Protection Still Enabled"
msgstr ""

#: views/offboarding/deactivation-prompt.php:41
msgid "The Wordfence firewall is still optimized. You should remove the firewall's extended protection before deleting to avoid PHP errors if some firewall files cannot be removed, or if PHP's \"auto_prepend_file\" setting is cached."
msgstr ""

#. translators: Support URL.
#: views/offboarding/deactivation-prompt.php:45
#: views/waf/options-group-basic-firewall.php:169
msgid "Remove Extended Protection"
msgstr ""

#: views/offboarding/deactivation-prompt.php:53
msgid "Delete Wordfence Data?"
msgstr ""

#: views/offboarding/deactivation-prompt.php:56
msgid "Are you sure you want to delete the selected Wordfence data? If you reactivate Wordfence later, deleted settings and history cannot be recovered."
msgstr ""

#: views/offboarding/deactivation-prompt.php:68
msgid "An unexpected error occurred while attempting to configure Wordfence to delete its data on deactivation."
msgstr ""

#: views/onboarding/banner.php:8
msgid "Wordfence installation is incomplete"
msgstr ""

#: views/onboarding/banner.php:11
msgid "Remind Me Later"
msgstr ""

#: views/onboarding/banner.php:20
msgid "Notice Dismissed"
msgstr ""

#: views/onboarding/banner.php:23
msgid "You will be reminded again in 12 hours."
msgstr ""

#: views/onboarding/banner.php:36
msgid "An unexpected error occurred while attempting to dismiss the notice. Please try again."
msgstr ""

#: views/onboarding/disabled-overlay.php:7
msgid "You must install a license to continue using Wordfence."
msgstr ""

#: views/onboarding/fresh-install.php:9
msgid "Wordfence - Securing your WordPress Website"
msgstr ""

#. translators: Wordfence version.
#: views/onboarding/fresh-install.php:10
msgid "You have successfully installed Wordfence %s"
msgstr ""

#: views/onboarding/modal-final-attempt.php:10
#: views/onboarding/plugin-header.php:9
msgid "Please Complete Wordfence Installation"
msgstr ""

#: views/onboarding/modal-final-attempt.php:21
msgid "Activate Premium"
msgstr ""

#: views/onboarding/modal-final-attempt.php:22
msgid "Enter your premium license key to enable real-time protection for your website."
msgstr ""

#: views/onboarding/modal-final-attempt.php:24
msgid "Enter Premium Key"
msgstr ""

#: views/onboarding/modal-final-attempt.php:26
msgid "If you don't have one, you can purchase one now."
msgstr ""

#: views/onboarding/registration-prompt.php:19
msgid "Install your license to finish activating Wordfence."
msgstr ""

#: views/onboarding/registration-prompt.php:21
msgid "Register with Wordfence to secure your site with the latest threat intelligence."
msgstr ""

#: views/onboarding/registration-prompt.php:26
msgid "Get Your Wordfence License"
msgstr ""

#: views/onboarding/registration-prompt.php:29
msgid "Install an existing license"
msgstr ""

#: views/onboarding/registration-prompt.php:39
msgid "Email"
msgstr ""

#: views/onboarding/registration-prompt.php:40
msgid "Please enter a valid email address"
msgstr ""

#: views/onboarding/registration-prompt.php:41
msgid "This is where future security alerts for your website will be sent. This can also be changed in Global Options."
msgstr ""

#: views/onboarding/registration-prompt.php:44
msgid "each Wordfence installation should have a unique key"
msgstr ""

#: views/onboarding/registration-prompt.php:48
msgid "Would you like WordPress security and vulnerability alerts sent to you via email?"
msgstr ""

#: views/onboarding/registration-prompt.php:54
msgid "You must select either \"Yes\" or \"No\""
msgstr ""

#: views/onboarding/registration-prompt.php:70
msgid "Response License Installed"
msgstr ""

#: views/onboarding/registration-prompt.php:71
msgid "Congratulations! Wordfence Response is now active on your website. Please note that some Response features are not enabled by default."
msgstr ""

#: views/onboarding/registration-prompt.php:74
msgid "Care License Installed"
msgstr ""

#: views/onboarding/registration-prompt.php:75
msgid "Congratulations! Wordfence Care is now active on your website. Please note that some Care features are not enabled by default."
msgstr ""

#: views/onboarding/registration-prompt.php:82
msgid "Free License Installed"
msgstr ""

#: views/onboarding/registration-prompt.php:83
msgid "Congratulations! Wordfence Free is now active on your website."
msgstr ""

#: views/onboarding/registration-prompt.php:99
#: views/onboarding/registration-prompt.php:122
msgid "Go To Dashboard"
msgstr ""

#: views/onboarding/registration-prompt.php:115
msgid "This key may already be used on several sites. To avoid scan scheduling issues and other problems, you should get a new key or remove it from the other sites."
msgstr ""

#. translators: 1: Registration URL.
#: views/onboarding/registration-prompt.php:116
msgid "You can <a class=\"wf-onboarding-link\" href=\"%s\" target=\"_blank\">get a new free license<span class=\"screen-reader-text\">(opens in new tab)</span></a> for this site directly, or click the \"Get a free license\" button at the top of the Licenses page when logged in to wordfence.com if you need more than one."
msgstr ""

#: views/onboarding/registration-prompt.php:130
msgid "Error Installing License"
msgstr ""

#: views/onboarding/registration-prompt.php:134
msgid "An error occurred while installing your license key."
msgstr ""

#: views/onboarding/registration-prompt.php:135
msgid "Please try again. If the problem persists, please <a href=\"https://www.wordfence.com/help/api-key\" target=\"_blank\" rel=\"noopener noreferrer\">contact Wordfence Support<span class=\"screen-reader-text\">(opens in new tab)</span></a>"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Restore"
#: views/options/block-all-options-controls.php:39
#: views/options/block-controls.php:37
msgid "<span class=\"wf-hidden-xs\">Restore </span>Defaults"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Changes"
#: views/options/block-all-options-controls.php:39
#: views/options/block-controls.php:37
msgid "Cancel<span class=\"wf-hidden-xs wf-hidden-sm\"> Changes</span>"
msgstr ""

#. translators: word order may be reversed as long as HTML remains around "Changes"
#: views/options/block-all-options-controls.php:39
#: views/options/block-controls.php:37
msgid "Save<span class=\"wf-hidden-xs wf-hidden-sm\"> Changes</span>"
msgstr ""

#: views/options/block-all-options-controls.php:84
#: views/options/block-all-options-controls.php:146
msgid "Search All Options"
msgstr ""

#: views/options/options-title.php:32
msgid "Expand All"
msgstr ""

#: views/options/options-title.php:32
msgid "Collapse All"
msgstr ""

#. translators: 1. Start date. 2. End date.
#: views/reports/activity-report-email-inline.php:13
msgid "Wordfence activity from <br><strong>%1$s</strong> to <strong>%2$s</strong>"
msgstr ""

#. translators: 1. Site URL. 2. Start date. 3. End date.
#: views/reports/activity-report-email-inline.php:130
msgid "This email was sent from your website <a href=\"%1$s\">%1$s</a> and is a summary of security related activity that Wordfence monitors for the period %2$s to %3$s."
msgstr ""

#: views/reports/activity-report-email-inline.php:134
msgid "NOTE: You are using the free version of Wordfence and are missing out on features like real-time firewall rule and malware signature updates, country blocking, and detecting if your site IP is sending spam. <a href=\"https://www.wordfence.com/zz6/\">Click here to upgrade to Wordfence Premium now</a>."
msgstr ""

#: views/reports/activity-report-email-inline.php:138
msgid "Top 10 IPs Blocked"
msgstr ""

#: views/reports/activity-report-email-inline.php:174
#: views/reports/activity-report-email-inline.php:228
#: views/reports/activity-report.php:34
#: views/reports/activity-report.php:78
msgid "(Unknown)"
msgstr ""

#: views/reports/activity-report-email-inline.php:183
#: views/reports/activity-report-email-inline.php:238
msgid "No data currently."
msgstr ""

#: views/reports/activity-report-email-inline.php:191
#: views/reports/activity-report.php:51
msgid "Update Blocked IPs"
msgstr ""

#: views/reports/activity-report-email-inline.php:196
msgid "Top 10 Countries Blocked"
msgstr ""

#: views/reports/activity-report-email-inline.php:202
#: views/reports/activity-report.php:62
msgid "Total IPs Blocked"
msgstr ""

#: views/reports/activity-report-email-inline.php:246
#: views/reports/activity-report.php:96
msgid "Update Blocked Countries"
msgstr ""

#: views/reports/activity-report-email-inline.php:251
msgid "Top 10 Failed Logins"
msgstr ""

#: views/reports/activity-report-email-inline.php:258
#: views/reports/activity-report.php:108
msgid "Existing User"
msgstr ""

#: views/reports/activity-report-email-inline.php:276
#: views/reports/activity-report.php:123
msgid "No failed logins yet."
msgstr ""

#: views/reports/activity-report-email-inline.php:284
#: views/reports/activity-report.php:131
msgid "Update Login Security Options"
msgstr ""

#: views/reports/activity-report-email-inline.php:289
msgid "Recently Blocked Attacks"
msgstr ""

#: views/reports/activity-report-email-inline.php:295
msgid "IP / Action"
msgstr ""

#: views/reports/activity-report-email-inline.php:316
msgid "No blocked attacks yet."
msgstr ""

#: views/reports/activity-report-email-inline.php:326
msgid "and %d additional attacks"
msgstr ""

#: views/reports/activity-report-email-inline.php:330
msgid "View Recent Traffic"
msgstr ""

#: views/reports/activity-report-email-inline.php:335
msgid "Recently Modified Files"
msgstr ""

#: views/reports/activity-report-email-inline.php:340
msgid "Modified"
msgstr ""

#: views/reports/activity-report-email-inline.php:361
msgid "This list may include WordPress core/plugin/theme updates, error logs, cache files, and other normal changes."
msgstr ""

#: views/reports/activity-report-email-inline.php:365
#: views/reports/activity-report.php:164
msgid "Updates Needed"
msgstr ""

#: views/reports/activity-report-email-inline.php:373
#: views/reports/activity-report.php:172
msgid "Core"
msgstr ""

#. translators: WordPress version.
#: views/reports/activity-report-email-inline.php:375
#: views/reports/activity-report.php:174
msgid "A new version of WordPress (v%s) is available."
msgstr ""

#: views/reports/activity-report-email-inline.php:379
#: views/reports/activity-report.php:178
msgid "Plugins"
msgstr ""

#. translators: Plugin name.
#. translators: Plugin version.
#: views/reports/activity-report-email-inline.php:386
#: views/reports/activity-report.php:185
msgid "A new version of the plugin \"%s\" is available."
msgstr ""

#. translators: Plugin name.
#. translators: Theme name.
#: views/reports/activity-report-email-inline.php:386
#: views/reports/activity-report-email-inline.php:399
msgid "<strong>This update includes security-related fixes.</strong>"
msgstr ""

#. translators: Theme name.
#. translators: Theme version.
#: views/reports/activity-report-email-inline.php:399
#: views/reports/activity-report.php:198
msgid "A new version of the theme \"%s\" is available."
msgstr ""

#: views/reports/activity-report-email-inline.php:407
#: views/reports/activity-report.php:205
msgid "Update Now"
msgstr ""

#: views/reports/activity-report-email-inline.php:411
#: views/reports/activity-report.php:207
msgid "No updates are available at this time."
msgstr ""

#. translators: 1. Site URL. 2. WordPress admin panel URL. 3. WordPress admin panel URL.
#: views/reports/activity-report-email-inline.php:418
msgid "If you would like to sign-in to <a href=\"%1$s\">%1$s</a> please <a href=\"%2$s\">click here</a> now. You can change the frequency of this email or turn it on and off by visiting your <a href=\"%3$s\">Wordfence options page</a>."
msgstr ""

#. translators: Number of IPs.
#: views/reports/activity-report.php:9
msgid "Top %d IPs Blocked"
msgstr ""

#: views/reports/activity-report.php:43
msgid "No IPs blocked yet."
msgstr ""

#. translators: Number of countries.
#: views/reports/activity-report.php:56
msgid "Top %d Countries Blocked"
msgstr ""

#: views/reports/activity-report.php:88
msgid "No requests blocked yet."
msgstr ""

#. translators: Number of failed logins.
#: views/reports/activity-report.php:101
msgid "Top %d Failed Logins"
msgstr ""

#. translators: Seconds with millisecond precision.
#: views/reports/activity-report.php:210
msgid "Generated in %.4f seconds"
msgstr ""

#: views/scanner/issue-base.php:31
msgid "Issue Found "
msgstr ""

#: views/scanner/issue-base.php:40
msgid "Found "
msgstr ""

#: views/scanner/issue-base.php:52
msgid "New"
msgstr ""

#: views/scanner/issue-base.php:52
#: views/scanner/issue-base.php:100
msgid "Ignored"
msgstr ""

#: views/scanner/issue-base.php:53
msgid "Issue First Detected"
msgstr ""

#: views/scanner/issue-base.php:53
msgid "ago"
msgstr ""

#. translators: Localized date.
#: views/scanner/issue-base.php:102
msgid "Issue Found: %s"
msgstr ""

#. translators: Severity level.
#: views/scanner/issue-base.php:122
msgid "Severity: %s"
msgstr ""

#: views/scanner/issue-checkGSB.php:10
#: views/scanner/issue-checkHowGetIPs.php:10
#: views/scanner/issue-checkSpamIP.php:10
#: views/scanner/issue-configReadable.php:10
#: views/scanner/issue-control-ignore.php:7
#: views/scanner/issue-diskSpace.php:10
#: views/scanner/issue-geoipSupport.php:10
#: views/scanner/issue-publiclyAccessible.php:10
#: views/scanner/issue-skippedPaths.php:14
#: views/scanner/issue-spamvertizeCheck.php:10
#: views/scanner/issue-suspiciousAdminUsers.php:10
#: views/scanner/issue-timelimit.php:10
#: views/scanner/issue-wafStatus.php:10
#: views/scanner/issue-wfPluginAbandoned.php:10
#: views/scanner/issue-wfPluginRemoved.php:10
#: views/scanner/issue-wfPluginVulnerable.php:10
#: views/scanner/issue-wfUpgrade.php:10
msgid "Ignore"
msgstr ""

#: views/scanner/issue-checkGSB.php:12
#: views/scanner/issue-checkGSB.php:21
#: views/scanner/issue-commentBadURL.php:13
#: views/scanner/issue-commentBadURL.php:28
#: views/scanner/issue-database.php:13
#: views/scanner/issue-database.php:25
#: views/scanner/issue-file.php:14
#: views/scanner/issue-file.php:30
#: views/scanner/issue-knownfile.php:14
#: views/scanner/issue-knownfile.php:29
#: views/scanner/issue-optionBadURL.php:13
#: views/scanner/issue-optionBadURL.php:27
#: views/scanner/issue-postBadURL.php:13
#: views/scanner/issue-postBadURL.php:28
msgid "Bad URL"
msgstr ""

#: views/scanner/issue-checkGSB.php:14
#: views/scanner/issue-checkGSB.php:23
#: views/scanner/issue-checkHowGetIPs.php:12
#: views/scanner/issue-checkHowGetIPs.php:20
#: views/scanner/issue-checkSpamIP.php:12
#: views/scanner/issue-checkSpamIP.php:19
#: views/scanner/issue-commentBadURL.php:16
#: views/scanner/issue-commentBadURL.php:31
#: views/scanner/issue-configReadable.php:14
#: views/scanner/issue-configReadable.php:25
#: views/scanner/issue-control-show-details.php:4
#: views/scanner/issue-coreUnknown.php:11
#: views/scanner/issue-coreUnknown.php:19
#: views/scanner/issue-database.php:15
#: views/scanner/issue-database.php:27
#: views/scanner/issue-diskSpace.php:14
#: views/scanner/issue-diskSpace.php:23
#: views/scanner/issue-easyPassword.php:16
#: views/scanner/issue-easyPassword.php:27
#: views/scanner/issue-file.php:16
#: views/scanner/issue-file.php:32
#: views/scanner/issue-geoipSupport.php:12
#: views/scanner/issue-geoipSupport.php:19
#: views/scanner/issue-knownfile.php:16
#: views/scanner/issue-knownfile.php:31
#: views/scanner/issue-optionBadURL.php:15
#: views/scanner/issue-optionBadURL.php:29
#: views/scanner/issue-postBadTitle.php:15
#: views/scanner/issue-postBadTitle.php:29
#: views/scanner/issue-postBadURL.php:16
#: views/scanner/issue-postBadURL.php:31
#: views/scanner/issue-publiclyAccessible.php:14
#: views/scanner/issue-publiclyAccessible.php:25
#: views/scanner/issue-skippedPaths.php:16
#: views/scanner/issue-skippedPaths.php:24
#: views/scanner/issue-spamvertizeCheck.php:12
#: views/scanner/issue-spamvertizeCheck.php:19
#: views/scanner/issue-suspiciousAdminUsers.php:12
#: views/scanner/issue-suspiciousAdminUsers.php:22
#: views/scanner/issue-timelimit.php:12
#: views/scanner/issue-timelimit.php:19
#: views/scanner/issue-wafStatus.php:14
#: views/scanner/issue-wafStatus.php:23
#: views/scanner/issue-wfPluginAbandoned.php:16
#: views/scanner/issue-wfPluginAbandoned.php:33
#: views/scanner/issue-wfPluginRemoved.php:15
#: views/scanner/issue-wfPluginRemoved.php:30
#: views/scanner/issue-wfPluginUpgrade.php:16
#: views/scanner/issue-wfPluginUpgrade.php:33
#: views/scanner/issue-wfPluginVulnerable.php:15
#: views/scanner/issue-wfPluginVulnerable.php:30
#: views/scanner/issue-wfThemeUpgrade.php:16
#: views/scanner/issue-wfThemeUpgrade.php:32
#: views/scanner/issue-wfUpgrade.php:15
#: views/scanner/issue-wfUpgrade.php:29
#: views/scanner/issue-wfUpgradeError.php:12
#: views/scanner/issue-wfUpgradeError.php:20
#: views/scanner/issue-wpscan_directoryList.php:14
#: views/scanner/issue-wpscan_directoryList.php:25
#: views/scanner/issue-wpscan_fullPathDiscl.php:14
#: views/scanner/issue-wpscan_fullPathDiscl.php:25
msgid "Details"
msgstr ""

#: views/scanner/issue-checkGSB.php:17
#: views/scanner/issue-checkHowGetIPs.php:16
#: views/scanner/issue-checkSpamIP.php:15
#: views/scanner/issue-commentBadURL.php:23
#: views/scanner/issue-configReadable.php:19
#: views/scanner/issue-coreUnknown.php:15
#: views/scanner/issue-database.php:20
#: views/scanner/issue-diskSpace.php:17
#: views/scanner/issue-easyPassword.php:19
#: views/scanner/issue-file.php:22
#: views/scanner/issue-geoipSupport.php:15
#: views/scanner/issue-knownfile.php:22
#: views/scanner/issue-optionBadURL.php:22
#: views/scanner/issue-postBadTitle.php:22
#: views/scanner/issue-postBadURL.php:23
#: views/scanner/issue-publiclyAccessible.php:19
#: views/scanner/issue-skippedPaths.php:20
#: views/scanner/issue-spamvertizeCheck.php:15
#: views/scanner/issue-suspiciousAdminUsers.php:18
#: views/scanner/issue-timelimit.php:15
#: views/scanner/issue-wafStatus.php:17
#: views/scanner/issue-wfPluginAbandoned.php:24
#: views/scanner/issue-wfPluginRemoved.php:22
#: views/scanner/issue-wfPluginUpgrade.php:24
#: views/scanner/issue-wfPluginVulnerable.php:22
#: views/scanner/issue-wfThemeUpgrade.php:23
#: views/scanner/issue-wfUpgrade.php:21
#: views/scanner/issue-wfUpgradeError.php:15
#: views/scanner/issue-wpscan_directoryList.php:19
#: views/scanner/issue-wpscan_fullPathDiscl.php:19
msgid "Mark as Fixed"
msgstr ""

#: views/scanner/issue-checkHowGetIPs.php:15
msgid "Use Recommended Value"
msgstr ""

#: views/scanner/issue-checkSpamIP.php:8
#: views/scanner/issue-spamvertizeCheck.php:8
msgid "Spam"
msgstr ""

#: views/scanner/issue-commentBadURL.php:12
#: views/scanner/issue-commentBadURL.php:27
msgid "Author"
msgstr ""

#: views/scanner/issue-commentBadURL.php:14
#: views/scanner/issue-commentBadURL.php:29
#: views/scanner/issue-postBadTitle.php:13
#: views/scanner/issue-postBadTitle.php:27
#: views/scanner/issue-postBadURL.php:14
#: views/scanner/issue-postBadURL.php:29
msgid "Posted on"
msgstr ""

#: views/scanner/issue-commentBadURL.php:18
#: views/scanner/issue-commentBadURL.php:33
#: views/scanner/issue-optionBadURL.php:17
#: views/scanner/issue-optionBadURL.php:31
#: views/scanner/issue-postBadTitle.php:17
#: views/scanner/issue-postBadTitle.php:31
#: views/scanner/issue-postBadURL.php:18
#: views/scanner/issue-postBadURL.php:33
msgid "Multisite Blog ID"
msgstr ""

#: views/scanner/issue-commentBadURL.php:19
#: views/scanner/issue-commentBadURL.php:34
#: views/scanner/issue-optionBadURL.php:18
#: views/scanner/issue-optionBadURL.php:32
#: views/scanner/issue-postBadTitle.php:18
#: views/scanner/issue-postBadTitle.php:32
#: views/scanner/issue-postBadURL.php:19
#: views/scanner/issue-postBadURL.php:34
msgid "Multisite Blog Domain"
msgstr ""

#: views/scanner/issue-commentBadURL.php:20
#: views/scanner/issue-commentBadURL.php:35
#: views/scanner/issue-optionBadURL.php:19
#: views/scanner/issue-optionBadURL.php:33
#: views/scanner/issue-postBadTitle.php:19
#: views/scanner/issue-postBadTitle.php:33
#: views/scanner/issue-postBadURL.php:20
#: views/scanner/issue-postBadURL.php:35
msgid "Multisite Blog Path"
msgstr ""

#: views/scanner/issue-configReadable.php:8
msgid "Publicly Accessible Config/Backup/Log"
msgstr ""

#: views/scanner/issue-configReadable.php:17
#: views/scanner/issue-coreUnknown.php:13
#: views/scanner/issue-file.php:19
#: views/scanner/issue-knownfile.php:19
#: views/scanner/issue-publiclyAccessible.php:17
#: views/scanner/issue-wpscan_directoryList.php:17
#: views/scanner/issue-wpscan_fullPathDiscl.php:17
msgid "View File"
msgstr ""

#: views/scanner/issue-configReadable.php:18
#: views/scanner/issue-coreUnknown.php:14
#: views/scanner/issue-file.php:21
#: views/scanner/issue-knownfile.php:21
#: views/scanner/issue-publiclyAccessible.php:18
#: views/scanner/issue-wpscan_directoryList.php:18
#: views/scanner/issue-wpscan_fullPathDiscl.php:18
msgid "Delete File"
msgstr ""

#: views/scanner/issue-control-edit-comment.php:4
#: views/scanner/issue-control-edit-post.php:4
#: views/scanner/issue-control-edit-user.php:4
msgid "Edit"
msgstr ""

#: views/scanner/issue-control-hide-file.php:4
msgid "Hide File"
msgstr ""

#: views/scanner/issue-control-ignore.php:7
msgid "Stop Ignoring"
msgstr ""

#: views/scanner/issue-control-repair.php:4
msgid "Repair"
msgstr ""

#: views/scanner/issue-coreUnknown.php:8
msgid "Unknown Core File"
msgstr ""

#: views/scanner/issue-coreUnknown.php:10
msgid "Always Ignore Version"
msgstr ""

#: views/scanner/issue-coreUnknown.php:10
msgid "Ignore Until Version Changes"
msgstr ""

#: views/scanner/issue-database.php:8
msgid "Option"
msgstr ""

#: views/scanner/issue-database.php:10
msgid "Ignore Value"
msgstr ""

#: views/scanner/issue-database.php:10
#: views/scanner/issue-optionBadURL.php:10
msgid "Ignore Option"
msgstr ""

#: views/scanner/issue-database.php:12
#: views/scanner/issue-database.php:24
#: views/scanner/issue-optionBadURL.php:12
#: views/scanner/issue-optionBadURL.php:26
msgid "Option Name"
msgstr ""

#: views/scanner/issue-database.php:18
msgid "View Option"
msgstr ""

#: views/scanner/issue-database.php:19
msgid "Delete Option"
msgstr ""

#: views/scanner/issue-diskSpace.php:8
msgid "Disk Space"
msgstr ""

#: views/scanner/issue-diskSpace.php:12
#: views/scanner/issue-diskSpace.php:21
msgid "Space Remaining"
msgstr ""

#: views/scanner/issue-easyPassword.php:8
msgid "Insecure Password"
msgstr ""

#: views/scanner/issue-easyPassword.php:10
msgid "Ignore All for User"
msgstr ""

#: views/scanner/issue-easyPassword.php:10
msgid "Ignore Only this Password"
msgstr ""

#: views/scanner/issue-easyPassword.php:12
#: views/scanner/issue-easyPassword.php:23
msgid "Login Name"
msgstr ""

#: views/scanner/issue-easyPassword.php:13
#: views/scanner/issue-easyPassword.php:24
msgid "User Email"
msgstr ""

#: views/scanner/issue-easyPassword.php:14
#: views/scanner/issue-easyPassword.php:25
msgid "Full Name"
msgstr ""

#: views/scanner/issue-file.php:10
#: views/scanner/issue-knownfile.php:10
#: views/scanner/issue-postBadTitle.php:10
#: views/scanner/issue-postBadURL.php:10
msgid "Always Ignore"
msgstr ""

#: views/scanner/issue-file.php:10
#: views/scanner/issue-knownfile.php:10
msgid "Ignore Until File Changes"
msgstr ""

#: views/scanner/issue-file.php:12
#: views/scanner/issue-file.php:26
#: views/scanner/issue-knownfile.php:12
#: views/scanner/issue-knownfile.php:26
msgid "Filename"
msgstr ""

#: views/scanner/issue-file.php:13
#: views/scanner/issue-file.php:27
#: views/scanner/issue-file.php:28
#: views/scanner/issue-file.php:29
#: views/scanner/issue-knownfile.php:13
#: views/scanner/issue-knownfile.php:27
#: views/scanner/issue-knownfile.php:28
msgid "File Type"
msgstr ""

#: views/scanner/issue-file.php:13
msgid "WordPress Configuration File"
msgstr ""

#: views/scanner/issue-file.php:13
#: views/scanner/issue-knownfile.php:13
msgid "Not a core, theme, or plugin file from wordpress.org"
msgstr ""

#: views/scanner/issue-file.php:16
msgid "This is your main configuration file and cannot be deleted. It must be cleaned manually."
msgstr ""

#: views/scanner/issue-file.php:20
#: views/scanner/issue-knownfile.php:20
msgid "View Differences"
msgstr ""

#: views/scanner/issue-geoipSupport.php:8
msgid "Server Update"
msgstr ""

#: views/scanner/issue-optionBadURL.php:10
msgid "Ignore URL"
msgstr ""

#: views/scanner/issue-postBadTitle.php:8
msgid "Post"
msgstr ""

#: views/scanner/issue-postBadTitle.php:10
msgid "Ignore Only this Title"
msgstr ""

#: views/scanner/issue-postBadTitle.php:12
#: views/scanner/issue-postBadTitle.php:26
#: views/scanner/issue-postBadURL.php:12
#: views/scanner/issue-postBadURL.php:27
msgid "Title"
msgstr ""

#: views/scanner/issue-postBadURL.php:10
msgid "Ignore this URL"
msgstr ""

#: views/scanner/issue-publiclyAccessible.php:8
msgid "Quarantined File"
msgstr ""

#: views/scanner/issue-skippedPaths.php:12
msgid "Skipped Paths"
msgstr ""

#: views/scanner/issue-skippedPaths.php:19
msgid "Go To Option"
msgstr ""

#: views/scanner/issue-suspiciousAdminUsers.php:8
msgid "Unknown Administrator"
msgstr ""

#: views/scanner/issue-suspiciousAdminUsers.php:15
msgid "Delete User"
msgstr ""

#: views/scanner/issue-suspiciousAdminUsers.php:16
msgid "Revoke Capabilities"
msgstr ""

#: views/scanner/issue-suspiciousAdminUsers.php:17
msgid "Acknowledge User"
msgstr ""

#: views/scanner/issue-timelimit.php:8
msgid "Time Limit"
msgstr ""

#: views/scanner/issue-wafStatus.php:8
msgid "WAF Status"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:8
msgid "Plugin Abandoned"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:12
#: views/scanner/issue-wfPluginAbandoned.php:29
#: views/scanner/issue-wfPluginRemoved.php:12
#: views/scanner/issue-wfPluginRemoved.php:27
#: views/scanner/issue-wfPluginUpgrade.php:12
#: views/scanner/issue-wfPluginUpgrade.php:29
#: views/scanner/issue-wfPluginVulnerable.php:12
#: views/scanner/issue-wfPluginVulnerable.php:27
msgid "Plugin Name"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:13
#: views/scanner/issue-wfPluginAbandoned.php:30
#: views/scanner/issue-wfPluginRemoved.php:13
#: views/scanner/issue-wfPluginRemoved.php:28
#: views/scanner/issue-wfPluginUpgrade.php:13
#: views/scanner/issue-wfPluginUpgrade.php:30
#: views/scanner/issue-wfPluginVulnerable.php:13
#: views/scanner/issue-wfPluginVulnerable.php:28
msgid "Current Plugin Version"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:14
#: views/scanner/issue-wfPluginAbandoned.php:31
msgid "Last Updated"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:16
#: views/scanner/issue-wfPluginAbandoned.php:34
msgid "Plugin has unpatched security issues."
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:18
#: views/scanner/issue-wfPluginAbandoned.php:36
#: views/scanner/issue-wfPluginRemoved.php:17
#: views/scanner/issue-wfPluginRemoved.php:32
#: views/scanner/issue-wfPluginUpgrade.php:18
#: views/scanner/issue-wfPluginUpgrade.php:36
msgid "Plugin URL"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:19
#: views/scanner/issue-wfPluginAbandoned.php:37
#: views/scanner/issue-wfPluginUpgrade.php:37
#: views/scanner/issue-wfPluginVulnerable.php:17
msgid "Repository URL"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:25
#: views/scanner/issue-wfPluginRemoved.php:23
#: views/scanner/issue-wfPluginVulnerable.php:23
msgid "Manage Plugins"
msgstr ""

#: views/scanner/issue-wfPluginAbandoned.php:34
#: views/scanner/issue-wfPluginUpgrade.php:34
#: views/scanner/issue-wfThemeUpgrade.php:33
#: views/scanner/issue-wfUpgrade.php:30
msgid "Vulnerability Status"
msgstr ""

#: views/scanner/issue-wfPluginRemoved.php:8
msgid "Plugin Removed"
msgstr ""

#: views/scanner/issue-wfPluginUpgrade.php:8
msgid "Plugin Upgrade"
msgstr ""

#: views/scanner/issue-wfPluginUpgrade.php:10
#: views/scanner/issue-wfUpgradeError.php:10
msgid "Ignore Update"
msgstr ""

#: views/scanner/issue-wfPluginUpgrade.php:14
#: views/scanner/issue-wfPluginUpgrade.php:31
msgid "New Plugin Version"
msgstr ""

#: views/scanner/issue-wfPluginUpgrade.php:16
#: views/scanner/issue-wfThemeUpgrade.php:16
#: views/scanner/issue-wfUpgrade.php:15
msgid "Click here to update now"
msgstr ""

#: views/scanner/issue-wfPluginUpgrade.php:19
msgid "Changelog"
msgstr ""

#: views/scanner/issue-wfPluginUpgrade.php:25
#: views/scanner/issue-wfThemeUpgrade.php:24
#: views/scanner/issue-wfUpgrade.php:22
#: views/scanner/issue-wfUpgradeError.php:16
msgid "View Updates"
msgstr ""

#: views/scanner/issue-wfPluginVulnerable.php:8
msgid "Plugin Vulnerable"
msgstr ""

#: views/scanner/issue-wfThemeUpgrade.php:8
msgid "Theme Upgrade"
msgstr ""

#: views/scanner/issue-wfThemeUpgrade.php:10
msgid "Ignore "
msgstr ""

#: views/scanner/issue-wfThemeUpgrade.php:12
#: views/scanner/issue-wfThemeUpgrade.php:28
msgid "Theme Name"
msgstr ""

#: views/scanner/issue-wfThemeUpgrade.php:13
#: views/scanner/issue-wfThemeUpgrade.php:29
msgid "Current Theme Version"
msgstr ""

#: views/scanner/issue-wfThemeUpgrade.php:14
#: views/scanner/issue-wfThemeUpgrade.php:30
msgid "New Theme Version"
msgstr ""

#: views/scanner/issue-wfThemeUpgrade.php:18
#: views/scanner/issue-wfThemeUpgrade.php:35
msgid "Theme URL"
msgstr ""

#: views/scanner/issue-wfUpgrade.php:8
msgid "Core Upgrade"
msgstr ""

#: views/scanner/issue-wfUpgrade.php:12
#: views/scanner/issue-wfUpgrade.php:26
msgid "Current WordPress Version"
msgstr ""

#: views/scanner/issue-wfUpgrade.php:13
#: views/scanner/issue-wfUpgrade.php:27
msgid "New WordPress Version"
msgstr ""

#: views/scanner/issue-wfUpgradeError.php:8
msgid "Update Check Error"
msgstr ""

#: views/scanner/issue-wpscan_directoryList.php:8
msgid "Directory Listing Enabled"
msgstr ""

#: views/scanner/option-scan-signatures.php:27
msgid "Add Additional Signatures"
msgstr ""

#: views/scanner/options-group-advanced.php:23
msgid "Advanced Scan Options"
msgstr ""

#: views/scanner/options-group-advanced.php:60
msgid "This option requires cURL. (This may have no effect on some old PHP or cURL versions.)"
msgstr ""

#: views/scanner/options-group-basic.php:23
msgid "Basic Scan Type Options"
msgstr ""

#: views/scanner/options-group-general.php:23
msgid "General Options"
msgstr ""

#: views/scanner/options-group-general.php:32
#: views/scanner/options-group-general.php:33
#: views/scanner/options-group-general.php:34
msgid "<em>Reputation check</em>"
msgstr ""

#: views/scanner/options-group-performance.php:23
msgid "Performance Options"
msgstr ""

#: views/scanner/options-group-performance.php:33
msgid "0 or empty means unlimited issues will be sent"
msgstr ""

#. translators: Time until.
#: views/scanner/options-group-performance.php:34
msgid "0 or empty means the default of %s will be used"
msgstr ""

#. translators: Time until.
#: views/scanner/options-group-performance.php:35
msgid "Memory size in megabytes"
msgstr ""

#. translators: PHP max execution time (number).
#: views/scanner/options-group-performance.php:36
msgid "0 for default. Must be %d or greater and 10-20 or higher is recommended for most servers"
msgstr ""

#: views/scanner/options-group-scan-schedule.php:23
msgid "Scan Scheduling"
msgstr ""

#: views/scanner/scan-failed.php:15
msgid "Scan Failed"
msgstr ""

#: views/scanner/scan-failed.php:26
msgid "The error returned was:"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "activity"
#: views/scanner/scan-progress-detailed.php:16
msgid "Email<span class=\"wf-hidden-xs\"> activity</span> log"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "full"
#: views/scanner/scan-progress-detailed.php:16
msgid "View<span class=\"wf-hidden-xs\"> full</span> log"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "full"
#: views/scanner/scan-progress-detailed.php:16
msgid "Hide log"
msgstr ""

#. translators: word order may be altered as long as HTML remains around "full"
#: views/scanner/scan-progress-detailed.php:16
msgid "Show log"
msgstr ""

#: views/scanner/scan-progress-element.php:59
msgid "Upgrade"
msgstr ""

#: views/scanner/scan-progress.php:18
msgid "Spamvertising Checks"
msgstr ""

#: views/scanner/scan-progress.php:25
msgid "Spam Check"
msgstr ""

#: views/scanner/scan-progress.php:32
msgid "Blocklist Check"
msgstr ""

#: views/scanner/scan-progress.php:39
msgid "Server State"
msgstr ""

#: views/scanner/scan-progress.php:46
msgid "File Changes"
msgstr ""

#: views/scanner/scan-progress.php:53
msgid "Malware Scan"
msgstr ""

#: views/scanner/scan-progress.php:60
msgid "Content Safety"
msgstr ""

#: views/scanner/scan-progress.php:67
msgid "Public Files"
msgstr ""

#: views/scanner/scan-progress.php:74
msgid "Password Strength"
msgstr ""

#: views/scanner/scan-progress.php:81
msgid "Vulnerability Scan"
msgstr ""

#: views/scanner/scan-progress.php:88
msgid "User & Option Audit"
msgstr ""

#: views/scanner/scan-results.php:16
msgid "Results<span class=\"wf-hidden-xs\"> Found</span>"
msgstr ""

#: views/scanner/scan-results.php:17
msgid "Ignored<span class=\"wf-hidden-xs\"> Results</span>"
msgstr ""

#: views/scanner/scan-results.php:30
msgid "Delete All Deletable Files"
msgstr ""

#: views/scanner/scan-results.php:30
msgid "Repair All Repairable Files"
msgstr ""

#: views/scanner/scan-results.php:39
#: views/scanner/scan-results.php:71
msgid "Posts, Comments, &amp; Files"
msgstr ""

#: views/scanner/scan-results.php:45
#: views/scanner/scan-results.php:77
msgid "Themes &amp; Plugins"
msgstr ""

#: views/scanner/scan-results.php:51
#: views/scanner/scan-results.php:83
msgid "Users Checked"
msgstr ""

#: views/scanner/scan-results.php:57
#: views/scanner/scan-results.php:89
msgid "URLs Checked"
msgstr ""

#: views/scanner/scan-results.php:63
#: views/scanner/scan-results.php:95
msgid "Results Found"
msgstr ""

#: views/scanner/scan-scheduling.php:29
msgid "Let Wordfence choose when to scan my site (recommended)"
msgstr ""

#: views/scanner/scan-scheduling.php:38
msgid "Manually schedule scans"
msgstr ""

#: views/scanner/scan-scheduling.php:44
msgid "Once Daily"
msgstr ""

#: views/scanner/scan-scheduling.php:45
msgid "Twice Daily"
msgstr ""

#: views/scanner/scan-scheduling.php:46
msgid "Every Other Day"
msgstr ""

#: views/scanner/scan-scheduling.php:47
msgid "Weekdays"
msgstr ""

#: views/scanner/scan-scheduling.php:48
msgid "Weekends"
msgstr ""

#: views/scanner/scan-scheduling.php:49
msgid "Odd Days & Weekends"
msgstr ""

#: views/scanner/scan-scheduling.php:190
msgid "Use preferred start time"
msgstr ""

#: views/scanner/scan-scheduling.php:190
msgid "Start time"
msgstr ""

#: views/scanner/scan-scheduling.php:202
#: views/scanner/scan-scheduling.php:242
msgid "AM"
msgstr ""

#: views/scanner/scan-scheduling.php:205
#: views/scanner/scan-scheduling.php:253
msgid "PM"
msgstr ""

#: views/scanner/scan-scheduling.php:223
msgid "Monday"
msgstr ""

#: views/scanner/scan-scheduling.php:224
msgid "Tuesday"
msgstr ""

#: views/scanner/scan-scheduling.php:225
msgid "Wednesday"
msgstr ""

#: views/scanner/scan-scheduling.php:226
msgid "Thursday"
msgstr ""

#: views/scanner/scan-scheduling.php:227
msgid "Friday"
msgstr ""

#: views/scanner/scan-scheduling.php:228
msgid "Saturday"
msgstr ""

#: views/scanner/scan-scheduling.php:229
msgid "Sunday"
msgstr ""

#: views/scanner/scan-starter.php:13
msgid "Start New Scan"
msgstr ""

#: views/scanner/scan-starter.php:14
msgid "Stop Scan"
msgstr ""

#: views/scanner/scan-starter.php:29
msgid "Scan Stopping"
msgstr ""

#: views/scanner/scan-starter.php:29
msgid "Stop Failed"
msgstr ""

#: views/scanner/scan-starter.php:29
msgid "A termination request has been sent to stop any running scans."
msgstr ""

#: views/scanner/scan-starter.php:29
msgid "We failed to send a termination request."
msgstr ""

#: views/scanner/scan-type.php:21
msgid "For entry-level hosting plans. Provides limited detection capability with very low resource utilization."
msgstr ""

#: views/scanner/scan-type.php:27
msgid "Our recommendation for all websites. Provides the best detection capability in the industry."
msgstr ""

#: views/scanner/scan-type.php:33
msgid "For site owners who think they may have been hacked. More thorough but may produce false positives."
msgstr ""

#: views/scanner/scan-type.php:39
msgid "Selected automatically when General Options have been customized for this website."
msgstr ""

#: views/scanner/scanner-status.php:19
msgid "Wordfence Scan &amp; Response License Enabled"
msgstr ""

#: views/scanner/scanner-status.php:22
msgid "Wordfence Scan &amp; Care License Enabled"
msgstr ""

#: views/scanner/scanner-status.php:25
msgid "Wordfence Scan &amp; Premium Enabled"
msgstr ""

#: views/scanner/scanner-status.php:35
msgid "Wordfence Scan Deactivated"
msgstr ""

#: views/scanner/scanner-status.php:36
msgid "A Wordfence scan examines all files, posts, pages, and comments on your WordPress website looking for malware, known malicious URLs, and known patterns of infections. It also does several other reputation and server checks."
msgstr ""

#: views/scanner/scanner-status.php:38
msgid "Enable Automatic Scans"
msgstr ""

#: views/scanner/scanner-status.php:58
msgid "Wordfence Scan Enabled"
msgstr ""

#: views/scanner/scanner-status.php:66
msgid "As a free Wordfence user, you are currently using the Community version of the Threat Defense Feed. Premium users are protected by additional firewall rules and malware signatures as well as the Wordfence real-time IP blocklist. Upgrade to Premium today to improve your protection."
msgstr ""

#: views/scanner/scanner-status.php:72
#: views/waf/firewall-status.php:85
msgid "Premium Protection Enabled"
msgstr ""

#: views/scanner/site-cleaning-beta-sigs.php:13
#: views/scanner/site-cleaning-high-sense.php:13
msgid "The results of your scan may indicate that you could benefit from Wordfence Site Cleaning"
msgstr ""

#: views/scanner/site-cleaning-beta-sigs.php:14
msgid "Since you have the beta threat defense feed enabled, there is a high likelihood that your results could include false positives. If you think you have indeed been hacked, our team of security experts can help."
msgstr ""

#: views/scanner/site-cleaning-bottom.php:12
msgid "Need help from the WordPress security experts?"
msgstr ""

#: views/scanner/site-cleaning-bottom.php:14
msgid "Sign up for Wordfence Care today and get expert help with any security issue. Wordfence Care is for business owners who put a premium on their time. With Wordfence Care, we take care of it, so that you can focus on your customers. Check out Wordfence Response for mission-critical WordPress websites."
msgstr ""

#: views/scanner/site-cleaning-bottom.php:18
#: views/scanner/site-cleaning.php:21
msgid "Learn More About Wordfence Care"
msgstr ""

#: views/scanner/site-cleaning-bottom.php:19
msgid "Learn More About Wordfence Response"
msgstr ""

#: views/scanner/site-cleaning-high-sense.php:14
msgid "Since you have High Sensitivity scanning enabled, there is a very high likelihood that your results will include false positives. If you think you have indeed been hacked, our team of security experts can help."
msgstr ""

#: views/scanner/site-cleaning.php:13
msgid "Need help with a security issue?"
msgstr ""

#: views/scanner/site-cleaning.php:15
msgid "Sign up for Wordfence Care and we’ll take care of it. Our security experts will clean your site, resolve the infection, and perform a security audit. Get hands-on support with any security issue including help installing, configuring and optimizing Wordfence."
msgstr ""

#: views/tools/options-group-2fa.php:24
msgid "Two-Factor Authentication Options"
msgstr ""

#. translators: Support URL.
#: views/tools/options-group-2fa.php:51
msgid "<strong>Require Cellphone Sign-in for all Administrators<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wfhelp wf-inline-help\"><span class=\"screen-reader-text\"> (opens in new tab)</span></a></strong><br><em>Note:</em> This setting requires at least one administrator to have Cellphone Sign-in enabled. On multisite, this option applies only to super admins."
msgstr ""

#. translators: Support URL.
#: views/tools/options-group-2fa.php:63
msgid "<strong>Enable Separate Prompt for Two-Factor Code<a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"wfhelp wf-inline-help\"><span class=\"screen-reader-text\"> (opens in new tab)</span></a></strong><br><em>Note:</em> This setting changes the behavior for obtaining the two-factor authentication code from using the password field to showing a separate prompt. If your theme overrides the default login page, you may not be able to use this option."
msgstr ""

#. translators: Support URL.
#: views/tools/options-group-2fa.php:64
msgid "<br><strong>This setting will be ignored because the PHP configuration option <code>output_buffering</code> is off.</strong>"
msgstr ""

#: views/tools/options-group-audit-log.php:37
msgid "Audit Log Options"
msgstr ""

#: views/tools/options-group-audit-log.php:45
msgid "These options let you choose which site events to record in the audit log. When enabled and your site is connected to Wordfence Central, these events are automatically sent to Central to prevent any tampering by an attacker. When <strong>Audit Log logging mode</strong> is set to \"Significant Events\", all events except content changes will be recorded. \"All Events\" will include content-related events and may be used to monitor for unauthorized post or page changes. \"Preview\" and \"Disabled\" modes do not send any events to Central."
msgstr ""

#: views/tools/options-group-audit-log.php:54
msgid "Are you sure you want to restore the default Audit Log settings? This will undo any custom changes you have made to the options on this page."
msgstr ""

#: views/tools/options-group-audit-log.php:69
msgid "Preview"
msgstr ""

#: views/tools/options-group-audit-log.php:70
msgid "Significant <span class=\"wf-hidden-xs\">Events</span>"
msgstr ""

#: views/tools/options-group-audit-log.php:71
msgid "All <span class=\"wf-hidden-xs\">Events</span>"
msgstr ""

#: views/tools/options-group-audit-log.php:87
msgid "Display Audit Log menu option"
msgstr ""

#: views/tools/options-group-live-traffic.php:32
msgid "Live Traffic Options"
msgstr ""

#: views/tools/options-group-live-traffic.php:40
msgid "These options let you choose which traffic to log and to ignore certain types of visitors, based on their level of access, usernames, IP address, or browser type. If you run a high-traffic website where it is not feasible to see your visitors in real-time, simply change the <strong>Traffic logging mode</strong> to \"Security Only.\""
msgstr ""

#: views/tools/options-group-live-traffic.php:49
msgid "Are you sure you want to restore the default Live Traffic settings? This will undo any custom changes you have made to the options on this page."
msgstr ""

#: views/tools/options-group-live-traffic.php:61
msgid "Traffic logging mode"
msgstr ""

#: views/tools/options-group-live-traffic.php:63
msgid "Security Only"
msgstr ""

#: views/tools/options-group-live-traffic.php:64
msgid "All Traffic"
msgstr ""

#: views/tools/options-group-live-traffic.php:123
msgid "Maximum days to keep Live Traffic data (1-30 days)"
msgstr ""

#: views/tools/options-group-live-traffic.php:135
msgid "Display Live Traffic menu option"
msgstr ""

#: views/tours/login-security.php:28
msgid "Introducing the New Wordfence 2FA"
msgstr ""

#: views/tours/login-security.php:29
msgid "We are excited to announce the release of a completely rebuilt two-factor authentication (2FA) feature within Wordfence. 2FA is an important layer of security that protects you from password guessing and credential stuffing attacks. Previously a Premium-only feature, it is now available for sites running the free version of Wordfence. You are now able to enable 2FA for any role, we’ve added a number of important security features, and we’ve significantly improved the admin interface."
msgstr ""

#: views/tours/login-security.php:44
msgid "Individual Allowlisting"
msgstr ""

#: views/tours/login-security.php:45
msgid "Two-factor authentication now has its own IP allowlist. If necessary, you can allow specific IP addresses or ranges to skip 2FA when logging in."
msgstr ""

#: views/tours/login-security.php:60
msgid "New Login Page Captcha Feature"
msgstr ""

#: views/tours/login-security.php:61
msgid "Wordfence now includes the option to enable Google reCaptcha v3 on your WordPress login and registration pages. This adds a powerful new layer of protection against password guessing and credential stuffing attacks from bots without slowing down real users."
msgstr ""

#: views/tours/login-security.php:70
msgid "Done"
msgstr ""

#. translators: 1. PHP version. 2. Wordfence version. 3. Minimum PHP version.
#: views/unsupported-php/admin-message.php:17
msgid "You are running PHP version %1$s that is not supported by Wordfence %2$s. Wordfence features will not be available until PHP has been upgraded. We recommend using PHP version 8.1, but Wordfence will run on PHP version %3$s at a minimum."
msgstr ""

#. translators: 1. WordPress version. 2. Wordfence version. 3. Minimum WordPress version.
#: views/unsupported-wp/admin-message.php:19
msgid "You are running WordPress version %1$s that is not supported by Wordfence %2$s. Wordfence features will not be available until WordPress has been upgraded. We recommend using the current version of WordPress, but Wordfence will run on WordPress version %3$s at a minimum."
msgstr ""

#: views/user/disabled-application-passwords.php:9
msgid "Application Passwords"
msgstr ""

#: views/user/disabled-application-passwords.php:17
msgid "The site admin can change this option."
msgstr ""

#: views/user/disabled-application-passwords.php:24
msgid "Edit Wordfence Options"
msgstr ""

#: views/waf/firewall-status.php:19
msgid "Wordfence Firewall &amp; Response License Enabled"
msgstr ""

#: views/waf/firewall-status.php:22
msgid "Wordfence Firewall &amp; Care License Enabled"
msgstr ""

#: views/waf/firewall-status.php:25
msgid "Wordfence Firewall &amp; Premium Enabled"
msgstr ""

#: views/waf/firewall-status.php:35
msgid "Wordfence Firewall Deactivated"
msgstr ""

#: views/waf/firewall-status.php:36
msgid "The Wordfence Web Application Firewall is a PHP-based, application-level firewall that filters out malicious requests to your site. It is designed to run at the beginning of WordPress' initialization to filter any attacks before plugins or themes can run any potentially vulnerable code."
msgstr ""

#: views/waf/firewall-status.php:38
msgid "Enable Firewall"
msgstr ""

#: views/waf/firewall-status.php:59
msgid "Wordfence Firewall Activated"
msgstr ""

#: views/waf/firewall-status.php:68
msgid "Learning Mode Enabled"
msgstr ""

#. translators: Localized date.
#: views/waf/firewall-status.php:68
msgid "Learning Mode Until %s"
msgstr ""

#. translators: Localized date.
#: views/waf/firewall-status.php:69
msgid "<i class=\"wf-fa wf-fa-lightbulb-o wf-tip\" aria-hidden=\"true\"></i> When you first install the Wordfence Web Application Firewall, it will be in learning mode. This allows Wordfence to learn about your site so that we can understand how to protect it and how to allow normal visitors through the firewall. We recommend you let Wordfence learn for a week before you enable the firewall."
msgstr ""

#: views/waf/option-rate-limit.php:34
msgid "then"
msgstr ""

#: views/waf/option-rules.php:5
msgid "Rules"
msgstr ""

#: views/waf/option-rules.php:6
msgid "You are currently running the WAF from another WordPress installation. These rules can be disabled or enabled once you configure the firewall to run correctly on this site."
msgstr ""

#: views/waf/option-rules.php:11
msgid "Manually Refresh Rules"
msgstr ""

#: views/waf/option-whitelist.php:5
msgid "Add Allowlisted URL/Param"
msgstr ""

#: views/waf/option-whitelist.php:5
msgid "The URL/parameters in this table will not be tested by the firewall. They are typically added while the firewall is in Learning Mode or by an admin who identifies a particular action/request is a false positive."
msgstr ""

#: views/waf/option-whitelist.php:13
msgid "POST Body"
msgstr ""

#: views/waf/option-whitelist.php:14
msgid "Cookie"
msgstr ""

#: views/waf/option-whitelist.php:15
msgid "File Name"
msgstr ""

#: views/waf/option-whitelist.php:16
msgid "Header"
msgstr ""

#: views/waf/option-whitelist.php:17
msgid "Query String"
msgstr ""

#: views/waf/option-whitelist.php:21
msgid "Param Name"
msgstr ""

#: views/waf/option-whitelist.php:23
msgid "Add"
msgstr ""

#: views/waf/option-whitelist.php:53
msgid "Allowlist Entry Exists"
msgstr ""

#: views/waf/option-whitelist.php:53
msgid "An allowlist entry for this URL and parameter already exists."
msgstr ""

#: views/waf/option-whitelist.php:60
msgid "Allowlisted via Firewall Options page"
msgstr ""

#: views/waf/option-whitelist.php:107
#: views/waf/options-group-whitelisted.php:83
#: views/waf/options-group-whitelisted.php:96
msgid "Param"
msgstr ""

#: views/waf/option-whitelist.php:108
#: views/waf/options-group-whitelisted.php:85
#: views/waf/options-group-whitelisted.php:98
msgid "Source"
msgstr ""

#: views/waf/option-whitelist.php:112
msgid "Filter Value"
msgstr ""

#: views/waf/options-group-advanced-firewall.php:26
msgid "Advanced Firewall Options"
msgstr ""

#: views/waf/options-group-advanced-firewall.php:41
msgid "You are currently running the WAF from another WordPress installation. This option can be changed once you configure the firewall to run correctly on this site."
msgstr ""

#: views/waf/options-group-advanced-firewall.php:54
msgid "Allowlisted IPs must be separated by commas or placed on separate lines. You can specify ranges using the following formats: 127.0.0.1/24, 127.0.0.[1-100], or 127.0.0.1-***********<br/>Wordfence automatically allowlists <a href=\"http://en.wikipedia.org/wiki/Private_network\" target=\"_blank\" rel=\"noopener noreferrer\">private networks<span class=\"screen-reader-text\"> (opens in new tab)</span></a> because these are not routable on the public Internet."
msgstr ""

#: views/waf/options-group-advanced-firewall.php:101
msgid "Separate multiple URLs with commas or place them on separate lines. Asterisks are wildcards, but use with care. If you see an attacker repeatedly probing your site for a known vulnerability you can use this to immediately block them. All URLs must start with a \"/\" without quotes and must be relative. e.g. /badURLone/, /bannedPage.html, /dont-access/this/URL/, /starts/with-*"
msgstr ""

#: views/waf/options-group-advanced-firewall.php:114
msgid "Ignored IPs must be separated by commas or placed on separate lines. These addresses will be ignored from any alerts about increased attacks and can be used to ignore things like standalone website security scanners."
msgstr ""

#: views/waf/options-group-advanced-firewall.php:137
msgid "Category"
msgstr ""

#: views/waf/options-group-advanced-firewall.php:153
msgid "No rules currently set."
msgstr ""

#: views/waf/options-group-advanced-firewall.php:153
msgid "<a href=\"#\" onclick=\"WFAD.wafUpdateRules();return false;\" role=\"button\">Click here</a> to pull down the latest from the Wordfence servers."
msgstr ""

#: views/waf/options-group-advanced-firewall.php:161
msgid "SHOW ALL RULES"
msgstr ""

#: views/waf/options-group-basic-firewall.php:26
msgid "Basic Firewall Options"
msgstr ""

#. translators: WordPress admin URL.
#: views/waf/options-group-basic-firewall.php:36
#: views/waf/options-group-basic-firewall.php:473
#: views/waf/options-group-whitelisted.php:35
msgid "You are currently running the Wordfence Web Application Firewall from another WordPress installation. Please <a href=\"%s\">click here</a> to configure the Firewall to run correctly on this site."
msgstr ""

#: views/waf/options-group-basic-firewall.php:40
msgid "Enabled and Protecting:"
msgstr ""

#: views/waf/options-group-basic-firewall.php:40
msgid "In this mode, the Wordfence Web Application Firewall is actively blocking requests matching known attack patterns and is actively protecting your site from attackers."
msgstr ""

#: views/waf/options-group-basic-firewall.php:41
msgid "Learning Mode:"
msgstr ""

#. translators: Support URL.
#: views/waf/options-group-basic-firewall.php:41
msgid "When you first install the Wordfence Web Application Firewall, it will be in learning mode. This allows Wordfence to learn about your site so that we can understand how to protect it and how to allow normal visitors through the firewall. We recommend you let Wordfence learn for a week before you enable the firewall. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#. translators: Support URL.
#: views/waf/options-group-basic-firewall.php:42
msgid "Disabled:"
msgstr ""

#. translators: Support URL.
#: views/waf/options-group-basic-firewall.php:42
msgid "In this mode, the Wordfence Web Application Firewall is functionally turned off and does not run any of its rules or analyze the request in any way."
msgstr ""

#: views/waf/options-group-basic-firewall.php:46
msgid "Enabled and Protecting"
msgstr ""

#: views/waf/options-group-basic-firewall.php:161
msgid "Automatically enable on"
msgstr ""

#: views/waf/options-group-basic-firewall.php:165
msgid "Protection Level"
msgstr ""

#: views/waf/options-group-basic-firewall.php:167
msgid "Extended Protection:"
msgstr ""

#: views/waf/options-group-basic-firewall.php:167
msgid "All PHP requests will be processed by the firewall prior to running."
msgstr ""

#. translators: Support URL.
#: views/waf/options-group-basic-firewall.php:168
msgid "If you're moving to a new host or a new installation location, you may need to temporarily disable extended protection to avoid any file not found errors. Use this action to remove the configuration changes that enable extended protection mode or you can <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">remove them manually<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: views/waf/options-group-basic-firewall.php:171
msgid "Existing WAF Installation Detected:"
msgstr ""

#: views/waf/options-group-basic-firewall.php:171
msgid "You are currently running the Wordfence Web Application Firewall from another WordPress installation. Please configure the firewall to run correctly on this site."
msgstr ""

#: views/waf/options-group-basic-firewall.php:172
#: views/waf/options-group-basic-firewall.php:175
msgid "Optimize the Wordfence Firewall"
msgstr ""

#: views/waf/options-group-basic-firewall.php:174
msgid "Basic WordPress Protection:"
msgstr ""

#: views/waf/options-group-basic-firewall.php:174
msgid "The plugin will load as a regular plugin after WordPress has been loaded, and while it can block many malicious requests, some vulnerable plugins or WordPress itself may run vulnerable code before all plugins are loaded."
msgstr ""

#: views/waf/options-group-basic-firewall.php:296
msgid "Error During Setup"
msgstr ""

#: views/waf/options-group-basic-firewall.php:470
msgid "Premium Feature:"
msgstr ""

#: views/waf/options-group-basic-firewall.php:470
#: views/waf/options-group-basic-firewall.php:475
msgid "This feature blocks all traffic from IPs with a high volume of recent malicious activity using Wordfence's real-time blocklist."
msgstr ""

#: views/waf/options-group-brute-force.php:39
msgid "This option enables all \"Brute Force Protection\" options, including strong password enforcement and invalid login throttling. You can modify individual options below."
msgstr ""

#: views/waf/options-group-brute-force.php:140
msgid "Hit enter to add a username"
msgstr ""

#: views/waf/options-group-brute-force.php:163
msgid "Additional Options"
msgstr ""

#: views/waf/options-group-brute-force.php:239
msgid "If you use external services that may send POST requests without these headers, do not use this option, as they will be blocked."
msgstr ""

#: views/waf/options-group-brute-force.php:251
msgid "HTML tags will be stripped prior to output and line breaks will be converted into the appropriate tags."
msgstr ""

#: views/waf/options-group-rate-limiting.php:39
msgid "NOTE: This checkbox enables ALL blocking/throttling functions including IP, country and advanced blocking, and the \"Rate Limiting Rules\" below."
msgstr ""

#: views/waf/options-group-rate-limiting.php:195
msgid "These URL patterns will be excluded from the throttling rules used to limit crawlers."
msgstr ""

#: views/waf/options-group-whitelisted.php:26
msgid "Allowlisted URLs"
msgstr ""

#: views/waf/options-group-whitelisted.php:53
msgid "Front-end Website"
msgstr ""

#: views/waf/options-group-whitelisted.php:60
msgid "Admin Panel"
msgstr ""

#: views/waf/options-group-whitelisted.php:64
msgid "Monitor background requests from an administrator's web browser for false positives"
msgstr ""

#: views/waf/options-group-whitelisted.php:80
#: views/waf/options-group-whitelisted.php:93
msgid "Select/deselect all"
msgstr ""

#: views/waf/options-group-whitelisted.php:84
#: views/waf/options-group-whitelisted.php:97
msgid "Created"
msgstr ""

#: views/waf/options-group-whitelisted.php:107
msgid "Select row ${idx}"
msgstr ""

#: views/waf/options-group-whitelisted.php:108
msgid "Toggle row ${idx}"
msgstr ""

#: views/waf/options-group-whitelisted.php:157
msgid "No allowlisted URLs currently set."
msgstr ""

#. translators: Support URL.
#: views/waf/status-tooltip-learning-mode.php:6
msgid "The Web Application Firewall is currently in Learning Mode. <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">Learn More<span class=\"screen-reader-text\"> (opens in new tab)</span></a>"
msgstr ""

#: views/waf/waf-install-manual.php:10
msgid "The required file has been created. You'll need to insert the following code into your <code>php.ini</code> to finish installation:"
msgstr ""

#. translators: Support URL.
#: views/waf/waf-install-manual.php:12
msgid "You can find more details on alternative setup steps, including installation on SiteGround or for multiple sites sharing a single php.ini, <a target=\"_blank\" rel=\"noopener noreferrer\" href=\"%s\">in our documentation<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: views/waf/waf-install.php:9
msgid "Optimize Wordfence Firewall"
msgstr ""

#: views/waf/waf-install.php:13
msgid "If you cannot complete the setup process, "
msgstr ""

#: views/waf/waf-install.php:13
msgid "click here for help"
msgstr ""

#: views/waf/waf-install.php:22
msgid "To make your site as secure as possible, the Wordfence Web Application Firewall is designed to run via a PHP setting called <code>auto_prepend_file</code>, which ensures it runs before any potentially vulnerable code runs."
msgstr ""

#: views/waf/waf-install.php:24
msgid "To make your site as secure as possible, the Wordfence Web Application Firewall is designed to run via a PHP setting called <code>auto_prepend_file</code>, which ensures it runs before any potentially vulnerable code runs. This PHP setting is currently in use, and is including this file:"
msgstr ""

#: views/waf/waf-install.php:26
msgid "If you don't recognize this file, please <a href=\"https://wordpress.org/support/plugin/wordfence\" target=\"_blank\" rel=\"noopener noreferrer\">contact us on the WordPress support forums<span class=\"screen-reader-text\"> (opens in new tab)</span></a> before proceeding."
msgstr ""

#: views/waf/waf-install.php:27
msgid "You can proceed with the installation and we will include this from within our <code>wordfence-waf.php</code> file which should maintain compatibility with your site, or you can opt to override the existing PHP setting."
msgstr ""

#: views/waf/waf-install.php:28
msgid "Include"
msgstr ""

#: views/waf/waf-install.php:28
msgid "Override"
msgstr ""

#: views/waf/waf-install.php:30
msgid "NOTE:"
msgstr ""

#: views/waf/waf-install.php:30
msgid "If you have separate WordPress installations with Wordfence installed within a subdirectory of this site, it is recommended that you perform the Firewall installation procedure on those sites before this one."
msgstr ""

#: views/waf/waf-install.php:34
#: views/waf/waf-uninstall.php:40
msgid "Apache + mod_php"
msgstr ""

#: views/waf/waf-install.php:35
#: views/waf/waf-uninstall.php:41
msgid "Apache + suPHP"
msgstr ""

#: views/waf/waf-install.php:36
#: views/waf/waf-uninstall.php:42
msgid "Apache + CGI/FastCGI"
msgstr ""

#: views/waf/waf-install.php:37
#: views/waf/waf-uninstall.php:43
msgid "LiteSpeed/lsapi"
msgstr ""

#: views/waf/waf-install.php:38
#: views/waf/waf-uninstall.php:44
msgid "NGINX"
msgstr ""

#: views/waf/waf-install.php:39
#: views/waf/waf-uninstall.php:45
msgid "Windows (IIS)"
msgstr ""

#: views/waf/waf-install.php:40
msgid "Manual Configuration"
msgstr ""

#: views/waf/waf-install.php:56
#: views/waf/waf-uninstall.php:61
msgid "If you know your web server's configuration, please select it from the list below."
msgstr ""

#: views/waf/waf-install.php:58
msgid "We've preselected your server configuration based on our tests, but if you know your web server's configuration, please select it now. You can also choose \"Manual Configuration\" for alternate installation details."
msgstr ""

#. translators: 1. PHP ini setting. 2. Support URL.
#: views/waf/waf-install.php:63
msgid "Part of the Firewall configuration procedure for NGINX depends on creating a <code>%1$s</code> file in the root of your WordPress installation. This file can contain sensitive information and public access to it should be restricted. We have <a href=\"%2$s\" target=\"_blank\" rel=\"noreferrer noopener\">instructions on our documentation site<span class=\"screen-reader-text\"> (opens in new tab)</span></a> on what directives to put in your nginx.conf to fix this."
msgstr ""

#: views/waf/waf-install.php:65
msgid "If you are using a web server not listed in the dropdown or if file permissions prevent the installer from completing successfully, you will need to perform the change manually. Click Continue below to create the required file and view manual installation instructions."
msgstr ""

#: views/waf/waf-install.php:86
#: views/waf/waf-uninstall.php:87
msgid "Please download a backup of the following files before we make the necessary changes:"
msgstr ""

#. translators: File path.
#: views/waf/waf-install.php:96
#: views/waf/waf-uninstall.php:97
msgid "Download %s"
msgstr ""

#: views/waf/waf-install.php:105
msgid "Once you have downloaded the files, click Continue to complete the setup."
msgstr ""

#: views/waf/waf-uninstall.php:9
msgid "Uninstall Wordfence Firewall"
msgstr ""

#: views/waf/waf-uninstall.php:26
msgid "Extended Protection Mode of the Wordfence Web Application Firewall uses the PHP ini setting called <code>auto_prepend_file</code> in order to ensure it runs before any potentially vulnerable code runs. This PHP setting currently refers to the Wordfence file at:"
msgstr ""

#. translators: Support URL.
#: views/waf/waf-uninstall.php:34
msgid "Automatic uninstallation cannot be completed, but you may still be able to <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">manually uninstall extended protection<span class=\"screen-reader-text\"> (opens in new tab)</span></a>."
msgstr ""

#: views/waf/waf-uninstall.php:36
msgid "Before this file can be deleted, the configuration for the <code>auto_prepend_file</code> setting needs to be removed."
msgstr ""

#: views/waf/waf-uninstall.php:63
msgid "We've preselected your server configuration based on our tests, but if you know your web server's configuration, please select it now."
msgstr ""

#: views/waf/waf-uninstall.php:107
msgid "Once you have downloaded the files, click Continue to complete uninstallation."
msgstr ""
