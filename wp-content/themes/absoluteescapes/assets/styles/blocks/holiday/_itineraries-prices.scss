.itineraries-prices {
    position: relative;
    //background: #efefef;

    &:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        // background: inherit;
    }

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }

    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    .itineraries-prices__container--table {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 100%;
            max-width: 100%;
            padding: 0;
        }
    }

    .accordion {
        margin: 0;

        &__item {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 0;
            border-top: 1px solid $midlightgrey;
            transition: 300ms;

            &:after {
                display: none;
            }

            &.active {
                border-color: $teal;
                background: $teal;


                .accordion__heading-wrapper {
                    .itineraries-prices__table-row {
                        *
                        span {
                            color: $white;
                        }

                        span.itineraries-prices__difficulty-icon {
                            color: $bluegrey;
                        }
                    }

                    .fa-chevron-down {
                        transform: rotate(180deg);
                        color: $white;
                    }
                }
            }
        }

        &__heading-wrapper {
            position: relative;

            .fa-chevron-down {
                position: absolute;
                top: 0;
                right: 20px;
                bottom: 0;
                margin: auto 0;
                color: $bluegrey;
                transition: 300ms;

                @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                    right: 15px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    font-size: 1.2rem;
                }

                @media only screen and (max-width: 360px) {
                    right: 8px;
                }
            }
        }
    }

    &__table-row,
    &__prices-row {
        display: flex;
        flex-wrap: wrap;
        padding: 15px 0;
        font-size: 1.8rem;

        color: $bluegrey;


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.3rem;
        }

        &--heading {
            span {
                font-weight: 600;
                color: $teal;
            }
        }
    }

    &__table-row {
        padding-right: 30px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 10px 10px;
        }
    }

    &__table-col,
    &__prices-col {
        flex: 1 0;
        padding: 0 10px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 0 8px;
            font-size: 1.3rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 5px;
            font-size: 1.3rem;
        }
    }

    &__table-col {

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {

            font-size: 1.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 1.6rem;
        }

        &--desktop {
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: none;
            }
        }

        &--difficulty-mobile {
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: block;
                flex: 0 0 auto;
                min-width: 120px; // Fixed width for consistent alignment
            }
        }

        &--mobile {
            display: none;
            padding-top: 15px;
            padding-bottom: 15px;
            border-top: 1px solid $white;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding-right: 15px;
                padding-left: 15px;
            }

            span {

                color: $white;

                &.itineraries-prices__difficulty-icon {
                    color: $bluegrey;
                }
            }


            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                display: block;
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    }

    &__table-col-item {
        display: block;
        margin-bottom: 3px;
    }

    &__expanded {
        position: relative;
        padding: 40px 35px;
        border: 1px solid $midlightgrey;
        background: $white;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding: 45px 25px 35px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 45px 20px 35px;
        }
    }

    &__expanded-col {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            flex: 0 0 100%;
            max-width: 100%;
        }


        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 0 0 50%;
            max-width: 50%;
        }


        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            flex: 0 0 100%;
            max-width: 100%;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0
        }
    }

    &__number-wrapper {
        margin-right: 20px;
    }

    &__number {
        display: block;
        position: relative;
        width: 26px;
        height: 26px;
        line-height: 26px;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        background: $blue;
        color: $white;

        &:after {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            width: 1px;
            height: 1000%;
            margin: 0 auto;
            background: $midlightgrey;
            transform: translateY(10%);
        }


    }

    &__points {
        max-width: 250px;
        margin: 0 auto;
    }

    &__point {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 15px;
        overflow: hidden;

        &:first-child {
            .itineraries-prices__number {
                border: 3px solid $blue;
                background: $white;
            }
        }

        &:last-child {
            .itineraries-prices__number {
                &:after {
                    display: none;
                }

                border: 3px solid $blue;
                background: $white;
            }
        }
    }

    &__text-wrapper {
        flex: 1 0;
    }

    .itineraries-prices__prices {
        padding-top: 15px;

        .accordion__item {
            &:last-child {
                border-bottom: 1px solid $midlightgrey;
            }

            .accordion__heading-wrapper {
                .fa-chevron-down {
                    position: static;
                    transform: rotate(0);
                    color: $teal;
                    margin: 0 20px;
                }
            }

            &.active {
                .itineraries-prices__prices-col {
                    span {
                        color: $white;
                    }

                    &--button {
                        .button {
                            background: transparent;
                            color: $white;
                            border: 2px solid $white;

                            &:hover, &:focus {
                                background: $white;
                                color: $teal;
                            }
                        }
                    }
                }

                .accordion__heading-wrapper {
                    .fa-chevron-down {
                        transform: rotate(180deg);
                        color: $white;
                    }
                }
            }
        }

        .accordion__copy {
            padding: 30px 15px;
            background: $white;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding: 25px 0;
            }

            &:after {
                content: '';
                display: table;
                clear: both;
            }

            .accordion__button-wrapper {
                text-align: right;
            }

            .button {
                background: $blue;
                color: $white;

                &:hover, &:focus {
                    background: $teal;
                }
            }
        }
    }

    .itineraries-prices__prices-row {
        + .fa-chevron-down {
            color: $teal;
        }

        &--offwhite {
            background: $offwhitethree;
        }
    }

    &__prices-col {
        flex: 1;
        padding: 0 8px;

        &:first-child {
            flex: 0.6; // Narrower first column

            span {
                color: $teal;
            }
        }

        &:nth-child(2) {
            flex: 0.8; // Narrower venue column
        }

        &:nth-child(3) {
            flex: 0.8; // Narrower price column
        }

        &--button {
            flex: 0 0 120px; // Wider button column
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px; // 15px horizontal padding

            .button {
                font-size: 1.4rem;
                padding: 8px 16px;
                margin: 0;
                white-space: nowrap;
                min-width: 90px;
                text-align: center;
            }

            // Mobile: halve the left/right padding on Enquire button
            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                .button {
                    padding: 8px 8px; // Halved from 8px 16px
                }
            }
        }

        &--arrow {
            flex: 0 0 60px; // Wider arrow column to accommodate margins
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;

            .fa-chevron-down {
                font-size: 1.2rem;
                margin: 0 20px; // 20px left and right margin
            }
        }

        span {
            font-weight: 600;
            color: $bluegrey;
        }
    }

    &__copy {
        &--footer {
            padding-top: 45px;
            text-align: right;

            p,
            li {
                font-size: 1.8rem;
                color: $bluegrey;


                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    font-size: 1.6rem;
                }
            }
        }
    }

    &__difficulty-wrapper {
        display: inline-block;
        vertical-align: top;
    }

    &__difficulty {
        display: block;
        font-size: 1.4rem;
        text-align: center;
        line-height: 1;

        // Override for mobile difficulty column
        .itineraries-prices__table-col--difficulty-mobile & {
            display: inline-block !important;
            text-align: left !important;
            font-size: 1.1rem !important;
            vertical-align: middle !important;
        }
    }

    &__difficulty-score {
        display: block;
        text-align: center;
        flex-direction: row !important;
        justify-content: space-between !important;

        // Override for mobile difficulty column
        .itineraries-prices__table-col--difficulty-mobile & {
            display: inline-block !important;
            text-align: left !important;
            // Removed margin-right for better alignment
            vertical-align: middle !important;
        }
    }

    &__difficulty-icon {
        display: inline-block;
        position: relative;
        width: 28px;
        height: 28px;
        font-size: 1.2rem;
        border-radius: 50%;
        border: 1px solid $bluegrey;
        background: $white;

        svg {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: auto;
        }

        i {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        // Mobile specific sizing and centering
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: inline-flex;
            align-items: center;
            justify-content: center;

            i {
                position: static;
                transform: none;
            }
        }
    }

    // Desktop-specific styling for arrow column and difficulty alignment
    @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
        &__table-col--arrow {
            flex: 0 0 4.375% !important; // Decreased width as requested
            max-width: none;
        }

        &__table-col--difficulty-mobile { // Column 3
            flex: 0 0 24.05% !important; // Increased width as requested
            text-align: left !important; // Left align the column container
            display: flex !important;
            flex-direction: column !important;
            align-items: flex-start !important; // Left align content
        }

        // Price column specific styling (Column 4)
        &__table-col--price {
            flex: 0 0 28.525% !important; // Adjusted to balance: 100% - 18.8% - 25.3% - 24.05% - 4.375% = 27.475%
            padding-left: 20px;
        }

        // Difficulty column SVG sizing
        &__table-col--difficulty-mobile &__difficulty-score svg {
            max-width: 41% !important;
        }

        // Left align difficulty column header only
        &__table-row--heading &__table-col--difficulty-mobile {
            text-align: left !important;
            align-items: flex-start !important;

            span {
                display: inline !important; // Make header title display inline on desktop
            }
        }

        // Create a wrapper for icons and text that fits content width
        &__table-col--difficulty-mobile > span {
            display: flex !important;
            flex-direction: column !important;
            width: fit-content !important; // Only take up space needed for icons
        }

        // Wrapper for difficulty score and difficulty text
        &__difficulty-wrapper {
            max-width: fit-content !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: flex-start !important;
        }

        // Target difficulty text specifically within the difficulty column on desktop
        &__table-col--difficulty-mobile &__difficulty {
            font-size: 1.4rem !important; // Restore original desktop size
            width: 100% !important;
            display: block !important;
            text-align: center !important;
        }

        &__difficulty-score {
            text-align: left !important; // Left align the icons on desktop
            display: block !important;
            margin-bottom: 5px !important; // Add space between icons and text on desktop
        }

        // Desktop column width adjustments - use specific column classes for better control
        &__table-col:nth-child(1) { // Tour code column (hidden)
            display: none !important;
        }

        &__table-col:nth-child(2) { // Itinerary column (column 1)
            flex: 0 0 18.8% !important; // Redistributed space: 16.5% + 2.3%
        }

        &__table-col:nth-child(3) { // Average miles column (column 2)
            flex: 0 0 25.3% !important; // Redistributed space: 23% + 2.3%
        }


    }

    // New itinerary styling
    &__itinerary-main {
        .fa-sun {
            color: #ffa500; // Orange for sun
            margin: 0 2px;
        }

        .fa-moon {
            color: #4169e1; // Royal blue for moon
            margin: 0 2px;
        }
    }

    &__itinerary-extra {
        margin-left: 5px;

        img {
            max-width: 12px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        &:hover img {
            opacity: 1;
        }
    }

    // Mobile layout adjustments for prices section
    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        // Prices section mobile layout
        &__prices-row {
            flex-wrap: wrap; // Allow wrapping for mobile layout
        }

        &__prices-col {
            &:first-child {
                flex: 0 0 100% !important; // First column takes full width
                margin-bottom: 10px; // Add space below first column
            }

            &:nth-child(2),
            &:nth-child(3) {
                flex: 1 !important; // Share the remaining space equally (after arrow takes 30px)
                margin-bottom: 10px; // Add space below these columns
            }

            &--button {
                flex: 0 0 100% !important; // Button column takes full width on new row
                order: 10; // Place button after other columns
                justify-content: flex-start !important; // Align button to the left
                margin-top: 5px; // Add space above button row
                padding-left: 8px !important; // 8px left padding to match other columns
            }

            &--arrow {
                flex: 0 0 20px !important; // Fixed 20px width for arrow column
            }
        }
    }

    // Mobile layout adjustments - Table-like column control
    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        &__table-row {
            display: flex;
            flex-wrap: nowrap; // Prevent wrapping to maintain column structure
            align-items: center; // Vertically center all row contents
            width: 100%;
            min-height: 50px; // Ensure consistent row height
        }

        // CENTRALLY CONTROLLED COLUMN WIDTHS (like a proper table)
        // Column 1: Itinerary - reduced width to make room for difficulty column
        &__table-col:nth-child(2) { // Itinerary column (2nd child after hidden tour code)
            flex: 0 0 25% !important; // Reduced from 30% to 25% (-5%)
            min-width: 0;
            padding-right: 10px;
            display: flex;
            align-items: center; // Vertically center itinerary content
        }

        // Column 2: Difficulty - increased width to prevent icon wrapping
        &__table-col--difficulty-mobile {
            flex: 0 0 40% !important; // Increased from 35% to 40% (+5%)
            text-align: center !important; // Center align for flexbox layout
            padding-left: 0 !important; // Remove padding to align with header
            padding-right: 10px;
            white-space: normal !important; // Allow text wrapping for difficulty text
            display: flex;
            flex-direction: column; // Stack icons and text vertically
            align-items: center; // Center align content horizontally
            justify-content: center; // Center align content vertically

            // Header and content alignment
            span {
                text-align: center !important;
                display: inline-block; // Ensure span displays properly
            }

            .itineraries-prices__difficulty-score {
                display: block !important; // Block display for stacking above text
                text-align: center !important; // Center align the icons
                margin-right: 0 !important; // Remove right margin for centered layout
                margin-bottom: 0 !important; // Remove bottom margin on mobile
                vertical-align: middle !important;

                .itineraries-prices__difficulty-icon {
                    width: 26px; // Increased from 24px
                    height: 26px; // Increased from 24px
                    font-size: 1.2rem; // Increased from 1.1rem
                    display: inline-flex !important; // Use flex for centering
                    align-items: center !important; // Vertically center icon
                    justify-content: center !important; // Horizontally center icon
                    margin-right: 0px; // Remove space between icons for tighter grouping

                    i {
                        position: static !important; // Override absolute positioning
                        transform: none !important; // Remove transform
                        margin: 0 !important; // Remove any default margins
                        padding: 0 !important; // Remove any default padding
                        line-height: 1 !important; // Ensure consistent line height
                        vertical-align: baseline !important; // Reset vertical alignment
                    }
                }
            }

            .itineraries-prices__difficulty {
                display: block !important; // Block display for proper wrapping
                font-size: 1.2rem !important; // Increased from 1.1rem
                text-align: center !important; // Center align difficulty text
                margin-top: 0; // Remove top margin since icons have bottom margin
                line-height: 1.2; // Tighter line height for better spacing
            }


        }

        // Column 3: Price - adjusted width to balance with other columns
        &__table-col--price {
            flex: 0 0 30% !important; // Reduced from 32% to 30% (-2%)
            text-align: right !important; // Ensure right alignment
            padding-right: 10px; // Minimal padding since arrow is separate
            padding-left: 15px;
            white-space: nowrap; // Prevent text wrapping
            display: flex;
            align-items: center; // Vertically center price content
            justify-content: flex-end; // Right align content within flex container

            // Extra small screens - ensure proper right alignment
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding-right: 5px; // Reduce padding on very small screens
                padding-left: 5px;

                .itineraries-prices__price-text-mobile {
                    text-align: right !important; // Force right alignment on mobile text
                    width: 100%; // Take full width of container
                    display: block !important; // Block display for proper text alignment
                }

                // Also handle any other spans that might be in price column (like price values)
                span:not(.itineraries-prices__price-text-desktop):not(.itineraries-prices__price-text-mobile) {
                    text-align: right !important; // Force right alignment on price values
                    width: 100%; // Take full width of container
                    display: block !important; // Block display for proper text alignment
                }
            }
        }

        // Column 4: Arrow - dedicated column for dropdown arrows
        &__table-col--arrow {
            flex: 0 0 5% !important; // Small dedicated column for arrows on mobile
            display: flex;
            align-items: center; // Vertically center arrow
            justify-content: center; // Center arrow horizontally
            padding: 0; // No padding needed
        }

        // Ensure all columns in header row follow same widths
        &__table-row--heading {
            .itineraries-prices__table-col:nth-child(2) {
                flex: 0 0 25% !important; // Match content row width (reduced)
                padding-right: 10px;
            }

            .itineraries-prices__table-col--difficulty-mobile {
                flex: 0 0 40% !important; // Match content row width (increased)
                text-align: center !important; // Center align header to match content
                padding-left: 0 !important;
                padding-right: 10px;
                white-space: nowrap; // Prevent header text wrapping

                span {
                    text-align: center !important; // Center align header text
                }
            }

            .itineraries-prices__table-col--price {
                flex: 0 0 30% !important; // Match content row width (reduced)
                text-align: right !important; // Ensure right alignment
                padding-right: 10px; // Match content row padding
                padding-left: 15px; // Match content row padding
                white-space: nowrap; // Prevent header text wrapping
                display: flex;
                align-items: center; // Vertically center header content
                justify-content: flex-end; // Right align header content

                // Extra small screens - ensure header is also right-aligned
                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    padding-right: 5px; // Match content row padding
                    padding-left: 5px; // Match content row padding

                    .itineraries-prices__price-text-mobile {
                        text-align: right !important; // Force right alignment on mobile text
                        width: 100%; // Take full width of container
                        display: block !important; // Block display for proper text alignment
                    }
                }
            }

            .itineraries-prices__table-col--arrow {
                flex: 0 0 5% !important; // Match content row width
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    // Price text visibility control
    &__price-text-desktop {
        display: inline;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: none;
        }
    }

    &__price-text-mobile {
        display: none;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: inline;
        }
    }
}
