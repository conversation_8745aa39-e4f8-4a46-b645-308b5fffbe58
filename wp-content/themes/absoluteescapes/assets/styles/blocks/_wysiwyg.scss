.wysiwyg {

    &--top-padding {
        padding-top: 60px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 0;
        }
    }

    &__inner {
        padding: 50px 0;
    }

    // Related Holidays styling within WYSIWYG blocks
    .holidays__posts {
        display: flex;
        flex-wrap: wrap;
        padding-top: 30px;
        margin: 0 -15px;
    }

    .holidays__post {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px 40px;
        border-bottom: 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            flex: 0 0 100%;
            max-width: 100%;
        }

        a {
            text-decoration: none;

            &:hover, &:focus {
                .holidays__title {
                    color: $teal;
                }
            }
        }
    }

    .holidays__post-row {
        position: relative;
        cursor: pointer;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            max-width: 360px;
            margin: 0 auto;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            margin: 0 auto;
        }

        // Create clickable overlay for entire row
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            background: transparent;
        }

        // Ensure gallery has higher z-index to remain functional
        .holidays__gallery {
            position: relative;
            z-index: 2;
        }

        // Ensure existing links still work (title link)
        a {
            position: relative;
            z-index: 2;
        }
    }

    .holidays__gallery {
        max-width: 100%;
        width: 100%;
        overflow: visible; // Allow navigation buttons to show outside container

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            max-width: 360px;
            padding-bottom: 10px;
        }

        // Ensure gallery doesn't cause horizontal overflow
        @media only screen and (max-width: 400px) {
            max-width: 100%;
        }

        &:hover {
            .flickity-button {
                opacity: 1;
            }
        }
    }

    .holidays__image {
        width: 100%;

        img {
            width: 100%;
        }
    }

    .holidays__post-content {
        padding-top: 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding-bottom: 10px;
        }
    }

    .holidays__title {
        margin-bottom: 2px;
        transition: 300ms;
    }

    .holidays__price {
        margin-bottom: 20px;
        font-weight: 300;
    }

    .marker__items {
        padding: 5px 0 0;
    }

    .marker__item {
        display: inline-block;
        vertical-align: middle;
        margin-right: 18px;
        margin-bottom: 7px;

        &:last-child {
            margin-right: 0;
        }
    }

    // Flickity slider styling for holidays galleries
    .flickity-button {
        display: block;
        width: 20px;
        height: 20px;
        border: none;
        padding: 0;
        opacity: 0;

        svg {
            display: block;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            fill: $white;
        }

        &.previous {
            left: 4px;
        }

        &.next {
            right: 4px;
        }
    }

    .flickity-page-dots {
        bottom: 10px;

        .dot {
            width: 4px;
            height: 4px;
            margin: 0 2px;

            &.is-selected {
                width: 6px;
                height: 6px;
            }
        }
    }

}