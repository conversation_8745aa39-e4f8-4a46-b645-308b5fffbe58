// Add geotargeting to forms - IMMEDIATE FIX VERSION
// This version runs when modals open instead of on DOMContentLoaded to prevent timing issues

// Cache for GeoIP result to avoid multiple API calls
var geoipCountryCode = null;
var geoipPromise = null;

function populateGeoIPForVisibleForms() {
    console.log('🌍 GeoIP detection starting for visible forms...');

    // Check if geoip_detect is available
    if (typeof geoip_detect === 'undefined') {
        console.warn('🌍 geoip_detect is not available - forms will work without geoip');
        return;
    }

    // Wrap in try-catch to prevent any errors from breaking form functionality
    try {
        // If we already have the country code, use it immediately
        if (geoipCountryCode) {
            console.log('🌍 Using cached country code:', geoipCountryCode);
            setGeoIPForVisibleForms(geoipCountryCode);
            return;
        }

        // If we already have a promise in progress, use it
        if (geoipPromise) {
            console.log('🌍 Using existing GeoIP promise');
            geoipPromise.then(function(countryCode) {
                if (countryCode) {
                    setGeoIPForVisibleForms(countryCode);
                }
            });
            return;
        }

        // Create new promise with timeout protection
        console.log('🌍 Creating new GeoIP detection promise');
        geoipPromise = geoip_detect.get_info().then(function(record) {
            console.log('🌍 GeoIP record received:', record);

            if (record.error()) {
                console.warn('🌍 GeoIP detection failed:', record.error());
                return null; // Don't break functionality
            }

            // Cache the result
            geoipCountryCode = record.get_with_locales('country.isoCode', ['en']);
            console.log('🌍 GeoIP country code cached:', geoipCountryCode);
            return geoipCountryCode;

        }).catch(error => {
            console.warn('🌍 GeoIP detection error (non-critical):', error);
            return null; // Don't break functionality
        });

        // Use the promise
        geoipPromise.then(function(countryCode) {
            if (countryCode) {
                setGeoIPForVisibleForms(countryCode);
            }
        });

        // Add timeout protection
        setTimeout(() => {
            if (!geoipCountryCode) {
                console.warn('🌍 GeoIP detection timeout - continuing without geoip');
            }
        }, 3000);

    } catch (error) {
        console.warn('🌍 Critical error in geoip detection (non-critical):', error);
        // Don't break other functionality
    }
}

function setGeoIPForVisibleForms(countryCode) {
    // Only target visible forms to avoid modal timing issues
    const visibleForms = document.querySelectorAll('form[id^="gform_"]:not([style*="display: none"])');
    console.log('🌍 Found visible forms:', visibleForms.length);

    visibleForms.forEach(form => {
        const geoipElement = form.querySelector('.geoip_country');
        if (geoipElement) {
            const inputElement = geoipElement.querySelector('input');
            if (inputElement && !inputElement.value) { // Don't overwrite existing values
                inputElement.value = countryCode;
                console.log('🌍 Set geoip value for form:', form.id, 'to:', countryCode);
            }
        }
    });
}