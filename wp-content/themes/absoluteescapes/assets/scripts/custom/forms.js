$(function() {

    var holidayIdField = $( "input[value='holiday']" );
    var holidayTitleField = $( "input[value='holiday_title']" );
    var holidayTypeField = $( "input[value='holiday_type']" );
    var tourCodeField = $( "input[value='tourcode']" );
    var itineraryField = $( "input[value='itinerary']" );
    var accommodationField = $( "input[value='accommodation']" );
    var startDateField = $( "input[value='startdate']" );

    jQuery(document).on("gform_post_render", function(event, form_id) {
        console.log('📝 Form post render event fired for form ID:', form_id);

        // IMMEDIATE FIX: Populate GeoIP when forms are rendered
        if (typeof populateGeoIPForVisibleForms === 'function') {
            console.log('🔧 Form rendered - triggering GeoIP population');
            // Small delay to ensure form is fully rendered
            setTimeout(function() {
                populateGeoIPForVisibleForms();
            }, 100);
        }

        // Log all forms on the page
        var forms = $('form[id^="gform_"]');
        console.log('📝 Total forms found on page:', forms.length);
        forms.each(function(index, form) {
            console.log('📝 Form found:', form.id, 'visible:', $(form).is(':visible'));
        });

        if(form_id !== 1) {

            // if($('.form__heading-wrapper').empty()) {
            //     $('.form').find('.gform_heading').appendTo('.form__heading-wrapper')
            // }

            $('.gfmc-column').append('<div class="form__next"><span class="button button--alt">Next <i class="fas fa-chevron-right"></i></span><div>');

            $('.form').find('.gform_footer').appendTo('.gfmc-column:last');

            if($('.gform_confirmation_wrapper').length) {
                $('.form__section').remove();
                $('.form__heading-wrapper').remove();
            }

            $('.gsection').first().addClass('active');
            $('.gsection').first().next().addClass('active');

            if($('.validation_message').length) {
                $('.validation_message').closest('.gfmc-column').addClass('active');
                $('.validation_message').closest('.gfmc-column').prev().addClass('active');
            }
        }

        if(form_id === 2) {

            holidayIdField.val($('.form__section').attr('data-id'));
            holidayTitleField.val($('.form__section').attr('data-title'));
            holidayTypeField.val($('.form__section').attr('data-type'));
            tourCodeField.val($('.tour-code').text());
            itineraryField.val($('#formItinerary').val());
            accommodationField.val($('#formAccommodation').val());
            startDateField.val($('#formStart').val());

            $('#formItinerary').on('change', function() {
                itineraryField.val($(this).val());
            });

            $('#formAccommodation').on('change', function() {
                accommodationField.val($(this).val());
            });

            $('#formStart').on('change', function() {
                startDateField.val($(this).val());
            });
        }

        if($('.validation_error').length) {
            $('.payment-choice').find('input').attr('checked', false);
        }

        $(document).find('.payment-choice').find('input').on('change', function() {
            console.log('💳 Payment choice changed:', $(this).val(), 'checked:', $(this).is(':checked'));

            if($(this).val().toLowerCase() !== 'bank transfer') {

                $('.form--payment').find('.gsection').show();

                $('.payment-details__notice').removeClass('active');

                $('.gfmc-row-2-column').addClass('active');
                $('.gfmc-row-2-column').prev().addClass('active');

            } else {

                $('.form--payment').find('.gsection').hide();
                $('.form--payment').find('.gfmc-column').removeClass('active');
                $('.form--payment').find('.gsection').removeClass('active');
                $('.payment-details__notice').addClass('active');
                $('.gsection').first().addClass('active');
            }

        });
    });

    gform.addFilter( 'gform_datepicker_options_pre_init', function( optionsObj, formId, fieldId ) {
        // do stuff
        optionsObj.yearRange = '-0:+5';
        return optionsObj;
    } );

    $(document).on('gform_confirmation_loaded', function(event, formId){
        if(formId === 2 || formId === 3) {
            if($('.gform_confirmation_wrapper').length) {
                $('.form__section').remove();
                $('.form__heading-wrapper').remove();
            }
        }
    });


    $(document).on('click', '.form__next .button', function() {

        var $this = $(this);
        //$(this).closest('.gfmc-column').prev().removeClass('active');
        $(this).closest('.gfmc-column').removeClass('active');

        // $(this).closest('.gfmc-column').next().addClass('active');
        // $(this).closest('.gfmc-column').next().next().addClass('active');

        if($(this).closest('.gfmc-column').next().is(':visible')) {

            $(this).closest('.gfmc-column').next().addClass('active');
            $(this).closest('.gfmc-column').next().next().addClass('active');
        } else {

            $(this).closest('.gfmc-column').next().next().next().addClass('active');
            $(this).closest('.gfmc-column').next().next().next().next().addClass('active');
        }

        $('html, body').animate({
            scrollTop: $this.closest('.gfmc-column').next().offset().top
        }, 0);

    });


    $(document).on('click', '.gsection', function() {

        if(!$(this).next().hasClass('active')) {
            $(this).next().addClass('active');
            $(this).addClass('active');
        } else {
            $(this).next().removeClass('active');
            //$(this).removeClass('active');
        }
    });


    $('#filterTrigger').on('click', function(e) {
        e.preventDefault();

        $('.filter').slideToggle();

    });

    $('.enquiry-form').on('click', function(e) {
        if (e.target !== this)
        return;

        $(this).removeClass('active');

    });


    $('.enquiry-cta__button').on('click', function(e) {
        console.log('🔘 Enquiry button clicked');
        e.preventDefault();

        $('.enquiry-form').toggleClass('active');
        console.log('🔘 Modal toggled, active class:', $('.enquiry-form').hasClass('active'));

        // Fix: Reset all Gravity Forms submission flags when modal opens
        if ($('.enquiry-form').hasClass('active')) {
            console.log('🔧 Resetting all Gravity Forms submission flags');
            for (var i = 1; i <= 10; i++) {
                if (window['gf_submitting_' + i] === true) {
                    console.log('🔧 Resetting gf_submitting_' + i);
                    window['gf_submitting_' + i] = false;
                    $('#gform_ajax_spinner_' + i).remove();
                }
            }
        }

    });

    $('.enquiry-form__close').on('click', function() {
        console.log('❌ Modal close button clicked');
        $('.enquiry-form').removeClass('active');

    });

    // Checkbox debugging (simplified)
    $(document).on('change', 'input[type="checkbox"]', function() {
        console.log('☑️ Checkbox changed:', this.name, 'checked:', this.checked);
    });

    // Add debugging for all form submissions
    $(document).on('submit', 'form', function(e) {
        console.log('📤 Form submission attempted:', this.id);
        console.log('📤 Form action:', this.action);
        console.log('📤 Form method:', this.method);
        console.log('📤 Form has gform_ajax input:', $(this).find('input[name="gform_ajax"]').length > 0);

        // Check if this is a Gravity Form
        if (this.id.startsWith('gform_')) {
            var formId = this.id.replace('gform_', '');
            console.log('📤 Gravity Form ID:', formId);
            console.log('📤 gf_submitting_' + formId + ':', window['gf_submitting_' + formId]);

            // CRITICAL FIX: If form is stuck in submitting state, reset it
            if (window['gf_submitting_' + formId] === true) {
                console.log('🔧 Form was stuck in submitting state - resetting');
                window['gf_submitting_' + formId] = false;
                $('#gform_ajax_spinner_' + formId).remove();

                // Allow the submission to proceed
                console.log('🔧 Allowing form submission to proceed');
            }
        }
    });

    // Add debugging for Gravity Forms AJAX submissions
    $(document).on('gform_pre_submission', function(event, form, ajax) {
        console.log('📤 Gravity Form pre-submission:', form.id, 'ajax:', ajax);
    });

    // Add debugging for Gravity Forms validation
    $(document).on('gform_post_submission', function(event, form, ajax) {
        console.log('📤 Gravity Form post-submission:', form.id, 'ajax:', ajax);
    });

    // Add debugging for Gravity Forms confirmation
    $(document).on('gform_confirmation_loaded', function(event, formId) {
        console.log('✅ Gravity Form confirmation loaded:', formId);
    });




});
