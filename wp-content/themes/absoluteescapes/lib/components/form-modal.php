<?php
$test_groups = get_field( 'test_group', 'options' );
$modals = [];
$test_mode = false;

// Check if test_groups is an array and not empty
if ( is_array( $test_groups ) && !empty( $test_groups ) ) {
  foreach ( $test_groups as $test_group ) {
    // Check if test_group is an array before accessing its elements
    if ( is_array( $test_group ) && isset( $test_group['acf_fc_layout'] ) && $test_group['acf_fc_layout'] === 'form_modal' && $test_group['enabled'] ) {
      $modals = $test_group['Form Modal'];
      $test_mode = isset( $test_group['test_mode'] ) ? $test_group['test_mode'] : false;
      break;
    }
  }
}

// Check for URL parameter override
$url_test_mode = isset($_GET['modaltest']) && $_GET['modaltest'] == '1';
$is_test_mode = $test_mode || $url_test_mode;
?>
<?php if ( $modals ): ?>
<div class="form-modal-ab" id="form-modal-ab-variant-1"></div>
<div class="modal-variants" style="display:none;">
  <?php foreach ( $modals as $idx => $modal ): ?>
    <?php
    // Check if this individual modal is enabled
    $modal_enabled = isset($modal['enabled']) ? $modal['enabled'] : true; // Default to enabled for backward compatibility
    if ( !$modal_enabled ) {
        continue; // Skip this modal if it's disabled
    }

    $desktop_layout = isset($modal['desktop_layout']) ? $modal['desktop_layout'] : (isset($modal['layout']) ? $modal['layout'] : 'two-column');
    $mobile_layout = isset($modal['mobile_layout']) ? $modal['mobile_layout'] : (isset($modal['layout']) ? $modal['layout'] : 'image-top');

    // Debug: Let's see what layouts are being detected
    error_log("Modal Layout Debug - Desktop: $desktop_layout, Mobile: $mobile_layout");

    // Generate the form once to avoid duplication
    $form_html = do_shortcode('[gravityform id="' . $modal['gravity_forms'] .'" title="false" description="false" ajax="true"]');
    ?>
    <div id="modal-variant-<?= ($idx + 1); ?>" class="form-modal" data-desktop-layout="<?= $desktop_layout; ?>" data-mobile-layout="<?= $mobile_layout; ?>">

      <!-- Single responsive layout with CSS-controlled image positioning -->
      <div class="modal-content-wrapper"
           data-desktop-layout="<?= $desktop_layout; ?>"
           data-mobile-layout="<?= $mobile_layout; ?>">

        <!-- Image container - positioned by CSS -->
        <div class="modal-image-container">
          <div class="image">
            <?php
            // Check if this is the Rectangle-22.png image that needs to be replaced
            $image_url = wp_get_attachment_url($modal['image']['ID']);
            $is_rectangle_22 = strpos($image_url, 'Rectangle-22') !== false;

            if ($is_rectangle_22) {
              // Try different methods to find the attachments
              $desktop_id = null;
              $mobile_id = null;

              // Method 1: Search by filename in _wp_attached_file
              $desktop_attachment = get_posts(array(
                'post_type' => 'attachment',
                'meta_query' => array(
                  array(
                    'key' => '_wp_attached_file',
                    'value' => '2025/08/Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales.jpeg',
                    'compare' => 'LIKE'
                  )
                ),
                'posts_per_page' => 1
              ));

              $mobile_attachment = get_posts(array(
                'post_type' => 'attachment',
                'meta_query' => array(
                  array(
                    'key' => '_wp_attached_file',
                    'value' => '2025/08/Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris.jpeg',
                    'compare' => 'LIKE'
                  )
                ),
                'posts_per_page' => 1
              ));

              if (!empty($desktop_attachment)) $desktop_id = $desktop_attachment[0]->ID;
              if (!empty($mobile_attachment)) $mobile_id = $mobile_attachment[0]->ID;

              // Method 2: If not found, try searching by post title/name
              if (!$desktop_id) {
                $desktop_attachment = get_posts(array(
                  'post_type' => 'attachment',
                  'name' => 'desktop-pop-up-afon-ogwan-snowdonia-wales',
                  'posts_per_page' => 1
                ));
                if (!empty($desktop_attachment)) $desktop_id = $desktop_attachment[0]->ID;
              }

              if (!$mobile_id) {
                $mobile_attachment = get_posts(array(
                  'post_type' => 'attachment',
                  'name' => 'mobile-pop-up-blue-skies-over-luskentyre-beach-isle-of-harris',
                  'posts_per_page' => 1
                ));
                if (!empty($mobile_attachment)) $mobile_id = $mobile_attachment[0]->ID;
              }

              // Method 3: Search by partial filename match
              if (!$desktop_id) {
                $desktop_attachment = get_posts(array(
                  'post_type' => 'attachment',
                  'meta_query' => array(
                    array(
                      'key' => '_wp_attached_file',
                      'value' => 'Desktop-pop-up-Afon-Ogwan',
                      'compare' => 'LIKE'
                    )
                  ),
                  'posts_per_page' => 1
                ));
                if (!empty($desktop_attachment)) $desktop_id = $desktop_attachment[0]->ID;
              }

              // Debug: Log what we found
              error_log("Desktop ID: " . ($desktop_id ?: 'not found'));
              error_log("Mobile ID: " . ($mobile_id ?: 'not found'));

              if ($desktop_id && $mobile_id) {
                // Get WordPress generated srcset for both images
                $desktop_srcset = wp_get_attachment_image_srcset($desktop_id, 'large');
                $mobile_srcset = wp_get_attachment_image_srcset($mobile_id, 'large');
                $desktop_sizes = wp_get_attachment_image_sizes($desktop_id, 'large');
                $mobile_sizes = wp_get_attachment_image_sizes($mobile_id, 'large');

                // Debug: Log srcsets
                error_log("Desktop srcset: " . ($desktop_srcset ?: 'empty'));
                error_log("Mobile srcset: " . ($mobile_srcset ?: 'empty'));

                // Fallback URLs
                $desktop_url = wp_get_attachment_image_url($desktop_id, 'large');
                $mobile_url = wp_get_attachment_image_url($mobile_id, 'large');
                ?>
                <picture>
                  <source media="(min-width: 768px)"
                          srcset="<?= esc_attr($desktop_srcset ?: $desktop_url); ?>"
                          sizes="<?= esc_attr($desktop_sizes ?: '(min-width: 1200px) 600px, (min-width: 768px) 50vw, 100vw'); ?>">
                  <source media="(max-width: 767px)"
                          srcset="<?= esc_attr($mobile_srcset ?: $mobile_url); ?>"
                          sizes="<?= esc_attr($mobile_sizes ?: '(max-width: 480px) 100vw, (max-width: 767px) 100vw'); ?>">
                  <img src="<?= esc_url($desktop_url); ?>"
                       alt="Modal Background"
                       style="width: 100%; height: 100%; object-fit: cover;">
                </picture>
                <?php
              } else {
                // Manual srcset creation using the available thumbnail sizes
                $base_url = 'https://absolute-escapes-redis.ddev.site/wp-content/uploads/2025/08/';

                // Desktop image srcset (Snowdonia)
                $desktop_srcset = $base_url . 'Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales-300x200.jpeg 300w, ' .
                                 $base_url . 'Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales-768x512.jpeg 768w, ' .
                                 $base_url . 'Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales-1024x683.jpeg 1024w, ' .
                                 $base_url . 'Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales.jpeg 4000w';

                // Mobile image srcset (Luskentyre)
                $mobile_srcset = $base_url . 'Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris-300x200.jpeg 300w, ' .
                                $base_url . 'Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris-768x1057.jpeg 768w, ' .
                                $base_url . 'Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris-1024x683.jpeg 1024w, ' .
                                $base_url . 'Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris.jpeg 3000w';

                $desktop_image_base = $base_url . 'Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales.jpeg';
                $mobile_image_base = $base_url . 'Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris.jpeg';
                ?>
                <picture>
                  <source media="(min-width: 768px)"
                          srcset="<?= esc_attr($desktop_srcset); ?>"
                          sizes="(min-width: 1200px) 600px, (min-width: 768px) 50vw, 100vw">
                  <source media="(max-width: 767px)"
                          srcset="<?= esc_attr($mobile_srcset); ?>"
                          sizes="(max-width: 480px) 100vw, (max-width: 767px) 100vw">
                  <img src="<?= esc_url($desktop_image_base); ?>"
                       alt="Modal Background"
                       style="width: 100%; height: 100%; object-fit: cover;">
                </picture>
                <?php
              }
            } else {
              // Use the original image for other cases
              echo wp_get_attachment_image( $modal['image']['ID'], 'holiday_type_large' );
            }
            ?>
          </div>
          <?php if ($modal['hover_image']): ?>
          <div class="image hover">
            <?php
            // Check if hover image is also Rectangle-22
            $hover_image_url = wp_get_attachment_url($modal['hover_image']['ID']);
            $is_hover_rectangle_22 = strpos($hover_image_url, 'Rectangle-22') !== false;

            if ($is_hover_rectangle_22) {
              // Reuse the same attachment IDs if already found, or find them again
              if (!isset($desktop_id) || !isset($mobile_id)) {
                $desktop_attachment = get_posts(array(
                  'post_type' => 'attachment',
                  'meta_query' => array(
                    array(
                      'key' => '_wp_attached_file',
                      'value' => 'Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales.jpeg',
                      'compare' => 'LIKE'
                    )
                  ),
                  'posts_per_page' => 1
                ));

                $mobile_attachment = get_posts(array(
                  'post_type' => 'attachment',
                  'meta_query' => array(
                    array(
                      'key' => '_wp_attached_file',
                      'value' => 'Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris.jpeg',
                      'compare' => 'LIKE'
                    )
                  ),
                  'posts_per_page' => 1
                ));

                $desktop_id = !empty($desktop_attachment) ? $desktop_attachment[0]->ID : null;
                $mobile_id = !empty($mobile_attachment) ? $mobile_attachment[0]->ID : null;
              }

              if ($desktop_id && $mobile_id) {
                // Get WordPress generated srcset for both images
                $desktop_srcset = wp_get_attachment_image_srcset($desktop_id, 'large');
                $mobile_srcset = wp_get_attachment_image_srcset($mobile_id, 'large');
                $desktop_sizes = wp_get_attachment_image_sizes($desktop_id, 'large');
                $mobile_sizes = wp_get_attachment_image_sizes($mobile_id, 'large');

                // Fallback URLs
                $desktop_url = wp_get_attachment_image_url($desktop_id, 'large');
                $mobile_url = wp_get_attachment_image_url($mobile_id, 'large');
                ?>
                <picture>
                  <source media="(min-width: 768px)"
                          srcset="<?= esc_attr($desktop_srcset ?: $desktop_url); ?>"
                          sizes="<?= esc_attr($desktop_sizes ?: '(min-width: 1200px) 600px, (min-width: 768px) 50vw, 100vw'); ?>">
                  <source media="(max-width: 767px)"
                          srcset="<?= esc_attr($mobile_srcset ?: $mobile_url); ?>"
                          sizes="<?= esc_attr($mobile_sizes ?: '(max-width: 480px) 100vw, (max-width: 767px) 100vw'); ?>">
                  <img src="<?= esc_url($desktop_url); ?>"
                       alt="Modal Background Hover"
                       style="width: 100%; height: 100%; object-fit: cover;">
                </picture>
                <?php
              } else {
                // Fallback to hardcoded URLs if attachments not found
                $desktop_image_base = 'https://absolute-escapes-redis.ddev.site/wp-content/uploads/2025/08/Desktop-pop-up-Afon-Ogwan-Snowdonia-Wales.jpeg';
                $mobile_image_base = 'https://absolute-escapes-redis.ddev.site/wp-content/uploads/2025/08/Mobile-pop-up-Blue-skies-over-Luskentyre-beach-Isle-of-Harris.jpeg';
                ?>
                <picture>
                  <source media="(min-width: 768px)" srcset="<?= esc_url($desktop_image_base); ?>">
                  <source media="(max-width: 767px)" srcset="<?= esc_url($mobile_image_base); ?>">
                  <img src="<?= esc_url($desktop_image_base); ?>" alt="Modal Background Hover" style="width: 100%; height: 100%; object-fit: cover;">
                </picture>
                <?php
              }
            } else {
              echo wp_get_attachment_image( $modal['hover_image']['ID'], 'holiday_type_large' );
            }
            ?>
          </div>
          <?php endif; ?>
        </div>

        <!-- Content container - always contains content + form -->
        <div class="modal-content-container">
          <?= $modal['content']; ?>
          <?= $form_html; ?>
        </div>

      </div>

    </div>
    </div>
  <?php endforeach; ?>
</div>

<script>
    (function($) {
        var isTestMode = <?= $is_test_mode ? 'true' : 'false'; ?>;

        $(function() {

            // Function to log current layout (CSS handles the actual layout)
            function updateModalLayout() {
                $('.form-modal').each(function() {
                    var $modal = $(this);
                    var desktopLayout = $modal.data('desktop-layout');
                    var mobileLayout = $modal.data('mobile-layout');
                    var isMobile = $(window).width() < 768;

                    if (isMobile) {
                        console.log('🔧 Mobile layout active:', mobileLayout);
                    } else {
                        console.log('🔧 Desktop layout active:', desktopLayout);
                    }
                });
            }

            // Update layout on page load and window resize
            updateModalLayout();

            // Add responsive CSS matching the original design
            var modalCSS = `
                <style>
                /* Base mobile layout - image-top (default) */
                .modal-content-wrapper {
                    display: flex;
                    flex-direction: column;
                }

                .modal-image-container {
                    order: 1;
                    position: relative;
                    width: 100%;
                    height: 160px; /* Reduced by 10px more */
                    overflow: hidden;
                }

                .modal-image-container .image {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 0;
                    background: #666;
                    opacity: 1;
                    transition: opacity 500ms;
                }

                .modal-image-container .image.hover {
                    opacity: 0;
                }

                .modal-image-container .image img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center center;
                }

                /* Gradient overlay for mobile close button visibility - only on image-top */
                .modal-content-wrapper[data-mobile-layout="image-top"] .modal-image-container::before {
                    content: "";
                    width: 120px;
                    height: 120px;
                    position: absolute;
                    top: 0;
                    right: 0;
                    z-index: 9;
                    background-size: 100% 100%;
                    background-position: 0px 0px;
                    background-image: linear-gradient(45deg, #FFFFFF00 65%, #ffffffff 100%);
                }

                .modal-content-container {
                    order: 2;
                    padding: 30px 20px;
                    text-align: left;
                    background: #fff;
                }

                .modal-content-container h2 {
                    font-size: 2.5rem;
                    margin-bottom: 15px;
                    color: #333;
                    font-weight: normal;
                }

                .modal-content-container p {
                    color: #666;
                    font-size: 16px;
                    line-height: 1.5;
                    margin-bottom: 25px;
                }

                /* Mobile: image-bottom layout */
                .modal-content-wrapper[data-mobile-layout="image-bottom"] .modal-image-container {
                    order: 2;
                }

                .modal-content-wrapper[data-mobile-layout="image-bottom"] .modal-content-container {
                    order: 1;
                }

                /* Larger mobile screens */
                @media (min-width: 576px) {
                    .modal-image-container {
                        height: 210px; /* Reduced by 10px */
                    }

                    .modal-content-container {
                        padding: 30px;
                    }
                }

                /* Desktop layouts */
                @media (min-width: 768px) {
                    /* Remove mobile gradient overlay on desktop */
                    .modal-content-wrapper[data-mobile-layout="image-top"] .modal-image-container::before {
                        display: none;
                    }

                    .modal-image-container {
                        height: 290px; /* Reduced by 10px */
                    }

                    .modal-content-container {
                        padding: 30px;
                    }

                    .modal-content-container h2 {
                        font-size: 3rem;
                        margin-bottom: 20px;
                    }

                    .modal-content-container p {
                        margin-bottom: 25px;
                    }

                    /* Two-column desktop layout */
                    .modal-content-wrapper[data-desktop-layout="two-column"] {
                        flex-direction: row;
                        overflow: hidden;
                        position: relative;
                    }

                    .modal-content-wrapper[data-desktop-layout="two-column"] .modal-image-container {
                        flex: 0 0 50%;
                        order: 1;
                        height: auto;
                        min-height: 500px;
                    }

                    .modal-content-wrapper[data-desktop-layout="two-column"] .modal-image-container img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .modal-content-wrapper[data-desktop-layout="two-column"] .modal-content-container {
                        flex: 0 0 50%;
                        order: 2;
                        padding: 30px 25px;
                        text-align: left;
                    }

                    /* One-column desktop layout */
                    .modal-content-wrapper[data-desktop-layout="one-column"] {
                        flex-direction: column;
                        position: relative;
                    }

                    .modal-content-wrapper[data-desktop-layout="one-column"] .modal-image-container {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        z-index: 0;
                        background: #666;
                        opacity: 1;
                        transition: opacity 500ms;
                    }

                    .modal-content-wrapper[data-desktop-layout="one-column"] .modal-content-container {
                        position: relative;
                        z-index: 1;
                        padding: 66px 45px;
                        text-align: center;
                        color: #fff;
                        background: transparent;
                    }

                    .modal-content-wrapper[data-desktop-layout="one-column"] .modal-content-container * {
                        color: #fff;
                    }

                    /* Hover effect for one-column layout */
                    .form-modal:hover .modal-content-wrapper[data-desktop-layout="one-column"] .modal-image-container .image:not(.hover) {
                        opacity: 0;
                    }

                    .form-modal:hover .modal-content-wrapper[data-desktop-layout="one-column"] .modal-image-container .image.hover {
                        opacity: 1;
                    }
                }

                /* Form styling to match reference images */
                .modal-content-container .gform_wrapper {
                    margin-top: 20px;
                }

                .modal-content-container .gform_wrapper .gform_required_legend {
                    display: none;
                }

                .modal-content-container .gform_wrapper .gfield_label {
                    display: inline-block;
                    font-size: 14px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 8px;
                }

                .modal-content-container .gform_wrapper .gfield_required {
                    display: none;
                }

                .modal-content-container .gform_wrapper input[type='text'],
                .modal-content-container .gform_wrapper input[type='email'] {
                    color: #000 !important;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    height: 40px;
                    margin-bottom: 15px;
                    padding: 12px 15px;
                    width: 100%;
                }

                .modal-content-container .gform_wrapper input[type='text']:focus,
                .modal-content-container .gform_wrapper input[type='email']:focus {
                    border-color: #01a59f;
                    outline: none;
                    box-shadow: 0 0 0 2px rgba(1, 165, 159, 0.1);
                }

                .modal-content-container .gform_wrapper .button {
                    background-color: #01a59f;
                    border: none;
                    border-radius: 4px;
                    color: #fff;
                    font-weight: normal;
                    padding: 12px 30px;
                    cursor: pointer;
                    margin-top: 10px;
                }

                .modal-content-container .gform_wrapper .button:hover,
                .modal-content-container .gform_wrapper .button:active,
                .modal-content-container .gform_wrapper .button:focus {
                    background-color: #008681;
                }

                /* Checkbox styling - restore working layout with proper positioning */
                .modal-content-container .gform_wrapper .ginput_container_checkbox {
                    height: 100%;
                }

                .modal-content-container .gform_wrapper .gfield_checkbox {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 15px;
                    margin-bottom: 20px;
                }

                .modal-content-container .gform_wrapper .gfield_checkbox li {
                    list-style: none;
                    margin: 0;
                    padding: 0;
                    flex: 0 0 auto;
                }

                /* .gchoice containers have the outline/border styling */
                .modal-content-container .gform_wrapper .gfield_checkbox .gchoice {
                    padding: 8px;
                    margin: 0;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background: #fff;
                    transition: all 0.2s ease;
                }

                .modal-content-container .gform_wrapper .gfield_checkbox input[type="checkbox"] {
                    display: none;
                }

                .modal-content-container .gform_wrapper .gfield_checkbox label {
                    display: inline-block;
                    padding-right: 25px; /* Only padding-right 25px */
                    cursor: pointer;
                    font-size: 12px;
                    color: #333;
                    position: relative;
                    background: none;
                    border: none;
                }

                /* Position checkbox ::before element on the right */
                .modal-content-container .gform_wrapper .gfield_checkbox label:before {
                    left: auto;
                    right: 0;
                    top: 1px;
                }

                /* Position checkbox ::after element on the right */
                .modal-content-container .gform_wrapper .gfield_checkbox label:after {
                    left: auto;
                    right: 4px;
                    top: 2px;
                }

                /* Hover effect on .gchoice container, not label */
                .modal-content-container .gform_wrapper .gfield_checkbox .gchoice:hover {
                    border-color: #01a59f;
                }

                /* Form submission error styling */
                .modal-content-container .gform_wrapper .gform_submission_error {
                    font-size: 2rem;
                    line-height: 2.5rem;
                }

                /* Paragraph font size */
                .modal-content-container p {
                    font-size: 15px;
                }

                /* Mobile spacing adjustments to fit button without scrolling */
                @media (max-width: 767px) {
                    .modal-content-container {
                        padding-top: 26px;
                        padding-bottom: 14px;
                    }

                    .modal-content-container .gform_wrapper .gfield {
                        margin-bottom: 8px; /* Reduced by 2px more */
                    }

                    .modal-content-container .gform_wrapper .gfield label {
                        margin-bottom: 0; /* Removed margin-bottom */
                    }

                    .modal-content-container .gform_wrapper .button {
                        margin-top: -10px; /* Added 2px back */
                        margin-bottom: 10px; /* Added 10px below button */
                    }

                    .modal-content-container h2 {
                        margin-top: -2px; /* Reduced by 2px */
                        margin-bottom: 15px; /* Reduce title spacing */
                    }

                    .modal-content-container p {
                        margin-bottom: 15px; /* Reduce paragraph spacing */
                    }
                }
                </style>
            `;

            // Inject the CSS
            $('head').append(modalCSS);
            $(window).on('resize', updateModalLayout);

            if (isTestMode || sessionStorage.getItem('modalWasShown') !== 'true') {
                var delay = isTestMode ? 1000 : 20000; // Show immediately in test mode (1 second delay for page load)
                console.log('🔧 Modal will show in', delay, 'ms. Test mode:', isTestMode);
                setTimeout(function() {
                    var modal = detectModal();
                    console.log('🔧 Modal detection result:', modal ? 'Found modal' : 'No modal found');
                    if (modal) {
                        if (!isTestMode) {
                            sessionStorage.setItem('modalWasShown', 'true');
                        }
                        updateModalLayout(); // Ensure correct layout before opening
                        console.log('🔧 Opening modal...');
                        openModal(modal);
                    } else {
                        console.warn('🔧 No modal found to display');
                    }
                }, delay);
            } else {
                console.log('🔧 Modal already shown or not in test mode');
            }

        });

        function detectModal() {
            // Detect whether Google Optimize has injected a div with an ID of
            // either variant-1 or variant-2 into the page
            var ab = $(".form-modal-ab");
            //form-modal-ab-variant-1
            if (ab.length) {
                return $("#modal-" + (ab.attr("id").replace('form-modal-ab-', '')));
            }
        }

        function openModal(modal) {
            console.log('🔧 Opening modal with fancybox...');

            // Check if fancybox is available
            if (typeof $.fancybox === 'undefined') {
                console.error('🔧 Fancybox not available - cannot open modal');
                return;
            }

            $.fancybox.open( modal, {
                type: "content",
                hideOnOverlayClick: false,
                hideOnContentClick: false,
                afterShow: function() {
                    console.log('🔧 Modal opened - initializing form functionality');

                    // Wait a moment for DOM to settle
                    setTimeout(function() {
                        // Check for forms in the modal (should now be only 1)
                        var modalForms = $('.fancybox-content form[id^="gform_"]');
                        console.log('🔧 Found', modalForms.length, 'forms in modal');

                        if (modalForms.length === 0) {
                            console.warn('🔧 No Gravity Forms found in modal');
                            return;
                        }

                        if (modalForms.length > 1) {
                            console.warn('🔧 Multiple forms detected - this should not happen with the fix');
                        }

                        // CRITICAL FIX: Reset all submission flags for modal forms
                        modalForms.each(function() {
                            var formId = this.id.replace('gform_', '');
                            console.log('🔧 Resetting submission flags for form:', formId);

                            // Reset the submission flag
                            window['gf_submitting_' + formId] = false;

                            // Remove any existing spinners
                            $('#gform_ajax_spinner_' + formId).remove();

                            // Clear any validation errors that might be stuck
                            $('#gform_' + formId + ' .validation_error').removeClass('validation_error');
                            $('#gform_' + formId + ' .validation_message').remove();
                        });

                        // Ensure Gravity Forms JavaScript is properly initialized
                        if (typeof gform !== 'undefined' && gform.initializeOnLoaded) {
                            console.log('🔧 Re-initializing Gravity Forms');
                            gform.initializeOnLoaded();
                        }

                        // Trigger gform_post_render for any forms in the modal
                        modalForms.each(function() {
                            var formId = this.id.replace('gform_', '');
                            console.log('🔧 Triggering gform_post_render for form:', formId);
                            $(document).trigger('gform_post_render', [formId, this]);
                        });

                        // IMMEDIATE FIX: Populate GeoIP after modal is fully shown and forms are visible
                        if (typeof populateGeoIPForVisibleForms === 'function') {
                            console.log('🔧 Triggering GeoIP population');
                            populateGeoIPForVisibleForms();
                        }

                    }, 300); // Increased delay to ensure everything is rendered
                },
                afterClose: function() {
                    console.log('🔧 Modal closed');
                    // In test mode, don't set the session storage so modal can reopen on page reload
                    if (!isTestMode) {
                        sessionStorage.setItem('modalWasShown', 'true');
                    }
                }
            } );
        }

    })(window.jQuery);
</script>
<?php endif; ?>
