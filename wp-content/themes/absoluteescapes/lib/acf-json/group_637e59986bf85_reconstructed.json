{"key": "group_637e59986bf85_reconstructed", "title": "A/B Testing - Form Modal", "fields": [{"key": "field_637e59aa6bf86", "label": "Test Group", "name": "test_group", "aria-label": "", "type": "flexible_content", "instructions": "Define the content (of a single type) to appear within the test group. Don't mix and match content types within a test group.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layouts": {"layout_637e59c899dcb": {"key": "layout_637e59c899dcb", "name": "form_modal", "label": "Form Modal", "display": "block", "sub_fields": [{"key": "field_637e60d12a313", "label": "Enabled", "name": "enabled", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_637e60d12a314", "label": "Test Mode", "name": "test_mode", "aria-label": "", "type": "true_false", "instructions": "When enabled, modal will display immediately on page load and ignore session tracking (for testing purposes)", "required": 0, "conditional_logic": [[{"field": "field_637e60d12a313", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_637e59d26bf87", "label": "Form Modals", "name": "Form Modal", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "min": 0, "max": 0, "collapsed": "field_modal_enabled_toggle", "button_label": "Add Content to Test Group", "rows_per_page": 20, "sub_fields": [{"key": "field_modal_enabled_toggle", "label": "Enabled", "name": "enabled", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No", "parent_repeater": "field_637e59d26bf87"}, {"key": "field_637e5a126bf89", "label": "Desktop Layout", "name": "desktop_layout", "aria-label": "", "type": "radio", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "37.5", "class": "", "id": ""}, "choices": {"one-column": "Single column (image as background)", "two-column": "Two columns (image on left)"}, "default_value": "two-column", "return_format": "value", "allow_null": 0, "other_choice": 0, "layout": "vertical", "save_other_choice": 0, "parent_repeater": "field_637e59d26bf87"}, {"key": "field_637e5a126bf90", "label": "Mobile Layout", "name": "mobile_layout", "aria-label": "", "type": "radio", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "37.5", "class": "", "id": ""}, "choices": {"image-top": "Image on top", "image-bottom": "Image on bottom"}, "default_value": "image-top", "return_format": "value", "allow_null": 0, "other_choice": 0, "layout": "vertical", "save_other_choice": 0, "parent_repeater": "field_637e59d26bf87"}, {"key": "field_637e59ff6bf88", "label": "Content", "name": "content", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0, "parent_repeater": "field_637e59d26bf87"}, {"key": "field_637e5a776bf8a", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "parent_repeater": "field_637e59d26bf87"}, {"key": "field_637f8e9abf1ed", "label": "Hover image", "name": "hover_image", "aria-label": "", "type": "image", "instructions": "Image that appears when modal is hovered. Only applies to the one column layout.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "parent_repeater": "field_637e59d26bf87"}, {"key": "field_637e5a9a6bf8b", "label": "Form", "name": "gravity_forms", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "multiple": 0, "allow_null": 0, "choices": {"5": "Make an Enquiry - General", "2": "Make an Enquiry - Specific Holiday", "1": "Newsletter - <PERSON><PERSON> Signup", "9": "Newsletter - Loyalty Club", "7": "Newsletter - Website Pop-Up", "8": "Newsletter modal form B (switched off in June 2023)", "3": "Payment Form", "11": "Test Form", "12": "Test Payment Form"}, "default_value": false, "ui": 0, "ajax": 0, "placeholder": "", "return_format": "value", "create_options": 0, "save_options": 0, "parent_repeater": "field_637e59d26bf87"}]}], "min": "", "max": ""}}, "button_label": "Add Content", "min": "", "max": ""}], "location": [[{"param": "options_page", "operator": "==", "value": "theme-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "Reconstructed Test Group field for Form Modal popup functionality", "show_in_rest": 0, "modified": 1755760427}