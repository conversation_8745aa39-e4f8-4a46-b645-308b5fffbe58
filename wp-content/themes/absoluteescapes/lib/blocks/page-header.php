<?php

/**
 * Page Header
 */

$heading = get_sub_field('heading');
$subheading = get_sub_field('subheading');
$image_field = (!empty(get_sub_field('image'))) ? get_sub_field('image') : get_post_thumbnail_id((get_the_ID()));
$background = wp_get_attachment_image_src($image_field, 'full');
$background_low = wp_get_attachment_image_src($image_field, 'small');
$image_alt = ''; // get_post_meta($image_field, '_wp_attachment_image_alt', true);
$links = 'links';
$options = '';
$type = '';

// write_log([
//     'image_field'=> $image_field,
//     'background'=> $background,
//     'image_alt'=> $image_alt,
// ]);
$type .= 'default,';

if (is_home() || is_category()) {
    $heading = get_field('blog_heading', 'options');
    $subheading = get_field('blog_subheading', 'options');
    $image_field = get_field('blog_background', 'options');
    $background = wp_get_attachment_image_src($image_field, 'full');
    $background_low = wp_get_attachment_image_src($image_field, 'small');
    $image_alt = get_post_meta(get_sub_field('blog_background'), '_wp_attachment_image_alt', true);
    $type .= 'home/cat,';
}

$page_obj = get_queried_object();


if (is_post_type_archive('holiday')) {

    $background = wp_get_attachment_image_src(get_field('holiday_archive_background', 'options'), 'full');
    $background_low = wp_get_attachment_image_src(get_field('holiday_archive_background', 'options'), 'small');
    $image_field = get_field('holiday_archive_background', 'options');
    $image_alt = get_post_meta(get_sub_field('holiday_archive_background'), '_wp_attachment_image_alt', true);
    $type = 'holiday-archive,';

}

if (is_404()) {
    $heading = get_field('404_heading', 'options');
    $copy = get_field('404_copy', 'options');
    $links = '404_links';
    $options = 'options';
    $image_field = get_field('404_background', 'options');
    $background = wp_get_attachment_image_src($image_field, 'full');
    $background_low = wp_get_attachment_image_src($image_field, 'small');
    $image_alt = get_post_meta(get_sub_field('404_background'), '_wp_attachment_image_alt', true);
    $type .= '404,';
}

if (isset($no_content)) {
    if ($no_content === true) {

        $heading = get_field('no_content_heading', 'options');
        $copy = get_field('no_content_copy', 'options');
        $links = 'no_content_links';
        $options = 'options';
        $image_field = get_field('no_content_background', 'options');
        $background = wp_get_attachment_image_src($image_field, 'full');
        $background_low = wp_get_attachment_image_src($image_field, 'small');
        $image_alt = get_post_meta(get_sub_field('no_content_background'), '_wp_attachment_image_alt', true);
        $type .= 'noncontent,';
    }
} else {
    $no_content = false;
}

$tax_image = '';
if (is_tax('holiday-regions')) {
    $tax_image = 'region_image';
    $image_field = get_field($tax_image, $page_obj);
    $region_image = wp_get_attachment_image_src($image_field, 'full');
    $region_image_low = wp_get_attachment_image_src($image_field, 'small');
    $hero_image = ($region_image[0]) ? $region_image[0]: get_the_post_thumbnail_url(get_the_ID(), 'full');
    $hero_image_low = ($region_image_low[0]) ? $region_image_low[0]: get_the_post_thumbnail_url(get_the_ID(), 'small');
    $hero_image_field = ($image_field) ? $image_field: get_post_thumbnail_id(get_the_ID());
    $type .= 'hol-regions,';
} else
if (is_tax('holiday-type')) {
    $tax_image = 'holiday_type_image';
    $image_field = get_field($tax_image, $page_obj);
    $region_image = wp_get_attachment_image_src($image_field, 'full');
    $region_image_low = wp_get_attachment_image_src($image_field, 'small');
    $hero_image = ($region_image[0]) ? $region_image[0]: get_the_post_thumbnail_url(get_the_ID(), 'full');
    $hero_image_low = ($region_image_low[0]) ? $region_image_low[0]: get_the_post_thumbnail_url(get_the_ID(), 'small');
    $hero_image_field = ($image_field) ? $image_field: get_post_thumbnail_id(get_the_ID());
    $type .= 'holiday-type,';
} else {
    // write_log(['background'=> $background, 'thumbnail' => get_the_post_thumbnail_url(get_the_ID(), 'full')]);
    $hero_image = ($background[0]) ? $background[0] : get_the_post_thumbnail_url(get_the_ID(), 'full'); 
    $hero_image_low = ($background_low[0]) ? $background_low[0]: get_the_post_thumbnail_url(get_the_ID(), 'small');
    $hero_image_field = ($image_field) ? $image_field: get_post_thumbnail_id(get_the_ID());
    $type .= 'fallback,';
}
if (is_singular('holiday')) {
    $image_alt = get_post_meta(get_post_thumbnail_id(), '_wp_attachment_image_alt', true);
    $type .= 'holiday,';
} 

// write_log($type);
?>
<link rel="preload" fetchpriority="high" as="image" href="<?php echo $hero_image_low ?>">
<link rel="preload" fetchpriority="high" as="image" href="/wp-content/themes/absoluteescapes/dist/img/banner-mask.svg">
<section
        class="page-header <?php if (is_tax('holiday-regions') || is_tax('holiday-type') || is_post_type_archive('holiday') || is_page()) : ?>page-header--taxonomy<?php endif; ?> <?php if (is_page()): ?>page-header--page<?php endif; ?>">
        
        <?php echo outputSrcset($image_field,'.page-header__background'); ?>

    <div class="page-header__background">
    <style>
            [data-ae="fade"] {
                opacity: 0;
                animation: fadeIn .5s ease-in-out forwards;
            }
            @keyframes fadeIn {
                to {
                    opacity: 1;
                }
            }
            .svg-inline--fa { display: inline-block;height: 1em;vertical-align: -.125em;}
            .svg-inline--fa.fa-w-16 { width: 1em; }
            svg:not(:root).svg-inline--fa { overflow: visible; }
            .button--icon svg { font-size: 1.8rem;margin-left: 0;margin-right: 10px; }


        </style>
        <?php if ($image_alt) : ?><span class="page-header__alt"><?php echo $image_alt; ?></span><?php endif; ?>
        <div class="page-header__inner" data-ae="fade">
            <div class="container page-header__container">
                <div class="page-header__content centre">

                    <?php if (is_tax('holiday-regions') || is_tax('holiday-type')) : ?>
                        <h1 class="page-header__heading text-white"><?php echo $page_obj->name; ?></h1>
                    <?php elseif (is_post_type_archive('holiday')) : ?>
                        <h1 class="page-header__heading text-white"><?php _e('All Holidays', 'absoluteescapes'); ?></h1>
                    <?php else : ?>
                        <h1 class="page-header__heading text-white"><?php echo ($heading) ? $heading : get_the_title(); ?></h1>
                    <?php endif; ?>

                    <?php if ($subheading) : ?>
                        <h3 class="page-header__subheading text-italic text-white"><?php echo $subheading; ?></h3>
                    <?php endif; ?>

                    <?php if (is_home() || is_category()) : ?>
                        <div class="page-header__button-wrapper">
                            <a href="#"
                               class="page-header__button button scroll-next"><?php _e('Latest articles', 'absoluteescapes'); ?>
                               <svg class="svg-inline--fa fa-chevron-down fa-w-14" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="chevron-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"></path></svg></a>
                        </div>
                    <?php endif; ?>

                    <?php if (is_tax('holiday-regions') || is_tax('holiday-type')) : ?>
                        <div class="page-header__button-wrapper">
                            <a href="#"
                               class="page-header__button button scroll-to" data-target="holidaysResults"><?php _e('View Holidays', 'absoluteescapes'); ?>
                                <svg class="svg-inline--fa fa-chevron-double-down fa-w-14" aria-hidden="true" focusable="false" data-prefix="fal" data-icon="chevron-double-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M443.5 98.5l-211 211.1c-4.7 4.7-12.3 4.7-17 0L4.5 98.5c-4.7-4.7-4.7-12.3 0-17l7.1-7.1c4.7-4.7 12.3-4.7 17 0L224 269.9 419.5 74.5c4.7-4.7 12.3-4.7 17 0l7.1 7.1c4.6 4.6 4.6 12.2-.1 16.9zm0 111l-7.1-7.1c-4.7-4.7-12.3-4.7-17 0L224 397.9 28.5 202.5c-4.7-4.7-12.3-4.7-17 0l-7.1 7.1c-4.7 4.7-4.7 12.3 0 17l211 211.1c4.7 4.7 12.3 4.7 17 0l211-211.1c4.8-4.8 4.8-12.4.1-17.1z"></path></svg></a>
                        </div>
                    <?php endif; ?>

                    <?php if (is_singular('holiday')) : ?>
                        <div class="page-header__buttons font-zero">
                            <?php $images_count = count(get_field('holiday_gallery')); ?>
                            <?php if (have_rows('holiday_gallery')) : $i = 0; ?>
                                <div class="page-header__button-wrapper inline-block vmiddle">


                                    <?php $i = 1;
                                    while (have_rows('holiday_gallery')) : the_row(); ?>
                                        <?php
                                        $image = wp_get_attachment_image_url(get_sub_field('image'), 'full');
                                        $alt = get_post_meta(get_sub_field('image'), '_wp_attachment_image_alt', true);
                                        ?>

                                        <?php if ($image && $i === 1) : ?>
                                            <a data-fancybox="headerGallery" href="<?php echo $image; ?>"
                                               data-caption="<?php echo $alt; ?>"
                                               class="page-header__button button button--hollow button--icon">
                                               <svg class="svg-inline--fa fa-camera fa-w-16" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="camera" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M512 144v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V144c0-26.5 21.5-48 48-48h88l12.3-32.9c7-18.7 24.9-31.1 44.9-31.1h125.5c20 0 37.9 12.4 44.9 31.1L376 96h88c26.5 0 48 21.5 48 48zM376 288c0-66.2-53.8-120-120-120s-120 53.8-120 120 53.8 120 120 120 120-53.8 120-120zm-32 0c0 48.5-39.5 88-88 88s-88-39.5-88-88 39.5-88 88-88 88 39.5 88 88z"></path></svg><?php _e('View Photos', 'absoluteescapes'); ?>
                                            </a>
                                        <?php endif; ?>

                                        <?php $i++; endwhile; ?>


                                    <div class="page-header__gallery none">
                                        <?php $i = 1;
                                        while (have_rows('holiday_gallery')) : the_row(); ?>
                                            <?php
                                            $image = wp_get_attachment_image_url(get_sub_field('image'), 'full');
                                            $alt = get_post_meta(get_sub_field('image'), '_wp_attachment_image_alt', true);
                                            ?>

                                            <?php if ($image && $i > 1) : ?>
                                                <a href="<?php echo $image; ?>" data-fancybox="headerGallery"
                                                   data-caption="<?php echo $alt; ?>"></a>
                                            <?php endif; ?>

                                            <?php $i++; endwhile; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="page-header__button-wrapper inline-block vmiddle">
                                <?php $holiday_header_button = get_field('holiday_header_button'); ?>
                                <?php if ($holiday_header_button) : ?>
                                    <a href="<?= $holiday_header_button['url']; ?>" class="page-header__button button button--hollow button--icon">
                                        <?= $holiday_header_button['title']; ?>
                                    </a>
                                <?php else : ?>
                                    <a href="#route" class="page-header__button button button--hollow button--icon">
                                    <svg class="svg-inline--fa fa-map-marker-alt fa-w-12" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="map-marker-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"></path></svg><?php _e('View Map', 'absoluteescapes'); ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (have_rows($links, $options)) : ?>
                        <div class="page-header__buttons font-zero">

                            <?php while (have_rows($links, $options)) : the_row(); ?>

                                <?php

                                $link = get_sub_field('link');
                                $link_type = seoUrl(get_sub_field('link_type'));

                                ?>

                                <?php if ($link_type === 'gallery') : ?>
                                    <div class="page-header__button-wrapper inline-block vmiddle">
                                        <a id="galleryTrigger" href="#"
                                           class="page-header__button button button--hollow button--icon" >
                                           <svg class="svg-inline--fa fa-camera fa-w-16" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="camera" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M512 144v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V144c0-26.5 21.5-48 48-48h88l12.3-32.9c7-18.7 24.9-31.1 44.9-31.1h125.5c20 0 37.9 12.4 44.9 31.1L376 96h88c26.5 0 48 21.5 48 48zM376 288c0-66.2-53.8-120-120-120s-120 53.8-120 120 53.8 120 120 120 120-53.8 120-120zm-32 0c0 48.5-39.5 88-88 88s-88-39.5-88-88 39.5-88 88-88 88 39.5 88 88z"></path></svg><?php _e('View Photos', 'absoluteescapes'); ?>
                                        </a>
                                        <?php if (have_rows('images')) : ?>
                                            <div class="page-header__gallery none">
                                                <?php while (have_rows('images')) : the_row(); ?>
                                                    <?php

                                                    $image = wp_get_attachment_image(get_sub_field('image'), 'full', false, array('data-skip-lazy'));
                                                    ?>

                                                    <?php if ($image) : ?>
                                                        <?= $image; ?>
                                                    <?php endif; ?>

                                                <?php endwhile; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php elseif ($link_type === 'map') : ?>

                                    <div class="page-header__button-wrapper inline-block vmiddle">
                                        <a href="<?php echo $link['url']; ?>"
                                           class="page-header__button button button--hollow button--icon">
                                           <svg class="svg-inline--fa fa-map-marker-alt fa-w-12" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="map-marker-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"></path></svg><?php echo $link['title']; ?>
                                        </a>
                                    </div>

                                <?php else : ?>
                                    <div class="page-header__button-wrapper inline-block vmiddle">
                                        <a href="<?php echo $link['url']; ?>"
                                           class="page-header__button button button--hollow" <?php echo ($link['target']) ? 'target="_blank"' : ''; ?>>
                                            <?php echo $link['title']; ?>
                                        </a>
                                    </div>

                                <?php endif; ?>
                            <?php endwhile; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
